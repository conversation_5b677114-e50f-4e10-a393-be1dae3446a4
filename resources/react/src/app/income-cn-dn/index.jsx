import moment from "moment";
import { useContext, useEffect, useRef, useState } from "react";
import { Container } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import Toast from "../../components/ui/Toast";
import { apiBaseURL, CALCULATION_ON_TYPE, SHIPPING_ADDRESS_TYPE_LIST, TABLE_HEADER_TYPE, toastType, TRANSACTION_TYPE } from "../../constants";
import { StateContext } from "../../context/StateContext";
import {
    calculateAdditionalCharges,
    calculateAdditionalClassification,
    calculateAddLessCharges,
    calculateClassification,
    calculateTotals,
    customToFixed,
    gstCalculate,
    RoundOffMethod,
    RoundOffMethodForTds,
} from "../../shared/calculation";
import useDropdownOption from "../../shared/dropdownList";
import CustomHelmet from "../../shared/helmet";
import Loader from "../../shared/loader";
import {
    areAllProductsExemptOrNA,
    calculateGSTFlags,
    checkAllAdditionalChargesNaAndExempt,
    convertToFormData,
    fetchOriInvoiceData,
    findGstRate,
    prepareIncomeData,
    prepareItemsData,
    prepareLedgerData,
    prepareSaveAndNewData,
} from "../../shared/prepareData";
import { correctClassificationNatureType, getEditLedgerFromList } from "../../shared/sharedFunction";
import { useTransactionShortcuts } from "../../shared/shortcut-keys";
import { errorToast } from "../../store/actions/toastAction";
import { getAdvancePayment } from "../../store/advance-payment/advancePaymentSlice";
import { fetchBrokerList } from "../../store/broker/brokerSlice";
import { fetchClassificationList } from "../../store/classification/classificationSlice";
import { fetchCompanyDetails, fetchUserPermission } from "../../store/company/companySlice";
import { fetchBankList, fetchConfigurationList, rearrangeItemList, resetConfiguration } from "../../store/configuration/configurationSlice";
import { fetchDispatchAddressList } from "../../store/dispatchAddress/dispatchAddressSlice";
import { addIncome, deleteCnDn, updateIncome } from "../../store/income-cn-dn/incomeSlice";
import { fetchIncomeInvoice } from "../../store/invoice/invoiceSlice";
import { fetchItemList } from "../../store/item/itemSlice";
import {
    fetchAdditionalLedgerList,
    fetchAddlessLedgerList,
    fetchItemLedgerDetail,
    fetchPartyDetail,
    fetchPartyList,
    fetchPaymentLedgerList,
    fetchPaymentModeList
} from "../../store/ledger/ledgerSlice";
import { fetchPrevNextUrl } from "../../store/prev-next/prev-nextSlice";
import { fetchTcsList, fetchTdsList } from "../../store/rate/rateSlice";
import { checkCreditLimit, createEInvoice } from "../../store/sale/saleSlice";
import { fetchShippingAddressList } from "../../store/shippingAddress/shippingAddressSlice";
import { fetchTransportList } from "../../store/transport/transportSlice";
import Error404Page from "../common/404";
import SaleItems from "../common/Items";
import SaleReturnInvoiceDetail from "../common/Return-InvoiceDetail";
import WarningModal from "../common/WarningModal";
import ConfigurationModal from "../modal/Configuration/ConfigurationModal";
import DeleteWarningModal from "../modal/DeleteWarningModal";

const IncomeTransaction = ({ id, singleSale, isDuplicateDebitNote = false }) => {
    const formRef = useRef(null);
    const IncomeCreateOrDebit = window.location.pathname.includes("credit");
    const isIncomeCredit = window.location.pathname.includes("income-credit-notes");
    const url = window.location.origin;
    const isInWord = IncomeCreateOrDebit ? true : false;
    const incomeEdit = window.location.pathname.includes("/edit");
    const shippingAddressType = IncomeCreateOrDebit ? SHIPPING_ADDRESS_TYPE_LIST.INCOME_CREDIT_NOTE : SHIPPING_ADDRESS_TYPE_LIST.INCOME_DEBIT_NOTE

    const { roundOffOption, gstOptions, classificationOptions, tableHeaderList, accountingTableHeader } = useDropdownOption();
    const {
        items,
        setItems,
        accountingItems,
        setAccountingItems,
        gstValue,
        setGstValue,
        invoiceDetail,
        setInvoiceDetail,
        partyAddress,
        setPartyAddress,
        ewayBillDetail,
        setEwayBillDetail,
        otherDetail,
        setOtherDetail,
        gstCalculation,
        setGstCalculation,
        paymentLedgerDetail,
        setPaymentLedgerDetail,
        classification,
        setClassification,
        additionalGst,
        setAdditionalGst,
        brokerDetail,
        setBrokerDetail,
        transporterDetail,
        setTransporterDetail,
        localDispatchAddress,
        setLocalDispatchAddress,
        gstQuote,
        setGstQuote,
        additionalCharges,
        setAdditionalCharges,
        tcsRate,
        setTcsRate,
        addLessChanges,
        setAddLessChanges,
        cessValue,
        selectedAddress,
        sameAsBill,
        setSameAsBill,
        itemType,
        setItemType,
        setChangeTax,
        grandTotal,
        setGrandTotal,
        mainGrandTotal,
        setMainGrandTotal,
        setCessValue,
        isEditCalculation,
        setIsEditCalculation,
        finalAmount,
        setFinalAmount,
        setConfigurationModalName,
        setConfigurationURL,
        setConfigurationHeaderList,
        setConfigurationTableList,
        setConfigurationFooterList,
        isIGSTCalculation,
        isSGSTCalculation,
        setIsSGSTCalculation,
        setIsIGSTCalculation,
        taxableValue,
        setTaxableValue,
        loader,
        setLoader,
        dispatchAddressId,
        setDispatchAddressId,
        changeTax,
        setInvoiceValue,
        isTcsAmountChange,
        classificationType,
        deleteTransaction,
        openDeleteTransactionModel,
        closeDeleteTransactionModel,
        showDeleteWarningModel,
        setShowDeleteWarningModel,
        isCheckGstType,
        selectedAdvancePayment,
        setSelectedAdvancePayment,
        setShowPaymentTable,
        isDisable,
        setIsDisable,
        customFieldListTransaction,
        setCustomHeaderListTransaction,
        setHasUnsavedChanges,
        isFieldsChanges,
        setisFieldsChanges,
        isBackButtonClick,
        setIsBackButtonClick,
        setUnsavedBackUrl,
        setIsChangePartyId
    } = useContext(StateContext);

    useEffect(() => {
        document.getElementById("showName").innerHTML = IncomeCreateOrDebit
            ? incomeEdit
                ? "Edit Credit Notes"
                : "Add Credit Notes"
            : incomeEdit
                ? "Edit Debit Notes"
                : "Add Debit Notes";

        if (!incomeEdit && !isDuplicateDebitNote) {
            setIsEditCalculation(true);
        }

        setConfigurationModalName(
            IncomeCreateOrDebit ? "Credit Note Configuration" : "Debit Note Configuration",
        );
    }, [IncomeCreateOrDebit]);

    const dispatch = useDispatch();
    const incomeCnDnRef = useRef(null);
    const {
        company,
        invoice,
        ledger,
        table,
        dispatchAddress,
        configuration,
        sale,
        prevNext,
        broker,
        item,
        incomeNote
    } = useSelector(selector => selector);

    const fetchSaleDetail = sale?.getSaleById?.length !== 0 && sale?.getSaleById;

    const configurationList = configuration?.configuration;
    const [isShowGstValue, setIsShowGstValue] = useState(false);
    const [tcsValue, setTCSValue] = useState(0);
    const [tcsRateValue, setTCSRateValue] = useState(0);
    const [shippingValue, setShippingValue] = useState(0);
    const [packingCharge, setPackingCharge] = useState(0);
    const [updatedTableHeader, setUpdatedTableHeader] = useState([]);
    const [submitTypeLocal, setSubmitTypeLocal] = useState("");
    const [creditLimitModal, setCreditLimitModal] = useState({
        open: false,
        action: null,
    });

     useEffect(() => {
        if (!additionalCharges.terms_and_conditions?.length > 0 && !id) {
            setAdditionalCharges(prev => ({
                ...prev,
                terms_and_conditions: invoice?.incomeInvoice?.term_and_condition,
            }));
        }
        if (!id || !isDuplicateDebitNote) {
            setAdditionalCharges(prev => ({
                ...prev,
                bank_id: configurationList?.document_prefix?.bank_id
            }));
        }
        }, [invoice, configurationList?.document_prefix?.bank_id]);

    useEffect(() => {
        if (
            !configurationList?.header?.is_change_gst_details &&
            // !classification?.classification_nature_name &&
            localDispatchAddress &&
            company?.company?.is_gst_applicable && ((!id && !isDuplicateDebitNote && !gstQuote?.original_inv_no) || isCheckGstType)
        ) {
            if (
                partyAddress?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id
            ) {
                setIsSGSTCalculation(true);
                setIsIGSTCalculation(false);
            } else {
                setIsSGSTCalculation(false);
                setIsIGSTCalculation(true);
            }
        }
    }, [
        ledger?.partyDetail?.billingAddress?.state_id,
        localDispatchAddress,
        gstQuote?.original_inv_no,
        isCheckGstType,
        partyAddress?.billingAddress
    ]);

     useEffect(() => {
            const newCnDnData = JSON.parse(localStorage.getItem('saveAndNewData'));

            if (newCnDnData) {
                setIsChangePartyId(true);
                dispatch(fetchPartyDetail(newCnDnData?.customer_ledger_id));
                dispatch(fetchShippingAddressList(parseFloat(newCnDnData?.customer_ledger_id), shippingAddressType, id));
                if (newCnDnData?.customer_ledger_id) {
                    dispatch(fetchPartyList({ ids: [newCnDnData?.customer_ledger_id] }));
                } else {
                    dispatch(fetchPartyList());
                }

                const newData = newCnDnData;
                prepareSaveAndNewData(newData, setGstQuote, setPartyAddress, additionalCharges, setAdditionalCharges, setItemType);
            }

        }, []);

    useEffect(() => {
        if (singleSale) {
            const cn_dn = IncomeCreateOrDebit ? "cn" : "dn";
            dispatch(fetchPartyDetail(singleSale?.customer_ledger_id));
            if (singleSale?.customer_ledger_id) {
                dispatch(fetchPartyList({ ids: [singleSale?.customer_ledger_id] }));
            } else {
                dispatch(fetchPartyList());
            }
            const ids = [];
            const item_ledger_id = [];
            const tcs_type = 1;
            const tds_type = 2;
            const is_call_payment_ledger = true;

            if (singleSale?.income_debit_note_items) {
                singleSale?.income_debit_note_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            } else if (singleSale?.income_credit_note_items) {
                singleSale?.income_credit_note_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            } else if (singleSale?.income_credit_note_ledgers) {
                singleSale?.income_credit_note_ledgers?.forEach(item => {
                    item_ledger_id.push(item.ledger_id);
                });
            }else if (singleSale?.income_debit_note_ledgers) {
                singleSale?.income_debit_note_ledgers?.forEach(item => {
                    item_ledger_id.push(item.ledger_id);
                });
            }

            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
            } else {
                dispatch(fetchItemList());
            }
            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: singleSale});
            if (singleSale?.advance_payment?.length > 0) {
                setShowPaymentTable(true);
                dispatch(getAdvancePayment(singleSale?.customer_ledger_id || singleSale?.party_ledger_id, IncomeCreateOrDebit ? "income-credit-note" : "income-debit-note", id));
            }
            prepareIncomeData({
                singleSale,
                id,
                cn_dn,
                setStateFunctions: {
                    invoiceDetail,
                    setInvoiceDetail,
                    setCessValue,
                    setGstQuote,
                    setItemType,
                    setClassification,
                    setItems,
                    setAccountingItems,
                    setAddLessChanges,
                    setAdditionalCharges,
                    setPartyAddress,
                    gstCalculation,
                    setGstCalculation,
                    setBrokerDetail,
                    setTransporterDetail,
                    setEwayBillDetail,
                    setOtherDetail,
                    setGstValue,
                    setTcsRate,
                    setPaymentLedgerDetail,
                    setGrandTotal,
                    setMainGrandTotal,
                    setChangeTax,
                    addLessChanges,
                    additionalCharges,
                    setFinalAmount,
                    setIsIGSTCalculation,
                    setIsSGSTCalculation,
                    setTaxableValue,
                    localDispatchAddress,
                    setLocalDispatchAddress,
                    setDispatchAddressId,
                    setSelectedAdvancePayment,
                    setCustomHeaderListTransaction,
                    setSameAsBill,
                    incomeEdit
                },
                dispatch,
                tableHeaderList,
                classificationOptions,
                accountingTableHeader,
                isDuplicateDebitNote,
            });
        }
    }, [singleSale, classificationOptions]);

    useEffect(() => {
        if (fetchSaleDetail) {
            const ids = [];
            const item_ledger_id = [];
            const tcs_type = 1;
            const tds_type = 2;
            const is_call_payment_ledger = true;
            if (fetchSaleDetail?.sale_items) {
                fetchSaleDetail?.sale_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            }

            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
            } else {
                dispatch(fetchItemList());
            }
            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: fetchSaleDetail});
            fetchOriInvoiceData({
                fetchSaleDetail,
                IncomeCreateOrDebit,
                setStateFunctions: {
                    invoiceDetail,
                    setInvoiceDetail,
                    gstQuote,
                    setGstQuote,
                    setItemType,
                    setClassification,
                    setItems,
                    setAccountingItems,
                    setAddLessChanges,
                    setAdditionalCharges,
                    setPartyAddress,
                    setGstCalculation,
                    setBrokerDetail,
                    setTransporterDetail,
                    setEwayBillDetail,
                    setOtherDetail,
                    setGstValue,
                    setTcsRate,
                    setPaymentLedgerDetail,
                    setGrandTotal,
                    setMainGrandTotal,
                    setChangeTax,
                    setFinalAmount,
                    setIsIGSTCalculation,
                    setIsSGSTCalculation,
                    setTaxableValue,
                    setCessValue,
                    setCustomHeaderListTransaction,
                    setSameAsBill
                },
                dispatch,
                tableHeaderList,
                accountingTableHeader,
                addLessChanges,
                additionalCharges,
                classificationOptions,
            });
        }
    }, [fetchSaleDetail, classificationOptions]);


    useEffect(() => {
        dispatch(resetConfiguration());
    }, []);

    useEffect(() => {
        setTimeout(() => {
            if (configurationList) {
                setLoader(false);
            }
        }, 300)
    }, [configurationList])

    useEffect(() => {
        setLoader(true);
        dispatch(
            fetchConfigurationList(
                IncomeCreateOrDebit
                    ? apiBaseURL.INCOME_CREDIT_NOTE_CONFIGURATION
                    : apiBaseURL.INCOME_DEBIT_NOTE_CONFIGURATION,
            ),
        );
        setConfigurationURL(
            IncomeCreateOrDebit
                ? apiBaseURL.INCOME_CREDIT_NOTE_CONFIGURATION
                : apiBaseURL.INCOME_DEBIT_NOTE_CONFIGURATION,
        );
        dispatch(fetchCompanyDetails());
        dispatch(fetchUserPermission());
        dispatch(fetchIncomeInvoice(IncomeCreateOrDebit));
        dispatch(fetchDispatchAddressList());

        setTimeout(() => {
            if (!incomeEdit) {
                dispatch(fetchBankList());
                dispatch(fetchItemList());
                dispatch(fetchPartyList());
                dispatch(fetchAddlessLedgerList());
                dispatch(fetchTcsList({id:1}));
                dispatch(fetchTdsList({id:2}));
                dispatch(fetchAdditionalLedgerList());
                dispatch(fetchPaymentLedgerList());
            }
            dispatch(fetchItemLedgerDetail());
            dispatch(
                fetchPrevNextUrl(IncomeCreateOrDebit ? { type: "7", id: id } : { type: "6", id: id }),
            );
            dispatch(fetchBrokerList());
            dispatch(fetchTransportList());
            dispatch(fetchClassificationList());
            dispatch(fetchPaymentModeList(isIncomeCredit ? 2 : 1 ));
        }, 1500)
    }, [dispatch]);

    useEffect(() => {
        if (!configurationList?.header?.is_change_gst_details && company?.company?.is_gst_applicable && isCheckGstType) {
            setClassification({
                rcm_applicable: false,
                classification_nature_name: '',
                classification_nature: 0
            })
            if (
                partyAddress?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id
            ) {
                setIsSGSTCalculation(true);
                setIsIGSTCalculation(false);
            } else {
                setIsSGSTCalculation(false);
                setIsIGSTCalculation(true);
            }
        }
    }, [configurationList?.header?.is_change_gst_details])


    useEffect(() => {
        setLocalDispatchAddress(dispatchAddress?.dispatchAddress);
    }, [dispatchAddress?.dispatchAddress]);


    const [showEInvoiceModal, setShowEInvoiceModal] = useState({
        open: false,
        id: null,
    });

    const onSuccessInvoice = () => {
        if (IncomeCreateOrDebit) {
            if (submitTypeLocal === "duplicate" || submitTypeLocal === "saveAndNew") {
                window.location.href = `${window.location.origin}/company/income-credit-notes/create`;
            } else {
                window.location.href = `${window.location.origin}/company/income-credit-notes`;
            }
        } else {
            if (submitTypeLocal === "duplicate" || submitTypeLocal === "saveAndNew") {
                window.location.href = `${window.location.origin}/company/income-debit-notes/create`;
            } else {
                window.location.href = `${window.location.origin}/company/income-debit-notes`;
            }
        }
    }

    const handleEInvoice = () => {
        setShowEInvoiceModal({
            open: false,
            id: showEInvoiceModal.id,
        });
        setLoader(true);
        dispatch(
            createEInvoice(
                showEInvoiceModal.id,
                IncomeCreateOrDebit ? "Income Credit Note" : "Income Debit Note",
                setLoader,
                onSuccessInvoice,
                setIsDisable
            ),
        );
        setIsDisable(false);
    };

    useEffect(() => {
        setConfigurationHeaderList(
            [
                {
                    name: "Dispatch Details",
                    key: "is_enabled_dispatch_details",
                    value: configurationList?.header?.is_enabled_dispatch_details,
                    position: 0,
                },
                {
                    name: "Mobile Number",
                    key: "is_enabled_phone_number",
                    value: configurationList?.header?.is_enabled_phone_number,
                    position: 1,
                },
                // !IncomeCreateOrDebit &&
                //     company?.company?.is_gst_applicable && {
                //         name: "E-Way Bill Details",
                //         key: "is_enabled_eway_details",
                //         value: configurationList?.header?.is_enabled_eway_details,
                //         position: 7,
                //     },
                {
                    name: "Credit Period Details",
                    key: "is_enabled_credit_period_details",
                    value: configurationList?.header?.is_enabled_credit_period_details,
                    position: 9,
                },
                {
                    name: "PO Details",
                    key: "is_enabled_po_details_of_buyer",
                    value: configurationList?.header?.is_enabled_po_details_of_buyer,
                    position: 8,
                },
            ].filter(Boolean),
        );

        setConfigurationTableList(
            [
                company?.company?.is_gst_applicable && {
                    name: "Change GST Details",
                    key: "is_change_gst_details",
                    value: configurationList?.header?.is_change_gst_details,
                    position: 1,
                },
                {
                    name: "Additional Ledger Description",
                    key: "is_additional_ledger_description",
                    value: configurationList?.item_table_configuration
                        ?.is_additional_ledger_description,
                    position: 3,
                },
                !IncomeCreateOrDebit && {
                    name: "Warn On Negative Stock",
                    key: "warn_on_negative_stock",
                    value: configurationList?.item_table_configuration?.warn_on_negative_stock,
                    position: 8,
                },
                {
                    name: "Discount 2",
                    key: "is_enabled_discount_2",
                    value: configurationList?.item_table_configuration?.is_enabled_discount_2,
                    position: 7,
                },
                {
                    name: "MRP",
                    key: "is_enabled_mrp",
                    value: configurationList?.item_table_configuration?.is_enabled_mrp,
                    position: 6,
                },
            ].filter(Boolean),
        );

        setConfigurationFooterList(
            [
                {
                    name: "TCS",
                    key: "is_enabled_tcs_details",
                    value: configurationList?.footer?.is_enabled_tcs_details,
                    position: 1,
                },
                {
                    name: "TDS",
                    key: "is_enabled_tds_details",
                    value: configurationList?.footer?.is_enabled_tds_details,
                    position: 2,
                },
                {
                    name: "Payment Details",
                    key: "is_enabled_payment_details",
                    value: configurationList?.footer?.is_enabled_payment_details,
                    position: 7,
                },
                {
                    name: "Terms & Conditions",
                    key: "is_enabled_terms_and_conditions",
                    value: configurationList?.footer?.is_enabled_terms_and_conditions,
                    position: 4,
                },
            ].filter(Boolean),
        );
    }, [configurationList, company]);

       useEffect(() => {
            if (isEditCalculation) {
                const { itemTotal, cessTotal } = calculateTotals(
                    itemType === "accounting" ? accountingItems : items,
                    classificationType
                );
                setGrandTotal(itemTotal);
                const addition_charges = calculateAdditionalCharges(
                    additionalCharges,
                    itemTotal,
                    isIGSTCalculation
                );
                setTaxableValue(parseFloat(addition_charges?.addition_charges) + itemTotal);
                // setCessValue(cessTotal);
                const newInvoiceValue = isIGSTCalculation
                    ? parseFloat(taxableValue || 0) + parseFloat(cessValue || 0) + parseFloat(gstValue?.igstValue || 0) + parseFloat(additionalGst || 0)
                    : parseFloat(taxableValue || 0) + parseFloat(cessValue || 0) + parseFloat(gstValue?.sgstValue || 0) + parseFloat(additionalGst || 0) + parseFloat(additionalGst || 0) + parseFloat(gstValue?.cgstValue || 0);
                setInvoiceValue(newInvoiceValue);
                if (taxableValue > 0 && tcsRate?.tcs_rate && !isTcsAmountChange) {
                    const tcsRateAmount =
                        tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                            ? parseFloat(newInvoiceValue * ((tcsRate?.tcs_rate || 0) / 100))
                            : parseFloat(taxableValue * ((tcsRate?.tcs_rate || 0) / 100));
                    setTcsRate({
                        ...tcsRate,
                        tcs_amount: parseFloat(tcsRateAmount || 0).toFixed(2),
                    });
                }
                const cgst =
                    !classification.rcm_applicable && !isIGSTCalculation && isSGSTCalculation
                        ? customToFixed(
                            parseFloat(gstValue?.cgstValue || 0) + parseFloat(additionalGst || 0),
                            2
                        )
                        : 0;
                const sgst =
                    !classification.rcm_applicable && !isIGSTCalculation && isSGSTCalculation
                        ? customToFixed(
                            parseFloat(gstValue?.sgstValue) + parseFloat(additionalGst || 0),
                            2
                        )
                        : 0;
                const igst =
                    !classification.rcm_applicable && isIGSTCalculation && classification.classification_nature_name !== "Export Sales Taxable" && classification.classification_nature_name !== "Sales to SEZ Taxable"
                        ? customToFixed(
                            parseFloat(gstValue?.igstValue) + parseFloat(additionalGst || 0),
                            2
                        )
                        : 0;
                const totalWithoutAddLess =
                    parseFloat(itemTotal) +
                    parseFloat(addition_charges?.addition_charges || 0) +
                    parseFloat(configurationList?.footer?.is_enabled_tcs_details ? tcsRate.tcs_tax_id ? isNaN(tcsRate.tcs_amount) ? 0 : tcsRate.tcs_amount : 0 || 0 : 0) +
                    // (parseFloat(totalAddLessAmount) || 0) +
                    parseFloat(company?.company?.is_gst_applicable ? cessValue || 0 : 0) +
                    parseFloat(company?.company?.is_gst_applicable ? cgst : 0) +
                    parseFloat(company?.company?.is_gst_applicable ? sgst : 0) +
                    parseFloat(company?.company?.is_gst_applicable ? igst : 0);
                const updatedAddLessCharges = calculateAddLessCharges(
                    addLessChanges,
                    totalWithoutAddLess
                );
                const totalAddLessAmount = updatedAddLessCharges?.reduce((sum, change) => {
                    if (change.al_type === 1) {
                        return sum + (parseFloat(change.al_value) || 0);
                    } else if (change.al_type === 2) {
                        return sum + (parseFloat(change.al_total) || 0);
                    }
                    return sum;
                }, 0);
                setMainGrandTotal(parseFloat(totalWithoutAddLess + (totalAddLessAmount || 0)));
                const gstData = calculateAdditionalClassification(classification, addition_charges?.acTotal);
                setAdditionalGst(gstData);
            }
        }, [
            taxableValue,
            tcsRate.tcs_amount,
            grandTotal,
            mainGrandTotal,
            cessValue,
            items,
            addLessChanges,
            gstValue,
            additionalGst,
            additionalCharges,
            configurationList?.footer?.round_off_method,
            configurationList?.footer?.is_enabled_tcs_details
        ]);

    const calculateTotal = () => {
        const { grandFinalAmount, roundOffAmount } = RoundOffMethod(
            mainGrandTotal,
            (id || gstQuote?.original_inv_no) && !isCheckGstType && gstCalculation?.round_off_method ? gstCalculation?.round_off_method : configurationList?.footer?.round_off_method
        );
        setGstCalculation({
            ...gstCalculation,
            is_gst_enabled: company?.company?.is_gst_applicable,
            round_of_amount: roundOffAmount,
        });
        setFinalAmount(grandFinalAmount);
    };

    useEffect(() => {
        if (isEditCalculation) {
            calculateTotal();
        }
    }, [mainGrandTotal, configurationList?.footer?.round_off_method, company, isCheckGstType]);


    useEffect(() => {
        if (isEditCalculation) {
            const newInvoiceValue = isIGSTCalculation
                ? parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.igstValue || 0)
                : parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.sgstValue || 0) +
                  parseFloat(gstValue?.cgstValue || 0);
            setInvoiceValue(newInvoiceValue);
            if (tcsRate?.tcs_rate) {
                setTcsRate(prevRate => ({
                    ...prevRate,
                    tcs_amount:
                        tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                            ? parseFloat(
                                  newInvoiceValue * ((tcsRate?.tcs_rate || 0) / 100)
                              ).toFixed(2)
                            : parseFloat(taxableValue * ((tcsRate?.tcs_rate || 0) / 100)).toFixed(
                                  2
                              ),
                }));
            }
            if (paymentLedgerDetail.tds_rate) {
                setPaymentLedgerDetail({
                    ...paymentLedgerDetail,
                    tds_amount: RoundOffMethodForTds(taxableValue * (paymentLedgerDetail?.tds_rate / 100), paymentLedgerDetail?.rounding_type),
                });
            }
        }
    }, [taxableValue]);

    useEffect(() => {
        let updatedHeaders = table?.tableHeader || [];
        if (
            configurationList?.item_table_configuration?.is_enabled_discount_2 === false ||
            configurationList?.item_table_configuration?.is_enabled_discount_2 == 0
        ) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.DISCOUNT_2);
        }
        if (configurationList?.item_table_configuration?.is_enabled_mrp === false) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.MRP);
        }
        if (!configurationList?.item_table_configuration?.is_enabled_hsn_code) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.HSN_SAC);
        }
        const filteredColumns = updatedHeaders.filter(col => col?.is_enabled !== false);
        setUpdatedTableHeader(filteredColumns);
    }, [table?.tableHeader, configurationList]);

    useEffect(() => {
        if (!id) {
            const maxDate = moment(company?.company?.currentFinancialYear?.yearEndDate).format(
                "YYYY-MM-DD",
            );
            let newDate = "";
            const date1 = new Date();
            const date2 = new Date(maxDate);

            if (date1 < date2) {
                newDate = moment(date1).format("DD-MM-YYYY");
            } else {
                newDate = moment(date2).format("DD-MM-YYYY");
            }
            setInvoiceDetail({
                ...invoiceDetail,
                invoice_date: newDate,
                invoice_number: invoice?.incomeInvoice?.invoice_number,
            });
            setChangeTax(invoice?.incomeInvoice?.with_tax ? 1 : 0);
        }
    }, [invoice, company?.company?.currentFinancialYear]);

    useEffect(() => {
        if(invoice?.incomeInvoice?.invoice_type){
            dispatch(rearrangeItemList(IncomeCreateOrDebit ? TRANSACTION_TYPE.INCOME_CREDIT_NOTE : TRANSACTION_TYPE.INCOME_DEBIT_NOTE, invoice?.incomeInvoice?.invoice_type == 2 ? 1 : 2));
            setItemType(invoice?.incomeInvoice?.invoice_type == 2 ? "item" : "accounting");
        }
    }, [invoice])

    useEffect(() => {
        if (configurationList?.header?.is_change_gst_details) {
            const gstData = calculateClassification(
                classification,
                itemType === "item"
                    ? findGstRate(items, gstOptions)
                    : findGstRate(accountingItems, gstOptions),
            );
            if (isEditCalculation) {
                setGstValue({
                    cgstValue: gstData.totalCgstTax !== NaN ? gstData?.totalCgstTax : 0.0,
                    sgstValue: gstData?.totalSgstTax,
                    igstValue: gstData?.totalIgstTax,
                });
            }
        } else {
            const matchState =
                ledger?.partyDetail?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id;
            const gstData = gstCalculate(
                itemType === "item"
                    ? findGstRate(items, gstOptions)
                    : findGstRate(accountingItems, gstOptions),
                grandTotal,
                matchState,
            );
            if (isEditCalculation) {
                setGstValue({
                    cgstValue:
                        gstData.totalCgstTax !== NaN ? parseFloat(gstData.totalCgstTax) : 0.0,
                    sgstValue: parseFloat(gstData.totalSgstTax),
                    igstValue: parseFloat(gstData.totalIgstTax),
                });
            }
        }
    }, [
        classification,
        grandTotal,
        company,
        ledger?.partyDetail,
        accountingItems,
        items,
        additionalCharges,
        addLessChanges,
        tcsRate.tcs_amount,
        configurationList?.footer?.round_off_method,
    ]);

    useEffect(() => {
        if (ledger?.partyDetail?.billingAddress?.state_id) {
            const GstType =
                localDispatchAddress[selectedAddress]?.state_id ==
                ledger?.partyDetail?.billingAddress?.state_id
                    ? true
                    : false;
            setIsShowGstValue(GstType);
        }
    }, [company, ledger?.partyDetail]);

    useEffect(() => {
        const payment_detail = paymentLedgerDetail?.payment_detail?.map(payment => {
            const paymentDate = invoiceDetail.invoice_date;
            return {
                ...payment,
                pd_date: payment.is_show_invoice_date ? paymentDate : payment.pd_date,
            };
        });
        setPaymentLedgerDetail({
            ...paymentLedgerDetail,
            payment_detail: payment_detail,
        });
    }, [invoiceDetail]);

    useTransactionShortcuts(formRef);

    const handleSubmit = async (e, isConfirmed = false) => {
        const submitType = e.nativeEvent.submitter.value;
        const submit_button = submitType === "save" ? 1 : submitType == "saveAndNew" ? 2 : 3;
        const submit_button_type = submitType == "saveAndNew" ? "saveAndNew" : "";

        e.preventDefault();
        const dispatch_address = localDispatchAddress[selectedAddress];

        const isFirstDetailInvalid = detail => {
            return (
                (detail.ac_ledger_id === null && !detail.ac_value) ||
                detail.ac_value === "" ||
                (detail.ac_value == NaN && detail.ac_gst_rate_id === null) ||
                (detail.ac_gst_rate_id?.value === null && detail.ac_total === 0)
            );
        };
        const {
            itemList: item,
            is_na,
            hasNegativeTotal,
        } = prepareItemsData(
            items,
            gstCalculation,
            setGstCalculation,
            company,
            gstOptions,
            configurationList,
            changeTax,
            isInWord
        );
        const {
            ledgerList: ledger,
            is_na: is_na_ledger,
            hasNegativeLedgerTotal,
        } = prepareLedgerData(
            accountingItems,
            gstCalculation,
            setGstCalculation,
            gstOptions,
            configurationList,
            changeTax
        );
        const {isExempt, isNa} = areAllProductsExemptOrNA(itemType === "accounting" ? accountingItems : items)
        const {isAdditionalChargesExempt,isAdditionalChargesNa } = checkAllAdditionalChargesNaAndExempt(additionalCharges)

        const { is_cgst_sgst_igst_calculated, is_gst_na } = calculateGSTFlags(
            company,
            isNa,
            isExempt,
            isAdditionalChargesNa,
            isAdditionalChargesExempt
        );
        if (submitType) {
            setIsDisable(true);
            if (
                company?.company?.is_gst_applicable &&
                configurationList?.header?.is_change_gst_details &&
                !classification.classification_nature_name
            ) {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Classification nature type is required.",
                        type: toastType.ERROR,
                    }),
                );
            }
            if (hasNegativeTotal) {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Negative Item Amount Not Accepted.",
                        type: toastType.ERROR,
                    }),
                );
            }

            if (hasNegativeLedgerTotal) {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Negative Item Amount Not Accepted.",
                        type: toastType.ERROR,
                    }),
                );
            }

            setSubmitTypeLocal(submitType === "saveAndNew" ? "duplicate" : submitType);
            if (otherDetail.credit_limit && !isConfirmed && !IncomeCreateOrDebit) {
                const paidAmount = paymentLedgerDetail?.payment_detail.reduce((total, item) => {
                    const amount = parseFloat(item.pd_amount);
                    return total + (isNaN(amount) ? 0 : amount);
                }, 0);

                const data = {
                    customer_ledger_id: gstQuote.party_ledger_id,
                    grand_total: customToFixed(finalAmount, 2),
                    amount_paid_with_transaction: paidAmount,
                };

                try {
                    const creditLimitData = await checkCreditLimit(data);
                    if (creditLimitData?.isCustomerCreditLimit) {
                        setCreditLimitModal({
                            open: true,
                            action: creditLimitData?.customerLimitAction,
                        });
                        return;
                    }
                } catch (error) {
                    console.error("Error checking credit limit:", error);
                }
            }


            const submitData = {
                ...(IncomeCreateOrDebit
                    ? {
                          invoice_number: invoiceDetail.invoice_number,
                          cn_item_type: itemType === "item" ? 2 : 1,
                      }
                    : {
                          debit_note_number: invoiceDetail.invoice_number,
                          dn_item_type: itemType === "item" ? 2 : 1,
                      }),
                date: (invoiceDetail.invoice_date),
                dispatch_address_id: configurationList?.header?.is_enabled_dispatch_details ? dispatchAddressId : null,
                customer_ledger_id: gstQuote.party_ledger_id,
                ...(company?.company?.is_gst_applicable && {
                    gstin: gstQuote.gstin,
                }),
                region_iso:gstQuote?.mobile?.region_iso,
                region_code:gstQuote?.mobile?.region_code,
                ...(configurationList?.header?.is_enabled_phone_number && {
                    party_phone_number: gstQuote?.mobile?.party_phone_number,
                }),
                original_inv_no: gstQuote.original_inv_no,
                original_inv_date: (gstQuote.original_inv_date),
                billing_address: {
                    address_1: partyAddress.billingAddress.address_1,
                    address_2: partyAddress.billingAddress.address_2,
                    country_id: partyAddress.billingAddress.country_id,
                    state_id: partyAddress.billingAddress.state_id,
                    city_id: partyAddress.billingAddress.city_id,
                    pin_code: partyAddress.billingAddress.pin_code,
                },
                same_as_billing: sameAsBill ? 1 : 0,
                shipping_gstin: partyAddress.shippingAddress?.shipping_gstin,
                shipping_name: partyAddress.shippingAddress?.shipping_name,
                address_name: partyAddress?.shippingAddress?.address_name,
                party_name_same_as_address_name: partyAddress?.shippingAddress?.party_name_same_as_address_name ? 1 : 0,
                ...(partyAddress.shippingAddress?.shipping_address_id ?
                    { shipping_address_id: partyAddress.shippingAddress?.shipping_address_id } :
                    {
                        shipping_address: configurationList?.header?.is_enabled_shipping_address ? {
                            address_name: partyAddress?.shippingAddress?.address_name,
                            shipping_gstin: partyAddress.shippingAddress?.shipping_gstin,
                            shipping_name: partyAddress.shippingAddress?.shipping_name,
                            address_1: partyAddress?.shippingAddress?.address_1,
                            address_2: partyAddress?.shippingAddress?.address_2,
                            country_id: partyAddress?.shippingAddress?.country_id,
                            state_id: partyAddress?.shippingAddress?.state_id,
                            city_id: partyAddress?.shippingAddress?.city_id,
                            pin_code: partyAddress?.shippingAddress?.pin_code,
                            party_name_same_as_address_name: partyAddress?.shippingAddress?.party_name_same_as_address_name ? 1 : 0
                        } : null
                    }),
                broker_details: configurationList?.header?.is_enabled_broker_details && brokerDetail?.broker_id
                    ? {
                        broker_id: brokerDetail.broker_id,
                        brokerage_for_sale: brokerDetail.broker_percentage,
                        brokerage_on_value_type: brokerDetail.brokerage_on_value,
                    }
                    : null,
                transport_details: configurationList?.header?.is_enabled_transport_details ? {
                    transport_id: transporterDetail.transport_id,
                    transporter_document_number: transporterDetail.transporter_document_number,
                    transporter_document_date: transporterDetail.transporter_document_date,
                    transporter_vehicle_number: transporterDetail.transporter_vehicle_number,
                } : null,
                eway_bill_details: configurationList?.header?.is_enabled_eway_details ? {
                    eway_bill_number: ewayBillDetail.eway_bill_number,
                    eway_bill_date: ewayBillDetail.eway_bill_date,
                } : null,
                other_details: {
                    po_no: otherDetail.po_number,
                    po_date: otherDetail.date,
                    credit_period: otherDetail.creditPeriod,
                    credit_period_type: otherDetail.creditPeriod
                        ? otherDetail.creditPeriodType
                        : null,
                    // shipping_name: null,
                    // shipping_gstin: null,
                },
                ...(itemType === "item" && { items: item }),
                ...(itemType === "accounting" && { ledgers: ledger }),
                main_classification_nature_type:
                    correctClassificationNatureType(isCheckGstType,
                        classification.classification_nature_name,
                        configurationList?.header?.is_change_gst_details,
                        partyAddress.billingAddress.state_id,
                        dispatch_address?.state_id,
                        isNa,
                        isExempt,
                        isAdditionalChargesNa,
                        isAdditionalChargesExempt),
                is_rcm_applicable: classification.rcm_applicable ? 1 : 0,
                narration: configurationList?.footer?.is_enable_narration ? additionalCharges?.note || null : null,
                term_and_condition: configurationList?.footer?.is_enabled_terms_and_conditions ? additionalCharges?.terms_and_conditions : null,
                ...(configurationList?.footer?.is_enabled_bank_details && {bank_id: additionalCharges?.bank_id}),
                ...(additionalCharges?.additional_detail?.length === 1 &&
                    isFirstDetailInvalid(additionalCharges?.additional_detail[0])
                    ? { additional_charges: " " }
                    : {
                        additional_charges: additionalCharges?.additional_detail?.map(detail => ({
                            ...detail,
                            ...(company?.company?.is_gst_applicable && {
                                ac_gst_rate_id: detail.ac_gst_rate_id?.value || null,
                            }),
                            // add_less_total: detail?.ac_total,
                            ac_total_without_tax:
                                detail?.ac_type == 2 ? detail?.ac_total : detail.ac_value,
                        })),
                    }),
                taxable_value: customToFixed(taxableValue, 2),
                gross_value: customToFixed(grandTotal, 2),
                total: grandTotal,
                ...(company?.company?.is_gst_applicable && {
                    cgst: classification.rcm_applicable
                        ? 0
                        : customToFixed(
                            !isIGSTCalculation && isSGSTCalculation
                                ? !isEditCalculation
                                    ? parseFloat(gstValue?.cgstValue)
                                    : parseFloat(gstValue?.cgstValue) + parseFloat(additionalGst || 0)
                                : 0,
                            2
                        ) || 0,
                }),
                ...(company?.company?.is_gst_applicable && {
                    sgst: classification.rcm_applicable
                        ? 0
                        : customToFixed(
                            !isIGSTCalculation && isSGSTCalculation
                                ? !isEditCalculation
                                    ? parseFloat(gstValue?.sgstValue)
                                    : parseFloat(gstValue?.sgstValue) + parseFloat(additionalGst || 0)
                                : 0,
                            2
                        ) || 0,
                }),
                ...(company?.company?.is_gst_applicable && {
                    igst: classification.rcm_applicable
                        ? 0
                        : customToFixed(
                            isIGSTCalculation
                                ? !isEditCalculation
                                    ? parseFloat(gstValue?.igstValue)
                                    : parseFloat(gstValue?.igstValue) + parseFloat(additionalGst || 0)
                                : 0,
                            2
                        ),
                }),
                ...(configurationList?.footer?.is_enabled_tcs_details ? { tcs_details: tcsRate.tcs_tax_id ? tcsRate : [] } : {}),
                add_less: addLessChanges[0]?.al_ledger_id ? addLessChanges : [],
                grand_total: customToFixed(finalAmount, 2),
                ...(configurationList?.footer?.is_enabled_tds_details ? {
                    tds_details: {
                        tds_tax_id: paymentLedgerDetail.tds_tax_id,
                        tds_rate: paymentLedgerDetail.tds_rate,
                        tds_amount: paymentLedgerDetail.tds_amount,
                    }
                } : {}),
                ...(paymentLedgerDetail.payment_detail[0]?.pd_ledger_id
                    ? {
                          payment_details: paymentLedgerDetail.payment_detail,
                      }
                    : {
                          payment_details: " ",
                      }),
                cess: customToFixed(cessValue, 2),
                round_off_amount: customToFixed(gstCalculation.round_of_amount, 2),
                is_gst_enabled: !company?.company?.is_gst_applicable
                    ? 0
                    : gstCalculation.is_gst_enabled || 1,
                rounding_amount: customToFixed(gstCalculation.round_of_amount, 2),
                is_cgst_sgst_igst_calculated,
                is_gst_na,
                is_round_off_not_changed: gstCalculation.is_round_off_not_changed ?? 1,
                submit_button_value: submit_button,
                advance_payment: selectedAdvancePayment,
                // custom_fields: customFieldListTransaction?.flatMap((customField) =>
                //     customField.value ? [{ ...(customField?.id ? {id: customField?.id} : {}), custom_field_id: customField.custom_field_id, value: customField.value }] : []
                // ),
                custom_fields: customFieldListTransaction?.flatMap(customField =>
                    customField.value &&
                    (customField.is_enabled === undefined || customField.is_enabled)
                        ? [
                              {
                                  ...(customField?.id ? { id: customField.id } : {}),
                                  custom_field_id: customField.custom_field_id,
                                  value: customField.value,
                              },
                          ]
                        : []
                ),
                round_off_method: (id || isDuplicateDebitNote || gstQuote?.original_inv_no) && !isCheckGstType && gstCalculation?.round_off_method ? gstCalculation?.round_off_method : configurationList?.footer?.round_off_method
            };
            const formData = convertToFormData(submitData);
            if (additionalCharges?.upload_document) {
                additionalCharges.upload_document.forEach((doc, index) => {
                    if (doc.file) {
                        formData.append(
                            IncomeCreateOrDebit
                                ? `income_credit_note_document[${index}]`
                                : `income_debit_note_document[${index}]`,
                            doc.file ? doc.file : " ",
                        );
                    }
                });
            }

            if (id && !isDuplicateDebitNote) {
                dispatch(
                    updateIncome(
                        id,
                        formData,
                        submitType,
                        IncomeCreateOrDebit,
                        submit_button,
                        setShowEInvoiceModal,
                        setIsDisable
                    ),
                );
            } else {
                dispatch(
                    addIncome(
                        formData,
                        submitType === "saveAndNew" ? "duplicate" : submitType,
                        IncomeCreateOrDebit,
                        submit_button,
                        setShowEInvoiceModal,
                        setIsDisable,
                        submitData,
                        submit_button_type
                    ),
                );
            };
            setisFieldsChanges(false);
            setHasUnsavedChanges(false);
        }
    };

    const handleInput = (e) => {
        setisFieldsChanges(true);
    };

    const handleBack = () => {
        if(isFieldsChanges) {
            setHasUnsavedChanges(true);
        };
    };

    const handleBeforeUnload = (e) => {
        if (isFieldsChanges && isBackButtonClick) {
            e.preventDefault();
            e.returnValue = '';
        }
    };

    useEffect(() => {
        if (isBackButtonClick) {
                window.addEventListener('beforeunload', handleBeforeUnload);
            return () => {
                window.removeEventListener('beforeunload', handleBeforeUnload);
            };
        };
    }, [isFieldsChanges, isBackButtonClick]);

    useEffect(() => {
        setBrokerDetail({
            ...brokerDetail,
            broker_percentage: broker?.getBrokerDetailsById?.brokerage_for_sale || "",
            brokerage_on_value: broker?.getBrokerDetailsById?.brokerage_for_sale_type || "",
        });
    }, [broker?.getBrokerDetailsById]);

    const salesUrl = IncomeCreateOrDebit
        ? "/company/income-credit-notes"
        : "/company/income-debit-notes";
    const salePrevUrl = `${url}${salesUrl}/${prevNext?.fetchPrevNextUrl?.previousBillId + "/edit"}`;
    const saleNextUrl = `${url}${salesUrl}/${
        prevNext?.fetchPrevNextUrl?.nextBillId
            ? prevNext.fetchPrevNextUrl.nextBillId + "/edit"
            : "create"
    }`;
    const incomePrevUrl = `${url}${salesUrl}/${
        prevNext?.fetchPrevNextUrl?.previousBillId + "/edit"
    }`;
    const saleReturnNextUrl = `${url}${salesUrl}/${
        prevNext?.fetchPrevNextUrl?.nextBillId
            ? prevNext.fetchPrevNextUrl.nextBillId + "/edit"
            : "create"
    }`;

       const handleDeleteTransaction = () => {
            if (id) {
                openDeleteTransactionModel()
                // dispatch(deleteSale(id, saleCreateOrReturn));
            }
        };

        const handleConfirmDelete = () => {
            if (id) {
                dispatch(deleteCnDn(id, IncomeCreateOrDebit, setShowDeleteWarningModel));
                closeDeleteTransactionModel();
            }
        }

    return (
        <>
            <CustomHelmet
                title={
                    id
                        ? IncomeCreateOrDebit
                            ? "Edit Credit Notes "
                            : "Edit Debit Notes "
                        : IncomeCreateOrDebit
                        ? "Add Credit Notes "
                        : "Add Debit Notes "
                }
            />
            {/* incomeEdit && !singleSale */}
            {loader ? (
                <Loader />
            ) : loader && (!configurationList?.document_prefix || showEInvoiceModal.id) ? (
                <Loader />
            ) : incomeNote.status === 404 && !window.location.pathname.includes("/create") ? (
                <Error404Page />
            ) : (
                <div className="ms-3 mt-12" defaultActiveKey={"edit"} id="uncontrolled-tab-example">
                    {/* <Tab eventKey={"edit"} title="Edit"> */}
                    <form ref={formRef} onSubmit={handleSubmit} onInput={handleInput} >
                        <Container fluid className="p-0">
                            <div className="content-wrapper-invoice py-6 px-lg-10 px-sm-8 px-6">
                                <SaleReturnInvoiceDetail
                                    id={id}
                                    incomeCnDnRef={incomeCnDnRef}
                                    IncomeCreateOrDebit={IncomeCreateOrDebit}
                                    ledgerModalName={"Add Customer"}
                                    ledgerModalEditName={"Update Customer"}
                                    shipping_address_type={shippingAddressType}
                                    isDuplicate
                                />
                            </div>
                            <div className="custom-nav-tabs nav-tabs d-flex">
                                <a
                                    href={
                                        !isFieldsChanges
                                            ? IncomeCreateOrDebit
                                                ? incomePrevUrl
                                                : salePrevUrl
                                            : undefined
                                    }
                                    onClick={e => {
                                        if (isFieldsChanges) {
                                            e.preventDefault();
                                            setUnsavedBackUrl(
                                                IncomeCreateOrDebit ? incomePrevUrl : salePrevUrl
                                            );
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        }
                                    }}
                                    className={`arrow-btn prev-btn d-flex justify-content-center ${
                                        !prevNext?.fetchPrevNextUrl?.previousBillId && "disabled"
                                    } align-items-center cursor_pointer`}
                                >
                                    <i className="fa-solid fa-angle-left"></i>
                                </a>
                                <a
                                    href={
                                        !isFieldsChanges
                                            ? IncomeCreateOrDebit
                                                ? saleReturnNextUrl
                                                : saleNextUrl
                                            : undefined
                                    }
                                    onClick={e => {
                                        if (isFieldsChanges) {
                                            e.preventDefault();
                                            setUnsavedBackUrl(
                                                IncomeCreateOrDebit
                                                    ? saleReturnNextUrl
                                                    : saleNextUrl
                                            );
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        }
                                    }}
                                    className={`arrow-btn next-btn d-flex justify-content-center align-items-center  ${
                                        !prevNext?.fetchPrevNextUrl?.nextBillId && !id && "disabled"
                                    } me-3 cursor_pointer`}
                                >
                                    <i className="fa-solid fa-angle-right"></i>
                                </a>
                                <ConfigurationModal roundOffOption={roundOffOption} />
                            </div>
                        </Container>
                        <SaleItems
                            items={itemType === "item" ? items : accountingItems}
                            setItems={itemType === "item" ? setItems : setAccountingItems}
                            grandTotal={grandTotal}
                            setGrandTotal={setGrandTotal}
                            mainGrandTotal={mainGrandTotal}
                            shippingValue={shippingValue}
                            setShippingValue={setShippingValue}
                            tcsValue={tcsValue}
                            setTCSValue={setTCSValue}
                            tcsRateValue={tcsRateValue}
                            setTCSRateValue={setTCSRateValue}
                            packingCharge={packingCharge}
                            setPackingCharge={setPackingCharge}
                            tableHeader={updatedTableHeader}
                            tableHeaderList={tableHeaderList}
                            accountingTableHeader={accountingTableHeader}
                            isShowGstValue={isShowGstValue}
                            taxableValue={taxableValue}
                            setFinalAmount={setFinalAmount}
                            finalAmount={finalAmount}
                            isShowPaymentDetails={true}
                            showCreditLimit={!IncomeCreateOrDebit}
                            settlePaymentType={
                                IncomeCreateOrDebit ? "income-credit-note" : "income-debit-note"
                            }
                            shipping_address_type={shippingAddressType}
                            isInWord={isInWord}
                        />
                        <Container fluid className="p-0 mt-10 fixed-bottom-section">
                            <div className="d-flex flex-wrap gap-sm-4 gap-3 fixed-buttons px-lg-10 px-sm-8 px-6">
                                <button
                                    type="submit"
                                    name="submitType"
                                    value="save"
                                    className="btn btn-primary"
                                    disabled={isDisable}
                                >
                                    Save
                                </button>
                                {!id && (
                                    <button
                                        type="submit"
                                        name="submitType"
                                        value="saveAndNew"
                                        className="btn btn-primary"
                                        disabled={isDisable}
                                    >
                                        Save & New
                                    </button>
                                )}
                                <button
                                    type="submit"
                                    name="submitType"
                                    value="saveAndPrint"
                                    className="btn btn-primary"
                                    disabled={isDisable}
                                >
                                    Save & Print
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        if (isFieldsChanges) {
                                            setUnsavedBackUrl(
                                                `${window.location.origin}${salesUrl}`
                                            );
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        } else {
                                            window.location.href = `${window.location.origin}${salesUrl}`;
                                        }
                                    }}
                                >
                                    Back
                                </button>
                                {id && (
                                    <button
                                        onClick={handleDeleteTransaction}
                                        className="btn btn-danger"
                                    >
                                        Delete
                                    </button>
                                )}
                            </div>
                        </Container>
                    </form>
                </div>
            )}
            <WarningModal
                show={showEInvoiceModal.open}
                handleClose={() => {
                    setShowEInvoiceModal({ open: false, id: null });
                    if (IncomeCreateOrDebit) {
                        if (submitTypeLocal === "duplicate" || submitTypeLocal === "saveAndNew") {
                            window.location.href = `${window.location.origin}/company/income-credit-notes/create`;
                        } else {
                            window.location.href = `${window.location.origin}/company/income-credit-notes`;
                        }
                    } else {
                        if (submitTypeLocal === "duplicate" || submitTypeLocal === "saveAndNew") {
                            window.location.href = `${window.location.origin}/company/income-debit-notes/create`;
                        } else {
                            window.location.href = `${window.location.origin}/company/income-debit-notes`;
                        }
                    }
                }}
                handleSubmit={handleEInvoice}
                message="Do you want to generate E-Invoice?"
                confirmText="Yes"
                cancelText="No"
                confirmClass="success-btn"
                showCancelButton
                showConfirmButton
            />
            {creditLimitModal.action == 2 ? (
                <WarningModal
                    show={creditLimitModal.open}
                    handleClose={() => {
                        setCreditLimitModal({ open: false, id: null });
                        setIsDisable(false);
                    }}
                    handleSubmit={
                        creditLimitModal.action == 2
                            ? () => setCreditLimitModal({ open: false, id: null })
                            : null
                    }
                    message={
                        "The total customer invoice amount is more than Credit Limit Amount. So invoice is not generating?"
                    }
                    confirmText={"Ok"}
                    confirmClass="btn-danger"
                    showCancelButton={false}
                    showConfirmButton
                    isWarning={false}
                    modalTitle={"Blocked!"}
                />
            ) : (
                ""
            )}

            {creditLimitModal.action === 1 && (
                <WarningModal
                    show={creditLimitModal.open}
                    handleClose={() => {
                        setCreditLimitModal({ open: false, id: null });
                        setIsDisable(false);
                    }}
                    handleSubmit={() => {
                        setCreditLimitModal(prev => ({ ...prev, open: false }));
                        setTimeout(() => {
                            handleSubmit(
                                {
                                    nativeEvent: {
                                        submitter: { value: submitTypeLocal },
                                    },
                                    preventDefault: () => {},
                                },
                                true
                            );
                            setIsDisable(false);
                        }, 0);
                    }}
                    message={
                        "The total customer invoice amount is more than Credit Limit Amount. Are you sure to continue this?"
                    }
                    confirmText={"Yes, Continue"}
                    cancelText={"No, Cancel"}
                    confirmClass="btn-danger"
                    showCancelButton
                    showConfirmButton
                    isWarning
                />
            )}
            {deleteTransaction && (
                <WarningModal
                    show={deleteTransaction}
                    title="Delete!"
                    message="Are you sure want to delete this transaction?"
                    showCancelButton
                    showConfirmButton
                    confirmText="Yes, Delete"
                    cancelText="No, Cancel"
                    handleClose={closeDeleteTransactionModel}
                    handleSubmit={() => handleConfirmDelete()}
                />
            )}
            <Toast />
            {showDeleteWarningModel && (
                <DeleteWarningModal
                    show={showDeleteWarningModel?.show}
                    handleClose={() => setShowDeleteWarningModel({ show: false })}
                    transactions={showDeleteWarningModel?.transactions}
                />
            )}
        </>
    );
};

export default IncomeTransaction;
