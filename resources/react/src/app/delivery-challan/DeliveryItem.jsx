import moment from "moment";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import CustomFieldDate from "../../components/ui/CustomFieldDate";
import ReactSelect from "../../components/ui/ReactSelect";
import { TABLE_HEADER_TYPE, WARRANTY_FIELD_TYPE } from "../../constants";
import {
    calculateTotal,
    convertToZeroStringWithOne,
    customToFixed,
    formattedDate,
    getCalculatedQuantity,
    updateCustomFieldCalculation
} from "../../shared/calculation";
import { handleShortcutKeys } from "../../shared/shortcut-keys";
import {
    fetchItemById,
    fetchItemList,
    fetchPriceListOfItems,
    fetchSingleItemById,
    getItemModelDetail,
} from "../../store/item/itemSlice";

const DeliveryItem = memo(
    ({
        item,
        index,
        id,
        onUpdate,
        removeItem,
        tableHeader,
        itemType,
        openItemModel,
        handleOpenSecond,
        itemOption,
        itemUnitOption,
        openQuantityModelHandler,
        openNegativeModelHandler,
        saleConfiguration,
        itemCount,
        setCreateNewItem,
        setisFieldsChanges,
        setIsEditCalculation,
        getCustomFieldTransactionType,
        ledgerId,
        handleOpenInventoryDetail
    }) => {
        const [itemState, setItemState] = useState(item);
        const [descriptionCount, setDescriptionCount] = useState(0);
        const [isFocused, setIsFocused] = useState(false);
        const [typingTimeout, setTypingTimeout] = useState(0);

        useEffect(() => {
            setItemState(item);
        }, [item, itemType]);

        const handleFocus = () => setIsFocused(true);
        const handleBlur = () => setIsFocused(false);

        const dispatch = useDispatch();
        const itemRef = useRef(null);
        const unitRef = useRef(null);
        const quantityRef = useRef(null);
        const totalRef = useRef(null);
        const mrpRef = useRef(null);
        const priceWithGstRef = useRef(null);
        const ledgerRef = useRef(null);

        const handleKeyDown = (event, nextRef, currentRef, type) => {
            if (event.key === "Enter" && nextRef && nextRef.current) {
                event.preventDefault();
                if (currentRef?.current?.getFocusedOption) {
                    let focusedOption = currentRef?.current?.getFocusedOption();
                    if (type === "item" && focusedOption) {
                        handleItemChange(focusedOption);
                    } else if (type === "unit" && focusedOption) {
                        handleUnitChange(focusedOption);
                    }
                }
                nextRef.current.focus();
            }
        };

        const handleItemChange = useCallback(
           async selectedOption => {
                setisFieldsChanges(true);
                const selectedData = parseInt(selectedOption?.value);
                const transaction_item_id = itemState?.selectedItem == selectedData ? itemState?.transaction_item_id : null
                if (selectedOption.__isNew__) {
                    return setCreateNewItem({ name: selectedOption?.value, index: index });
                }
                // dispatch(fetchSingleItemById(SelectedData));
                const priceListDetail = await dispatch(fetchPriceListOfItems([selectedData], ledgerId, null, null, getCustomFieldTransactionType, transaction_item_id));
                const selectedItemData = itemOption.find(item => item?.value === selectedData);
                if (selectedItemData) {
                    const updatedItem = {
                        ...itemState,
                        selectedItem: selectedData,
                        quantity: 1,
                    };
                    // if (itemType === "item") {
                    //     dispatch(updateSaleItemAction(updatedItem));
                    // }
                    onUpdate(id, updatedItem, index);
                    // if (saleConfiguration?.header?.is_change_gst_details && itemType == "item")
                        handleOpenSecond(priceListDetail, index);
                    // }
                }
            },
            [itemState, id, saleConfiguration, itemOption]
        );

        const handleOpenInventoryModal = useCallback( async () =>{
            handleOpenInventoryDetail(itemState, index);
        }, [itemState, index]);

        useEffect(() => {
            setDescriptionCount(itemState?.additional_description?.length || 0);
        }, [itemState?.additional_description]);

        const handleUnitChange = useCallback(
            selectedUnit => {
                const updatedItem = {
                    ...itemState,
                    selectedUnit: selectedUnit.value,
                };
                setisFieldsChanges(true);
                setItemState(updatedItem);
                onUpdate(id, updatedItem, index);
            },
            [itemState, onUpdate, id]
        );

        const handleChangeCustomField = (value, column, type) => {
                const updatedValue = value;

                // Step 1: Update the changed custom field
                let updatedCustomFields = Array.isArray(itemState?.custom_fields)
                    ? itemState.custom_fields.some(
                        item => item.custom_field_id == column.custom_field_id
                    )
                        ? itemState.custom_fields.map(item =>
                            item.custom_field_id == column.custom_field_id
                                ? {
                                        ...item,
                                        ...(type === 'warranty' ? { field_type: updatedValue || "" } : { value: (type == 'number' ? parseFloat(updatedValue) : updatedValue) || "" }),
                                    ...(type === 'select' && { option_id: updatedValue || "" }),
                                }
                              : item
                      )
                    : [
                          ...itemState.custom_fields,
                          {
                              custom_field_id: column.custom_field_id,
                              ...(type !== 'warranty' && type !== 'select' && {value: (type == 'number' ? parseFloat(updatedValue) : updatedValue) || ""}),
                              ...(type === 'warranty' && { field_type: updatedValue || "" }),
                              ...(type === 'select' && { option_id: updatedValue || "" }),
                          },
                      ]
                    : [
                        {
                            custom_field_id: column.custom_field_id,
                            ...(type !== 'warranty' && type !== 'select' && {value: (type == 'number' ? parseFloat(updatedValue) : updatedValue) || ""}),
                            ...(type === 'warranty' && { field_type: updatedValue || "" }),
                            ...(type === 'select' && { option_id: updatedValue || "" }),
                        },
                    ];

                    // Step 2: Recalculate all formula-based custom fields
                    const updatedCustomFieldForCalculations = updateCustomFieldCalculation(updatedCustomFields);
                    updatedCustomFields = updatedCustomFieldForCalculations;

                    // Step 3: Recalculate quantity from formula (if exists)
                    const newQuantity = getCalculatedQuantity(updatedCustomFieldForCalculations);
                    const decimalPlaces = itemState?.item_master?.decimal_places ?? 2;
                    // Step 4: Final merged item
                    const updatedItem = {
                        ...itemState,
                        is_change_quantity: false,
                        custom_fields: updatedCustomFields,
                        quantity:
                            newQuantity != null
                                ? parseFloat(newQuantity || 1).toFixed(decimalPlaces)
                                : itemState.quantity || 1,
                    };
                    setIsEditCalculation(true);
                    const calculatedTotal = calculateTotal(
                        updatedItem,
                        false,
                        true,
                        itemType === "accounting"
                    );
                    (updatedItem.total = calculatedTotal.total),
                    (updatedItem.updatedTotal = calculatedTotal.total),
                    (updatedItem.sgstValue = calculatedTotal.sgst),
                    (updatedItem.cgstValue = calculatedTotal.sgst),
                    (updatedItem.cessValue = calculatedTotal.cess),
                    setItemState(updatedItem),
                    // Step 5: Pass to parent handler
                    onUpdate(id, updatedItem, index);
                };

        const handleChangeDate = (date, column, input_type) =>{
            const isValidDate = date && !isNaN(new Date(date).getTime());
            const formattedDateValue = isValidDate ? input_type == "datetime" ? moment(new Date(date)).format("DD-MM-YYYY HH:mm") : formattedDate(new Date(date)) : "";
            const updatedItem = {
                ...itemState,
                custom_fields: Array.isArray(itemState?.custom_fields)
                    ? itemState.custom_fields.some(item => item.custom_field_item_id == column.custom_field_item_id)
                        ? itemState.custom_fields.map(item =>
                            item.custom_field_item_id == column.custom_field_item_id
                                ? { ...item, value: formattedDateValue }
                                : item
                        )
                        : [...itemState.custom_fields, { custom_field_item_id: column.custom_field_item_id, value: formattedDateValue }]
                    : [{ custom_field_item_id: column.custom_field_item_id, value: formattedDateValue }]
            };
                onUpdate(id, updatedItem, index);
        }

        const handleQuantityChange = useCallback(
            quantity => {
                const updatedItem = {
                    ...itemState,
                    quantity: parseFloat(quantity) || 0,
                };
                setisFieldsChanges(true);
                setItemState(updatedItem), onUpdate(id, updatedItem, index);
            },
            [itemState, onUpdate, id, saleConfiguration]
        );

        const handleQuantityModel = (e, id) => {
            e.target.select();
            if (saleConfiguration?.item_table_configuration?.consolidating_items_to_invoice) {
                openQuantityModelHandler(id);
            }
        };

        const handleOpenItemModal = (id, index) => {
            if (id) {
                dispatch(fetchItemById(id));
            }
            openItemModel(id, index);
            dispatch(getItemModelDetail());
        };

        const handleChangeDescription = e => {
            const { value } = e.target;
            const updatedItem = {
                ...itemState,
                additional_description: value,
            };
            setisFieldsChanges(true);
            setItemState(updatedItem);
            setDescriptionCount(value.length);
            onUpdate(id, updatedItem, index);
        };

        const shortcutItem = event => {
            if (event.altKey && event.keyCode == 67) {
                return handleOpenItemModal();
            }
        };

        handleShortcutKeys(shortcutItem);

        const handleScroll = () => {
            dispatch(fetchItemList({ skip: itemOption.length }));
        };

        const customFilter = searchText => {
            if (!searchText) {
                return;
            }
            if (typingTimeout) {
                clearTimeout(typingTimeout);
            }
            setTypingTimeout(
                setTimeout(() => dispatch(fetchItemList({ search: searchText })), 500)
            );
        };
        const checkIsSingleOrMultiInventory = itemState?.model_custom_fields?.[0]?.custom_field_type;
        const renderCell = column => {
            switch (column.header) {
                case TABLE_HEADER_TYPE.ITEM:
                    return (
                        <td key={column.id}>
                            <div id="TransactionForm" className="select-ledger-input mb-2 transaction-form d-flex align-items-center justify-content-between" style={{ minWidth: "160px" }}>
                                <div className="input-group flex-nowrap">
                                    <div className="position-relative h-40px w-100 pe-36px border-transparent focus-shadow">
                                        <ReactSelect
                                            value={itemState?.selectedItem || null}
                                            index={index}
                                            onChange={handleItemChange}
                                            options={itemOption}
                                            customLabel="item"
                                            customFilter={customFilter}
                                            onMenuScrollToBottom={handleScroll}
                                            styles={{
                                                container: provided => ({
                                                    ...provided,
                                                    minWidth: "230px",
                                                }),
                                            }}
                                            placeholder={"Select item"}
                                            defaultLabel=""
                                            onKeyDown={e =>
                                                handleKeyDown(e, ledgerRef, itemRef, "item")
                                            }
                                            ref={itemRef}
                                            islabel={true}
                                            required={true}
                                            portal={true}
                                            radius={true}
                                            showborder={false}
                                            width="300px"
                                            isEdit={true}
                                            handleOpen={handleOpenItemModal}
                                            // isCreatable
                                        />
                                    </div>
                                    <a
                                        onClick={() => handleOpenItemModal("", index)}
                                        className="input-group-text custom-group-text cursor-pointer"
                                        data-ledger-link-id="1"
                                        data-bs-toggle="tooltip"
                                        data-bs-placement="bottom"
                                        title="Shortcut Key : Alt + C"
                                    >
                                        <i className="fas fa-plus text-gray-900"></i>
                                    </a>
                                </div>
                                {itemState?.model_select_inventory_custom_fields?.length > 0 ?
                                    <a
                                        onClick={handleOpenInventoryModal}
                                        className="ms-2 input-group-text cursor-pointer inventory_modal_button px-3"
                                        data-ledger-link-id="1"
                                        data-bs-toggle="tooltip"
                                        data-bs-placement="bottom"
                                        title="Shortcut Key : Alt + C"
                                    >
                                        <i className="fas fa-eye text-gray-900"></i>
                                    </a>
                                    : ''}
                            </div>
                            {saleConfiguration?.item_table_configuration
                                ?.is_additional_item_description && (
                                <div className="position-relative">
                                    <textarea
                                        className="form-control border-transparent overflow-hidden pe-8"
                                        style={{
                                            fontStyle: "italic",
                                            fontSize: "12px",
                                            color: "#333333",
                                        }}
                                        placeholder="Additional Description For Item"
                                        type="text"
                                        value={itemState?.additional_description ?? ""}
                                        onChange={handleChangeDescription}
                                        // onInput={(e) => {
                                        //     e.target.style.height = "auto";
                                        //     e.target.style.height = `${e.target.scrollHeight}px`;
                                        // }}
                                        maxLength={5000}
                                        onFocus={handleFocus}
                                        onBlur={handleBlur}
                                        rows={1}
                                    />
                                    {isFocused && (
                                        <p
                                            className="position-absolute bottom-0 right-0 mb-0 end-0 fs-12 pt-1 bootstrap-maxlength badge text-primary limit-textarea"
                                            style={{ whiteSpace: "nowrap" }}
                                        >
                                            {5000 - descriptionCount || 0}
                                        </p>
                                    )}
                                </div>
                            )}
                        </td>
                    );
                case TABLE_HEADER_TYPE.QUANTITY:
                    return (
                        <td key={column.id} style={{ width: "100px" }}>
                            <div className="border-0">
                                <input
                                    className="form-control text-end h-40px border-transparent"
                                    required
                                    step={`0.${convertToZeroStringWithOne(
                                        itemState?.decimal_places
                                    )}`}
                                    type="number"
                                    min={`0.${convertToZeroStringWithOne(
                                        itemState?.decimal_places
                                    )}`}
                                    // defaultValue={1}
                                    value={itemState?.quantity != null ? parseFloat(itemState?.quantity) : null}
                                    onChange={e => handleQuantityChange(e.target.value)}
                                    onClick={e => handleQuantityModel(e, index)}
                                    ref={quantityRef}
                                    onKeyDown={e =>
                                        handleKeyDown(
                                            e,
                                            saleConfiguration?.item_table_configuration
                                                ?.is_enabled_mrp
                                                ? mrpRef
                                                : priceWithGstRef,
                                            quantityRef
                                        )
                                    }
                                    islabel="no"
                                    onBlur={e =>
                                        !saleConfiguration?.item_table_configuration
                                            ?.consolidating_items_to_invoice &&
                                        openNegativeModelHandler(e, itemState)
                                    }
                                    disabled={itemState?.model_select_inventory_custom_fields?.length > 0 || itemState?.custom_field_inventory_store?.length > 0}
                                />
                            </div>
                        </td>
                    );
                case TABLE_HEADER_TYPE.UOM:
                    return (
                        <td key={column.id} style={{ width: "124px" }}>
                            <div className="border-transparent h-40px min-w-124px input-group d-block">
                                <ReactSelect
                                    value={item?.selectedUnit || null}
                                    onChange={option => handleUnitChange(option, index)}
                                    placeholder={"Select unit"}
                                    defaultLabel={"Select unit"}
                                    options={item?.itemUnitOption || itemUnitOption}
                                    ref={unitRef}
                                    onKeyDown={e => handleKeyDown(e, quantityRef, unitRef, "unit")}
                                    islabel="no"
                                    showborder={false}
                                />
                            </div>
                        </td>
                    );
                    default:
                                        const matchedField = itemState?.custom_fields?.find(
                                            (field) => field.custom_field_id == column.custom_field_id
                                        );
                                        return column?.input_type === "number" || column?.input_type === "text" && column?.is_enabled ? (
                                            column?.custom_field_type == WARRANTY_FIELD_TYPE ? (
                                                <td key={column.id} style={{ minWidth: "130px", width: "130px" }}>
                                                    <div className="d-flex h-40px border-transparent sales-table_discount  position-relative">
                                                    <div className="discount border-0 top-0 input-group d-block min-w-80px">
                                                        <ReactSelect
                                                            value={matchedField?.field_type ?? 1}
                                                            defaultValue={1}
                                                            onChange={(e)=>handleChangeCustomField(e.value, column, "warranty")}
                                                            options={[
                                                                { label: 'Month', value: 1},
                                                                { label: "Year", value: 2 },
                                                            ]}
                                                            placeholder=""
                                                            showborder={false}
                                                            showbg={true}
                                                            height="32px"
                                                            width="80px"
                                                            isDisabled={matchedField?.is_able_to_edit ? false : true}
                                                        />
                                                    </div>
                                                    <div className="focus-shadow border-0">
                                                        <input
                                                            className="form-control text-end border-0"
                                                            type="text"
                                                            value={matchedField?.value ?? null}
                                                            onChange={(e)=>handleChangeCustomField(e.target.value, column, "text")}
                                                            onClick={e => e.target.select()}
                                                            disabled={matchedField?.is_able_to_edit ? false : true}
                                                        />
                                                    </div>
                                                </div>
                                                </td>
                                            ) :
                                            (<td key={column.id} style={{ width: "100px" }}>
                                                <div className={matchedField?.is_able_to_edit ? false : true ? 'react-input-disabled' : ''}>
                                                <input
                                                    className="form-control h-40px text-end border-transparent overflow-hidden"
                                                    type={column?.input_type}
                                                    value={matchedField?.is_able_to_edit ? column?.input_type === "number" ? parseFloat(matchedField?.value || null) ?? null : matchedField?.value ?? null : ''}
                                                    onChange={(e)=>handleChangeCustomField(e.target.value, column, column?.input_type)}
                                                    onClick={e => e.target.select()}
                                                    disabled={matchedField?.is_able_to_edit ? false : true}
                                                />
                                                </div>
                                            </td>
                                            )
                                        ) : column?.input_type?.includes("date") && column?.is_enabled ? (
                                            <td key={column.id} style={{ minWidth: "120px", width: "120px", }}>
                                                <div className={matchedField?.is_able_to_edit ? false : true ? 'react-input-disabled' : ''}>
                                                <CustomFieldDate
                                                    value={matchedField?.value ?? null}
                                                    onChange={(e) => handleChangeDate(e, column, column?.input_type)}
                                                    // placeholder={column.name}
                                                    input_type={column?.input_type}
                                                    onClick={e => e.target.select()}
                                                    disabled={matchedField?.is_able_to_edit ? false : true}
                                                />
                                                </div>
                                            </td>
                                        ) : column?.input_type === "select" && column?.is_enabled ? (
                                            <td key={column.id} style={{ minWidth: "150px", width: "150px", }}>
                                                 <div className={`border-transparent h-40px min-w-100px focus-shadow input-group d-block dropdown-disabled ${
                                                        matchedField?.is_able_to_edit ? false : true ? 'react-select-disabled' : ''
                                                    }`}>
                                                    <ReactSelect
                                                        value={matchedField?.option_id || null}
                                                        onChange={(e)=>handleChangeCustomField(e.value, column, "select")}
                                                        options={column?.options?.map(opt => ({
                                                            value: opt.id,
                                                            label: opt.option_label,
                                                        }))}
                                                        placeholder={column.name}
                                                        defaultLabel={column.name}
                                                        islabel="no"
                                                        onKeyDown={e => handleKeyDown(e, totalRef)}
                                                        showborder={false}
                                                        isDisabled={matchedField?.is_able_to_edit ? false : true}
                                                    />
                                                </div>
                                            </td>
                                        ): "";
            }
        };

        return (
            <>
                <td>{index + 1}</td>
                {tableHeader?.length > 0 && tableHeader?.map(item => renderCell(item))}
            </>
        );
    }
);

export default DeliveryItem;
