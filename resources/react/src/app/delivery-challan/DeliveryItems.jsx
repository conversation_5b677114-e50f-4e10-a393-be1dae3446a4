import React, { memo, useCallback, useContext, useEffect, useRef, useState } from "react";
import { Container, Form } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { FormInput } from "../../components/ui/Input";
import { StateContext } from "../../context/StateContext";
import { calculateTotal, customToFixed, getCalculatedQuantity, updateCustomFieldCalculation } from "../../shared/calculation";
import useDropdownOption from "../../shared/dropdownList";
import { negativeStock, rearrangeItemList } from "../../store/configuration/configurationSlice";
import {
    fetchItemList,
    fetchItemListForScan,
    fetchSingleItemById,
    getSingleItemById,
} from "../../store/item/itemSlice";
import AddItemModal from "../modal/Item/ItemModal";
import DeliveryItem from "./DeliveryItem";
import AddConsolidatingModal from "../modal/Item/ConsolidatingModal";
import NegativeStockModal from "../modal/Item/NegativeStockModel";
import DocumentModal from "../common/DocumentModal";
import { addToast, errorToast } from "../../store/actions/toastAction";
import { TABLE_HEADER_TYPE, toastType, TRANSACTION_TYPE, transactionTypeMap } from "../../constants";
import { addItemOnTheFly, checkPathName } from "../../shared/sharedFunction";
import { unitOption } from "../../shared/prepareData";
import ClassificationModal from "../modal/Classification/ClassificationModal";
import CustomFieldWithOneQty from "../modal/Item/CustomFieldWithOneQty";
import CustomFieldWithMultiQty from "../modal/Item/CustomFieldWithMultiQty";

const DeliveryItems = memo(({ items, setItems, tableHeader, shipping_address_type }) => {
    const dispatch = useDispatch();
    const { configuration, item, company } = useSelector(state => state);
    const saleConfiguration = configuration?.configuration;
    const { itemOption, itemUnitOption, classificationOptions } = useDropdownOption();
    const {
        isItemModel,
        openItemModel,
        closeItemModel,
        openClassificationModel,
        openQuantityModel,
        isNegativeStock,
        openIsNegativeStockModel,
        isOpenQuantityModel,
        closeIsNegativeStockModel,
        closeQuantityModel,
        classificationType,
        changeTax,
        itemType,
        setGrandTotal,
        additionalCharges,
        setAdditionalCharges,
        setIsEditCalculation,
        itemOnFly,
        setItemType,
        setisFieldsChanges,
        isClassificationModel,
        closeClassificationModel,
        classification,
        setClassification,
        gstQuote,
        isManageCustomItemMaster,
        setIsManageCustomItemMaster,
        isManageMultiQtyCustomItemMaster,
        setIsManageMultiQtyCustomItemMaster,
        storePriceListDetail,
        setStorePriceListDetail
    } = useContext(StateContext);
    const lineItem = useRef(null);
    const [itemIndex, setItemIndex] = useState(0);
    const [modelName, setModelName] = useState("");
    const [updatedId, setUpdatedId] = useState("");
    const [isChangeType, setIsChangeType] = useState(false);
    const [isRcsApplicable, setIsRcsApplicable] = useState(false);
    const [quantityId, setQuantityId] = useState("");
    const [noteCount, setNoteCount] = useState(0);
    const [isFocused, setIsFocused] = useState(false);
    const [noteTermsCount, setNoteTermsCount] = useState(0);
    const [isTermsFocused, setIsTermsFocused] = useState(false);
    const [scan, setScan] = useState([]);
    const [createNewItem, setCreateNewItem] = useState({
        name: "",
        index: null
    });

    const matchPathname = (route) =>{
        return window.location.pathname.includes(route);
    }

    useEffect(() => {
        if (createNewItem?.name) {
            addItemOnTheFly(dispatch, itemOnFly, createNewItem?.name, createNewItem?.index, items, setItems, setItemIndex);
        }
    }, [createNewItem])

    const getCustomFieldTransactionType = Object.keys(transactionTypeMap).find(path =>
        checkPathName(path)
    ) ? transactionTypeMap[Object.keys(transactionTypeMap).find(path => checkPathName(path))] : "";

    const handleFocus = () => setIsFocused(true);
    const handleBlur = () => setIsFocused(false);

    const handleTermsFocus = () => setIsTermsFocused(true);
    const handleTermsBlur = () => setIsTermsFocused(false);

    const totalQuantity = items.reduce(
        (acc, item) => parseFloat(acc) + parseFloat(item.quantity) || 0.0,
        0
    );

    const maxDecimalPlaces = Math.max(
        ...items
            .filter(item => item?.quantity?.toString()?.includes("."))
            .map(item => item.quantity.toString().split(".")[1]?.length || 0),
        0
    );

    const handleKeydown = e => {
        // Check if the forms exist
        const saleFormExists = items;
        if (!saleFormExists) return;

        // Check if the focus is on the button
        const isFocus = document.activeElement.classList.contains("barcode-print-focus-button");
        if (!isFocus) return;

        // Handle Enter key
        if (e.which === 10 || e.which === 13) {
            e.preventDefault();
        }

        // Handle scan logic
        if (scan.length > 0 && e.timeStamp - scan[scan.length - 1].timeStamp > 10) {
            setScan([]);
        }

        if (e.key === "Enter" && scan.length > 0) {
            const scannedString = scan.reduce(
                (scannedString, entry) => scannedString + entry.key,
                ""
            );
            setScan([]);
            document.dispatchEvent(new CustomEvent("scanBarcode", { detail: scannedString }));
            return;
        }

        if (e.key !== "Shift") {
            const data = {
                key: e.key,
                timeStamp: e.timeStamp,
                timeStampDiff: scan.length > 0 ? e.timeStamp - scan[scan.length - 1].timeStamp : 0,
            };
            setScan(prevScan => [...prevScan, data]);
        }
    };

    const handleScanBarcode = async e => {
        const itemSku = e.detail;
        let itemOptionDetail = [...itemOption];
        let search = itemSku;
        dispatch(fetchItemList({ search }));
        const updatedData = await dispatch(fetchItemListForScan({ search }, itemOptionDetail));
        try {
            let isAddItem = true;
            const itemList = [...items];
            const lastItemIndex = itemList?.length - 1;
            if (!itemSku) return;
            const matchedSkuItem = updatedData?.find(item => item?.sku === itemSku);
            dispatch(fetchSingleItemById(matchedSkuItem?.value));
            const isSkuExists = itemList.some(item => item?.sku === itemSku);
            const matchedSkuIndex = itemList.findIndex(item => item?.sku === itemSku);
            const matchedSku = updatedData.find(item => item?.sku === itemSku);
            if (!matchedSku) {
                return dispatch(
                    errorToast({
                        text: "Item with barcode not found.",
                        type: toastType.ERROR,
                    })
                );
            }
            if (itemList?.length == 1 && !itemList[0]?.selectedItem) {
                setUpdatedId(0);
                itemList[0].quantity = parseFloat(itemList[0].quantity) + 1;
            } else if (itemList[matchedSkuIndex]?.selectedItem && isSkuExists) {
                setUpdatedId(matchedSkuIndex);
                itemList[matchedSkuIndex].quantity = parseFloat(itemList[matchedSkuIndex].quantity) + 1;
            } else if (itemList[lastItemIndex]?.selectedItem && !isSkuExists) {
                setUpdatedId(lastItemIndex + 1);
                itemList[lastItemIndex + 1].quantity = parseFloat(itemList[lastItemIndex + 1].quantity) + 1;
            } else if (!itemList[lastItemIndex]?.selectedItem) {
                setUpdatedId(lastItemIndex);
                itemList[lastItemIndex].quantity = parseFloat(itemList[lastItemIndex].quantity) + 1;
            } else {
                setUpdatedId(lastItemIndex + 1);
            }
            const focusButton = document?.querySelector(".barcode-print-focus-button");
            if (focusButton) focusButton.focus();
        } catch (error) {
            console.error("Error fetching item:", error);
        }
    };

    useEffect(() => {
        // Add event listeners
        document.addEventListener("keydown", handleKeydown);
        document.addEventListener("scanBarcode", handleScanBarcode);

        // Clean up event listeners on unmount
        return () => {
            document.removeEventListener("keydown", handleKeydown);
            document.removeEventListener("scanBarcode", handleScanBarcode);
        };
    }, [scan, items]);
    useEffect(() => {
        const singleItem = item?.getSingleItemById[0];
        if (singleItem && !isChangeType) {
            const model = singleItem?.item_master?.model;
            const unitDetail = singleItem?.unit_of_array;
            const itemsList = [...items];

            itemsList[updatedId] = (() => {
                const itemDetail = items[updatedId];
                const unitOptionDetail = unitOption(unitDetail);
                    const custom_fields = items[updatedId]?.id === updatedId+1 ? updateCustomFieldCalculation(singleItem?.custom_fields_item_values) : itemsList[updatedId]?.custom_fields?.length > 0 ? updateCustomFieldCalculation(itemsList[updatedId]?.custom_fields) : updateCustomFieldCalculation(singleItem?.custom_fields_item_values)
                    const model_inventory_custom_fields = singleItem?.model_inventory_custom_fields
                    const mergedCustomFields = updateCustomFieldCalculation(custom_fields)
                    const model_select_inventory_custom_fields = singleItem?.model_select_inventory_custom_fields
                    const quantityFromCustomField = getCalculatedQuantity(mergedCustomFields);
                    const finalQuantity =
                                    quantityFromCustomField != null
                                        ? parseFloat(quantityFromCustomField)?.toFixed(
                                              model?.decimal_places ?? 2
                                          )
                                        : model_select_inventory_custom_fields?.length > 0
                                            ? parseFloat(0).toFixed(model?.decimal_places ?? 2)
                                            : parseFloat(model_inventory_custom_fields?.length > 0 ? 0 : 1).toFixed(model?.decimal_places ?? 2);
                const calculatedData = {
                    ...itemsList[updatedId],
                    quantity: finalQuantity,
                    updatedRateWithoutGst: parseFloat(
                                model?.selling_price_without_gst || 0
                            )?.toFixed(model?.decimal_places_for_rate ?? 2),
                    discountType: model?.discount_type || 1,
                    discountValue: model?.discount_value || 0,
                    discountType_2: 1,
                    discountValue_2: 0,
                    gst_copy: model?.gst_tax?.tax_rate,
                    decimal_places: model?.decimal_places || 2,
                    decimal_places_for_rate: model?.decimal_places_for_rate || 2,
                    gst: model?.gst_tax?.tax_rate,
                    gst_id: model?.gst_tax?.id,
                    cessRate: model?.gst_gst_cess_rate?.rate,
                    total: model?.selling_price_without_gst,
                };
                const calculatedTotal = calculateTotal(
                    calculatedData,
                    false,
                    changeTax,
                    itemType === "accounting"
                );
                return {
                    ...itemDetail,
                    id: model?.id,
                    selectedItem: singleItem?.item_master?.id,
                    additional_description: model?.description,
                    multiQuantity: [1, 0, 0, 0],
                    sku: singleItem?.item_master?.sku,
                    selectedUnit: model?.unit_of_measurement?.id || itemDetail.selectedUnit,
                    quantity: finalQuantity,
                    itemUnitOption: unitOptionDetail,
                    decimal_places: model?.decimal_places ?? 2,
                    isShowDelete: true,
                    selectedLedger: model?.income_ledger_id,
                    mrp: model?.mrp || 0,
                    rateWithGst: model?.selling_price_with_gst,
                    rateWithoutGst: model?.selling_price_without_gst,
                    updatedRateWithGst: model?.purchase_price_with_gst,
                    updatedRateWithoutGst:  parseFloat(model?.selling_price_without_gst || 0)?.toFixed(model?.decimal_places_for_rate ?? 2),
                    gst_id: model?.gst_tax?.id || 0,
                    gst: model?.gst_tax?.tax_rate || 0,
                    gst_copy: model?.gst_tax?.tax_rate || 0,
                    discountType: model.discount_type || 1,
                    discountValue: model.discount_value || 0,
                    discountType_2: model.discount_type_2 || 1,
                    discountValue_2: model.discount_value_2 || 0,
                    total: calculatedTotal.total || 0,
                    cessRate: model?.gst_gst_cess_rate?.rate || 0,
                    cessValue: calculatedTotal.cess || 0,
                    conversationRate: model?.conversion_rate || null,
                    secondaryUnitOfMeasurement: model?.secondary_unit_of_measurement || null,
                    decimal_places_for_rate: model?.decimal_places_for_rate || 2,
                    sgstValue: calculatedTotal.sgst,
                    cgstValue: calculatedTotal.sgst,
                    itemType: model?.item_master?.item_type,
                    custom_fields: custom_fields,
                    model_custom_fields: model_inventory_custom_fields || [],
                    model_inventory_custom_fields: model_inventory_custom_fields || [],
                    model_select_inventory_custom_fields: model_select_inventory_custom_fields || [],
                };
            })();
            setItems(itemsList);
            setTimeout(() => {
                // dispatch(getSingleItemById(""));
            }, 2000);
        }
    }, [item?.getSingleItemById, setItems, updatedId, changeTax]);

    useEffect(() => {
        setItems(prev =>
            prev?.map(item => {
                const calculatedTotal = calculateTotal(
                    item,
                    false,
                    changeTax,
                    itemType === "accounting"
                );
                return {
                    ...item,
                    with_tax: changeTax ? 1 : 0,
                    rpu: item?.updatedRateWithGst,
                    total: calculatedTotal.total,
                };
            })
        );
    }, [changeTax, isNegativeStock]);

    const openQuantityModelHandler = id => {
        setQuantityId(id);
        openQuantityModel();
    };

    const handleOpenNegativeStock = () => {
        openIsNegativeStockModel();
    };

    const openNegativeModelHandler = (value, data) => {
        if(saleConfiguration?.item_table_configuration?.warn_on_negative_stock){
            dispatch(negativeStock(data?.selectedItem, data?.quantity, handleOpenNegativeStock));
        }
    };
console.log(items);
    const handleUpdateItem = useCallback(
        (id, updatedItem, index) => {
            setUpdatedId(index);
            setItems(prev =>
                prev?.map((item, i) =>
                    i === index
                        ? {
                            ...item,
                            ...updatedItem,
                            custom_fields: updatedItem?.custom_fields?.length > 0 ? updateCustomFieldCalculation(updatedItem?.custom_fields) : [],
                        }
                        : item
                )
            );
        },
        [setItems, item?.itemTableDetail]
    );

    const addItem = useCallback(() => {
        const newItem = {
            id: items?.length > 0 ? items[items?.length - 1].id + 1 : 1,
            selectedItem: null,
            quantity: 0,
            units: [],
            multiQuantity: [0, 0, 0, 0],
            itemUnitOption: null,
            isShowDelete: true,
            additional_description: "",
        };
        setItems(prev => [...prev, newItem]);
        setTimeout(() => {
            lineItem.current.blur();
        }, 200);
    }, [items, itemType, setItems]);

    const removeItem = useCallback(
        id => {
            setItems(prevItems => {
                const newItems = prevItems.filter(item => item.id !== id);
                return newItems;
            });
        },
        [classificationType, setItems, setGrandTotal]
    );

    const handleOpenSecond = useCallback((priceListDetail, index) => {
        // model_inventory_custom_fields
        const customFields = priceListDetail?.[0]?.model_inventory_custom_fields;
        const customFieldsForInventory = priceListDetail?.[0]?.model_select_inventory_custom_fields;
        const custom_field_type = priceListDetail?.[0]?.model_inventory_custom_fields[0]?.custom_field_type;
        if(customFieldsForInventory == null || customFieldsForInventory?.length == 0){
            if(saleConfiguration?.header?.is_change_gst_details && company?.company?.is_gst_applicable){
                openClassificationModel();
            }
        }else{
            if(custom_field_type == 1){
                setIsManageCustomItemMaster(true);
            }else{
                setIsManageMultiQtyCustomItemMaster(true);
            }
            setStorePriceListDetail(priceListDetail[0]);
            setItemIndex(index);
            setItems(prev => prev.map((item, i) => (i === index ? { ...item, custom_field_inventory: customFields, model_select_inventory_custom_fields: customFieldsForInventory } : item)));
        }
    }, [openClassificationModel, item?.getSingleItemById?.[0]]);

    const handleOpenInventoryDetail = useCallback((priceListDetail, index) => {
                const custom_field_type = priceListDetail?.model_custom_fields[0]?.custom_field_type;
                if (custom_field_type == 1) {
                    setIsManageCustomItemMaster(true);
                } else {
                    setIsManageMultiQtyCustomItemMaster(true);
                }
                setItemIndex(index);
                setStorePriceListDetail(priceListDetail);
    }, []);

    useEffect(() => {
        setNoteCount(additionalCharges?.note?.length || 0);
    }, [additionalCharges?.note]);

    const handleChangeNote = e => {
        const { value } = e.target;
        setNoteCount(value?.length);
        setAdditionalCharges({
            ...additionalCharges,
            note: e.target.value,
        });
    };

    useEffect(() => {
        setNoteTermsCount(additionalCharges?.terms_and_conditions?.length || 0);
    }, [additionalCharges?.terms_and_conditions]);

    const handleChangeTerms = e => {
        const { value } = e.target;
        setNoteTermsCount(value?.length);
        setAdditionalCharges({
            ...additionalCharges,
            terms_and_conditions: value,
        });
    };

    const openItemsModel = useCallback(
        (id, index) => {
            openItemModel();
            setItemIndex(index);
            setUpdatedId(index);
            if (id) {
                setModelName("Update Item");
            } else {
                setModelName("Add Item");
            }
        },
        [openItemModel]
    );

    const renderCell = item => {
        switch (item.header) {
            case TABLE_HEADER_TYPE.ITEM:
                return <th className="text-center max-w-100px">Item</th>;
            case TABLE_HEADER_TYPE.QUANTITY:
                return <th className="text-center">Quantity</th>;
            case TABLE_HEADER_TYPE.UOM:
                return <th className="text-center min-w-100px">UOM</th>;
            default:
                return (
                    <th key={item.id} className="text-center min-w-100px">
                        {item.header}
                    </th>
                );
        }
    };

    useEffect(() => {
        const tooltipTriggerList = [].slice.call(
            document?.querySelectorAll('[data-bs-toggle="tooltip"]')
        );
        tooltipTriggerList?.forEach(tooltipTriggerEl => {
            new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }, []);

    const uploadDocument = e => {
        const maxFiles = 15;
        const maxFileSize = 2 * 1024 * 1024;
        const fileInput = e.currentTarget;
        const validFiles = Array.from(fileInput?.files || []).filter(
            file => file?.size <= maxFileSize
        );

        // Ensure upload_document is an array
        const currentDocuments = additionalCharges?.upload_document || [];
        const currentFileCount = Array.isArray(currentDocuments)
            ? currentDocuments.filter(file => !file.is_uploaded_new).length
            : 0;
        const newFileCount = validFiles.length;
        const totalFileCount = currentFileCount + newFileCount;

        if (totalFileCount > maxFiles) {
            dispatch(
                errorToast({
                    text: "Please Enter Max 15 Documents.",
                    type: toastType.ERROR,
                })
            );
            e.currentTarget.value = "";
            return;
        } else if (validFiles.length < (fileInput?.files || []).length) {
            dispatch(
                errorToast({
                    text: "Please Select a file less than 2MB",
                    type: toastType.ERROR,
                })
            );
            e.target.value = "";
            return;
        } else {
            setAdditionalCharges(prevCharges => ({
                ...prevCharges,
                upload_document: [
                    ...(Array.isArray(prevCharges.upload_document)
                        ? prevCharges.upload_document.filter(file => !file.is_uploaded_new)
                        : []),
                    ...validFiles.map((file, index) => ({
                        original_url: "",
                        file: file,
                        id: currentFileCount + index + 1,
                        is_uploaded_new: true
                    })),
                ],
            }));
        }
    };

    const handleItemTypeChange = (e) =>{
        const {value} = e.target;
        const isMakeReturn = matchPathname("sale-returns") ? TRANSACTION_TYPE.SALE_RETURN
                : matchPathname("create-sale-return") ? TRANSACTION_TYPE.SALE_RETURN
                : matchPathname("sales-create") ? TRANSACTION_TYPE.SALE
                : matchPathname("sales") ? TRANSACTION_TYPE.SALE
                : matchPathname("import-documents/create") ? TRANSACTION_TYPE.PURCHASE
                : matchPathname("income-credit-notes") ? TRANSACTION_TYPE.INCOME_CREDIT_NOTE
                : matchPathname("income-debit-notes") ? TRANSACTION_TYPE.INCOME_DEBIT_NOTE
                : matchPathname("expense-credit-notes") ? TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE
                : matchPathname("expense-debit-notes") ? TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE
                : matchPathname("income-estimate-quote") ? TRANSACTION_TYPE.INCOME_ESTIMATE_QUOTE
                : matchPathname("purchase-order") ? TRANSACTION_TYPE.PURCHASE_ORDER
                : matchPathname('purchase-returns') ? TRANSACTION_TYPE.PURCHASE_RETURN
                : matchPathname('delivery-challan') ? TRANSACTION_TYPE.DELIVERY_CHALLAN
                : matchPathname('purchases') ? TRANSACTION_TYPE.PURCHASE
                : matchPathname('purchase-create') ? TRANSACTION_TYPE.PURCHASE
                : matchPathname('purchases-create') ? TRANSACTION_TYPE.PURCHASE
                : matchPathname('create-purchase-return') ? TRANSACTION_TYPE.PURCHASE
                : "";
        setItemType(value);
        if(value === "item"){
            dispatch(rearrangeItemList(isMakeReturn, 1));
        }else{
            dispatch(rearrangeItemList(isMakeReturn, 2));
        }
    }

    return (
        <>
            <Container fluid className="p-0 mt-5">
                <div className="content-wrapper py-6 px-lg-10 px-sm-8 px-6">
                    <div className="card">
                        <div className="items-details-card">
                            <div className="row justify-content-between mb-2 mx-0">
                                    <div className="col-sm-6 col-12 px-0">
                                        <h5 className="title-name">
                                            Item Details
                                        </h5>
                                    </div>
                                    <div className="col-sm-6 col-12 d-flex px-0">
                                        <div className="ms-auto d-flex">
                                            <div
                                                className="d-inline-block"
                                                data-bs-toggle="tooltip"
                                                data-bs-placement="bottom"
                                                title="Click here to use the barcode scanner to select items."
                                            >
                                                <button
                                                    type="button"
                                                    className="btn btn-sm btn-outline-primary barcode-print-focus-button"
                                                    disabled={itemType === "accounting"}
                                                >
                                                    <i className="fa-solid fa-barcode fs-16 text-primary"></i>
                                                </button>
                                            </div>
                                            <div className="form-check mx-2 mt-1 align-content-center">
                                                <label
                                                    className="form-label mb-0 cursor-pointer"
                                                    htmlFor="itemInvoice"
                                                >
                                                    Without Amount
                                                </label>
                                                <input
                                                    className="form-check-input"
                                                    id="itemInvoice"
                                                    checked={itemType === "without_amount"}
                                                    name="sales_item_type"
                                                    type="radio"
                                                    value="without_amount"
                                                    onChange={handleItemTypeChange}
                                                />
                                            </div>
                                            <div className="form-check ms-2 mt-1 align-content-center">
                                                <label
                                                    className="form-label mb-0 cursor-pointer"
                                                    htmlFor="accountingInvoice"
                                                >
                                                    With Amount
                                                </label>
                                                <input
                                                    className="form-check-input"
                                                    id="accountingInvoice"
                                                    name="sales_item_type"
                                                    type="radio"
                                                    value="item"
                                                    checked={itemType === "item"}
                                                    onChange={handleItemTypeChange}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <div className="item-container" style={{width: '1000px'}}>
                                <table className="table table-bordered sales-table">
                                    <thead className="table-header">
                                        <th>Sr No.</th>
                                        {tableHeader?.length > 0 &&
                                            tableHeader?.map(item => renderCell(item))}
                                        <th></th>
                                    </thead>
                                    <tbody>
                                        {items?.map((item, index) => (
                                            <tr>
                                                <DeliveryItem
                                                    itemCount={items?.length}
                                                    id={item.id}
                                                    index={index}
                                                    item={item}
                                                    ledgerId={gstQuote?.party_ledger_id}
                                                    onUpdate={handleUpdateItem}
                                                    removeItem={removeItem}
                                                    itemOption={itemOption}
                                                    tableHeader={tableHeader}
                                                    itemType={itemType}
                                                    handleOpenSecond={handleOpenSecond}
                                                    openItemModel={openItemsModel}
                                                    itemUnitOption={itemUnitOption}
                                                    saleConfiguration={saleConfiguration}
                                                    openQuantityModelHandler={
                                                        openQuantityModelHandler
                                                    }
                                                    openNegativeModelHandler={
                                                        openNegativeModelHandler
                                                    }
                                                    setCreateNewItem={setCreateNewItem}
                                                    setisFieldsChanges={setisFieldsChanges}
                                                    setIsEditCalculation={setIsEditCalculation}
                                                    getCustomFieldTransactionType={getCustomFieldTransactionType}
                                                    handleOpenInventoryDetail={handleOpenInventoryDetail}
                                                />
                                                <td
                                                    className="pe-0"
                                                    style={{
                                                        maxWidth: "60px",
                                                        width: "60px",
                                                    }}
                                                >
                                                    <div className="delete-item w-100">
                                                        {item.isShowDelete === true &&
                                                        items.length !== 1 ? (
                                                            <button
                                                                type="button"
                                                                className="bg-transparent border-0 p-0 ps-3 w-100"
                                                                data-id="2"
                                                                onClick={() => removeItem(item.id)}
                                                            >
                                                                <i className="fas fs-2 fa-trash-alt text-danger mx-auto mt-2"></i>
                                                            </button>
                                                        ) : (
                                                            ""
                                                        )}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                        <tr>
                                            <td style={{ width: "50px" }}>{items?.length + 1}</td>
                                            <td>
                                                <button
                                                    type="button"
                                                    className="btn-sm btn-icon btn-icon-primary mt-2 add-item-btn"
                                                    ref={lineItem}
                                                    onClick={addItem}
                                                >
                                                    {"Line Item"}
                                                </button>
                                            </td>
                                            {Array.from({
                                                length: tableHeader?.length - 1,
                                            })?.map((_, i) => {
                                                if (
                                                    tableHeader[i + 1]?.show_total
                                                ) {
                                                    const currentHeader = tableHeader[i + 1];
                                                    const fieldKey = currentHeader?.custom_field_id;
                                                    const total = items.reduce((acc, item) => {
                                                        const value =
                                                            parseFloat(
                                                                item?.custom_fields?.find(
                                                                    item =>
                                                                        item.custom_field_id ==
                                                                        fieldKey
                                                                )?.value
                                                            ) || 0;
                                                        return acc + value;
                                                    }, 0);
                                                    return (
                                                        <td key={i} className="fs-5 text-end">
                                                            {" "}
                                                            <div className="pe-3">
                                                                {customToFixed(total, 2)}
                                                            </div>
                                                        </td>
                                                    );
                                                }
                                                if (
                                                    (tableHeader[i + 1]?.header ||
                                                        tableHeader[i + 2]?.header) ===
                                                    TABLE_HEADER_TYPE.QUANTITY
                                                ) {
                                                    return (
                                                        <td key={i} className="fs-5 text-end">
                                                            <div className="overflow-auto whitespace-nowrap pe-3">
                                                                {customToFixed(totalQuantity, maxDecimalPlaces)}
                                                            </div>
                                                        </td>
                                                    );
                                                }
                                                return <td key={i}></td>;
                                            })}
                                            <td className="fs-5 text-end">
                                                <div className="overflow-auto whitespace-nowrap">
                                                    {/* {customToFixed(
                                                        totalQuantity,
                                                        maxDecimalPlaces
                                                    )} */}
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
            <Container fluid className="p-0 mt-5">
                <div className="content-wrapper py-6 px-lg-10 px-sm-8 px-6">
                    <div className="row additional-info">
                        <div
                            className={
                                saleConfiguration?.footer?.is_enable_narration
                                    ? "col-6 mb-8"
                                    : "col-12 mb-8"
                            }
                        >
                            {saleConfiguration?.footer?.is_enabled_terms_and_conditions ? (
                                <Form.Group className="position-relative form-floating-group">
                                    <textarea
                                        className="form-control floating-label-input"
                                        placeholder=""
                                        value={additionalCharges?.terms_and_conditions?.slice(0, 5000)}
                                        maxLength={5000}
                                        onChange={handleChangeTerms}
                                        onFocus={handleTermsFocus}
                                        onBlur={handleTermsBlur}
                                    ></textarea>
                                    {isTermsFocused && (
                                        <p
                                            className="position-absolute bottom-0 right-0 mb-0 end-0 fs-12 pt-1 bootstrap-maxlength badge text-primary limit-textarea"
                                            style={{ whiteSpace: "nowrap" }}
                                        >
                                            {Math.max(5000 - (noteTermsCount ?? 0), 0)}
                                        </p>
                                    )}
                                    <Form.Label htmlFor="terms_conditions">
                                        Terms & Conditions
                                    </Form.Label>
                                </Form.Group>
                            ) : null}
                        </div>
                        {saleConfiguration?.footer?.is_enable_narration ? (
                            <div
                                className={`${
                                    saleConfiguration?.footer?.is_enabled_terms_and_conditions
                                        ? "col-6"
                                        : "col-12"
                                } mb-8`}
                            >
                                <Form.Group className="position-relative form-floating-group">
                                    <textarea
                                        className="form-control floating-label-input"
                                        placeholder=""
                                        value={additionalCharges?.note}
                                        onChange={handleChangeNote}
                                        maxLength={5000}
                                        onFocus={handleFocus}
                                        onBlur={handleBlur}
                                    ></textarea>
                                    {isFocused && (
                                        <p
                                            className="position-absolute bottom-0 right-0 mb-0 end-0 fs-12 pt-1 bootstrap-maxlength badge text-primary limit-textarea"
                                            style={{ whiteSpace: "nowrap" }}
                                        >
                                            {5000 - noteCount || 0}
                                        </p>
                                    )}
                                    <Form.Label htmlFor="note">Note</Form.Label>
                                </Form.Group>
                            </div>
                        ) : null}
                        <div className="mb-8">
                            <Form.Group className="position-relative form-floating-group">
                                <FormInput
                                    className="floating-label-input pt-3 file-upload-validate"
                                    data-bs-toggle="tooltip"
                                    data-bs-placement="bottom"
                                    title=""
                                    // name="sale_document"
                                    type="file"
                                    // id="sale_document"
                                    data-bs-original-title="Maximum file size is 2 MB."
                                    multiple
                                    aria-label="Maximum file size is 2 MB."
                                    onChange={uploadDocument}
                                    accept="image/jpg, image/jpeg, image/png, application/pdf, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, .docx"
                                />
                                <Form.Label className="upload-document" htmlFor="sale_document">
                                    Upload Document
                                </Form.Label>
                            </Form.Group>
                        </div>
                    </div>
                    <DocumentModal medias={additionalCharges?.upload_document} setAdditionalCharges={setAdditionalCharges} />
                </div>
                {isItemModel && (
                    <AddItemModal
                        show={isItemModel}
                        id={itemOption.length + 1}
                        handleClose={closeItemModel}
                        itemListdata={item}
                        name={modelName}
                        setModelName={setModelName}
                        index={itemIndex}
                        setIndex={setItemIndex}
                        items={items}
                        setItems={setItems}
                        isPurchase={false}
                    />
                )}
                {isOpenQuantityModel && (
                    <AddConsolidatingModal
                        show={isOpenQuantityModel}
                        handleClose={closeQuantityModel}
                        items={items}
                        setItems={setItems}
                        handleSubmit=""
                        quantityId={quantityId}
                        changeTax={changeTax}
                        itemType={itemType}
                        openNegativeModelHandler={openNegativeModelHandler}
                        setIsEditCalculation={setIsEditCalculation}
                    />
                )}
                {isNegativeStock && (
                    <NegativeStockModal
                        show={isNegativeStock}
                        handleClose={closeIsNegativeStockModel}
                        items={items}
                        setItems={setItems}
                        handleSubmit=""
                        quantityId={quantityId}
                        closeWarningModelHandler={closeIsNegativeStockModel}
                        updatedId={updatedId}
                    />
                )}
                {isClassificationModel && (
                    <ClassificationModal
                        show={isClassificationModel}
                        handleClose={closeClassificationModel}
                        classificationOptions={classificationOptions}
                        classification={classification}
                        setClassification={setClassification}
                        isRcsApplicable={isRcsApplicable}
                    />
                )}
                {isManageCustomItemMaster && <CustomFieldWithOneQty
                    show={isManageCustomItemMaster}
                    handleClose={() => setIsManageCustomItemMaster(false)}
                    openClassificationModel={openClassificationModel}
                    customFieldWithOneQty={storePriceListDetail}
                    items={items}
                    setItems={setItems}
                    itemIndex={itemIndex}
                    isInWord={false}
                />}
                {isManageMultiQtyCustomItemMaster && <CustomFieldWithMultiQty
                    show={isManageMultiQtyCustomItemMaster}
                    handleClose={() => setIsManageMultiQtyCustomItemMaster(false)}
                    openClassificationModel={openClassificationModel}
                    customFieldWithMultiQty={storePriceListDetail}
                    items={items}
                    setItems={setItems}
                    itemIndex={itemIndex}
                    isInWord={false}
                    changeTax={changeTax}
                />}
            </Container>
        </>
    );
});

export default DeliveryItems;
