import { useEffect } from "react";

export const useTransactionShortcuts = (formRef) => {
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.altKey) {
        switch (e.keyCode) {
          case 83:
            e.preventDefault();
            formRef?.current?.querySelector('[name="submitType"][value="save"]')?.click();
            break;
          case 78:
            e.preventDefault();
            formRef?.current?.querySelector('[name="submitType"][value="saveAndNew"]')?.click();
            break;
          case 80:
            e.preventDefault();
            formRef?.current?.querySelector('[name="submitType"][value="saveAndPrint"]')?.click();
            break;
          case 113:
            if (typeof formRef === 'string' && formRef.trim() !== '') {
                $(`#${formRef}`).trigger("click");
            }
            break;
          default:
            break;
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [formRef]);
};

export const handleShortcutKeys = handleKeyPress => {
    useEffect(() => {
        // attach the event listener
        window.addEventListener("keydown", handleKeyPress);
        // remove the event listener
        return () => {
            window.removeEventListener("keydown", handleKeyPress);
        };
    }, [handleKeyPress]);
};
