import React, { Suspense, useContext, useEffect } from "react";
import * as Sentry from "@sentry/react";
import "react-phone-input-2/lib/style.css";
import { Route, BrowserRouter as Router, Routes, useNavigate } from "react-router-dom";
import "react-toastify/dist/ReactToastify.css";
import "./assets/css/index.css";
import Loader from "./shared/loader";
import { routes } from "./routes";
import { useDispatch, useSelector } from "react-redux";
import { StateContext } from "./context/StateContext";
import { partyDetail } from "./store/ledger/ledgerSlice";
import { getShippingAddressList } from "./store/shippingAddress/shippingAddressSlice";

Sentry.init({
    dsn: process.env.MIX_SENTRY_DSN,
    integrations: [Sentry.browserTracingIntegration(), Sentry.replayIntegration()],
    // Tracing
    tracesSampleRate: 1.0, //  Capture 100% of the transactions
    // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
    // tracePropagationTargets: ["https://hisabkitab-react.infyom.com"],
    // Session Replay
    replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
    replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
});

function NavigationHandler() {
    const navigate = useNavigate();

    useEffect(() => {
        window.ReactNavigate = (url) => {
            navigate(url);
        };
    }, [navigate]);

    return null;
}

function MainApp() {
    const dispatch = useDispatch();
    const { company } = useSelector(state => state.company);

    const {
        setItems,
        defaultItem,
        setBrokerDetail,
        defaultBrokerDetail,
        setAccountingItems,
        defaultAccountingItem,
        setTransporterDetail,
        defaultTransporterDetail,
        setGstQuote,
        defaultGstQuote,
        setAdditionalCharges,
        defaultAdditionalCharges,
        setInvoiceDetail,
        defaultInvoiceDetail,
        setGstValue,
        defaultGst,
        setShippingAddress,
        setTcsRate,
        defaultTcsRate,
        setGstCalculation,
        defaultGstCalculation,
        setPaymentLedgerDetail,
        defaultPaymentDetail,
        setClassification,
        defaultClassification,
        setOtherDetail,
        defaultOtherDetail,
        setPartyAddress,
        defaultPartyAddress,
        setAdditionalGst,
        setAddLessChanges,
        defaultAddLessChanges,
        setFinalAmount,
        setTaxableValue,
        setCessValue,
        setGrandTotal,
        setSameAsBill,
        defaultRecurringInvoiceDetails,
        setRecurringInvoiceDetails,
        setConfigurationTableList,
        setConfigurationFooterList,
        setConfigurationHeaderList
    } = useContext(StateContext);

    let currentURL = window.location.href;

    function companySetting() {
        $('#kt_body').attr('data-kt-aside-minimize', 'on');
    }

    useEffect(() => {
        if (!currentURL.includes("dashboard")) {
            companySetting();
        }
    },[currentURL]);

    useEffect(() => {
        if(company?.length !== 0){
            Sentry.setUser({ username:company?.user?.full_name, email: company?.user?.email, user: company?.user});
        }
    },[company])

    const path = window.location.pathname.toLowerCase();

    const handleMenuHighlight = () => {

        // Clear main menu highlights
        document.getElementById("incomeMainMenu")?.classList.remove("active");
        document.getElementById("purchaseMainMenu")?.classList.remove("active");
        document.getElementById("ledgerMainMenu")?.classList.remove("active");
        document.getElementById("itemMainMenu")?.classList.remove("active");
        if(!currentURL.includes("dashboard")){
            const asideToggle = document.getElementById("kt_aside_toggle");
            const menuItemContainer = asideToggle?.closest(".menu-item");
            const menuLink = menuItemContainer?.querySelector(".menu-link.active");
            menuLink?.classList.remove("active");
        }


        // Clear all create button highlights
        const allButtonIds = [
            "deliveryCreateButtonReact",
            "estimateCreateButtonReact",
            "saleReturnCreateButtonReact",
            "incomeDebitCreateButtonReact",
            "incomeCreditCreateButtonReact",
            "expenseCreditCreateButtonReact",
            "expenseDebitCreateButtonReact",
            "purchaseOrderCreateButtonReact",
            "purchaseReturnCreateButtonReact",
            "salesCreateButtonReact",
            "purchaseCreateButtonReact",
            "itemCreateButtonReact",
            "ledgerCreateButtonReact",
            "recurringInvoiceCreateButtonReact",
            "dashboardButtonReact"
        ];
        allButtonIds.forEach(id => {
            document.getElementById(id)?.classList.remove("active");
        });

        // Exact path segment to button ID map (longer keys first)
        const pathToButtonMap = [
            ["expense-debit-notes", "expenseDebitCreateButtonReact"],
            ["expense-credit-notes", "expenseCreditCreateButtonReact"],
            ["income-debit-notes", "incomeDebitCreateButtonReact"],
            ["income-credit-notes", "incomeCreditCreateButtonReact"],
            ["purchase-returns", "purchaseReturnCreateButtonReact"],
            ["create-purchase-return", "purchaseReturnCreateButtonReact"],
            ["purchase-order", "purchaseOrderCreateButtonReact"],
            ["income-estimate-quote", "estimateCreateButtonReact"],
            ["sale-returns", "saleReturnCreateButtonReact"],
            ["create-sale-return", "saleReturnCreateButtonReact"],
            ["delivery-challan", "deliveryCreateButtonReact"],
            ["sales", "salesCreateButtonReact"],
            ["purchase-sale", "salesCreateButtonReact"],
            ["purchase", "purchaseCreateButtonReact"],
            ["item-masters", "itemCreateButtonReact"],
            ["ledgers", "ledgerCreateButtonReact"],
            ["recurring-invoices", "recurringInvoiceCreateButtonReact"],
            ["dashboard", "dashboardButtonReact"],
        ];

        // Highlight correct create button based on exact matching
        for (const [segment, buttonId] of pathToButtonMap) {
            if (path.includes(segment)) {
                document.getElementById(buttonId)?.classList.add("active");
                break; // Stop after first exact match
            }
        }

        // Main menu highlighting
        if (
            (path.includes("purchase") && !path.includes("purchase-sale")) ||
            path.includes("expense") ||
            path.includes("purchase-order") ||
            path.includes("purchase-returns")
        ) {
            document.getElementById("purchaseMainMenu")?.classList.add("active");
        }

        if (path.includes("ledger")) {
            document.getElementById("ledgerMainMenu")?.classList.add("active");
        }

        if (path.includes("item-masters")) {
            document.getElementById("itemMainMenu")?.classList.add("active");
        }

        if (
            path.includes("income") ||
            path.includes("sale") ||
            path.includes("sales") ||
            path.includes("estimate") ||
            path.includes("delivery-challan") ||
            path.includes("recurring-invoices")
        ) {
            document.getElementById("incomeMainMenu")?.classList.add("active");
        }
    };

    useEffect(() => {
        handleMenuHighlight();
        setGstQuote(defaultGstQuote);
        dispatch(partyDetail(""));
        dispatch(getShippingAddressList([]));
        setItems(defaultItem);
        setBrokerDetail(defaultBrokerDetail);
        setAccountingItems(defaultAccountingItem);
        setTransporterDetail(defaultTransporterDetail);
        setAdditionalCharges(defaultAdditionalCharges);
        setInvoiceDetail(defaultInvoiceDetail);
        setGstValue(defaultGst);
        // setShippingAddress([]);
        setTcsRate(defaultTcsRate);
        setGstCalculation(defaultGstCalculation);
        setPaymentLedgerDetail(defaultPaymentDetail);
        setClassification(defaultClassification);
        setOtherDetail(defaultOtherDetail);
        setPartyAddress(defaultPartyAddress);
        setAdditionalGst(0);
        setAddLessChanges(defaultAddLessChanges);
        setRecurringInvoiceDetails(defaultRecurringInvoiceDetails);
        setFinalAmount(0);
        setTaxableValue(0);
        setGrandTotal(0);
        setCessValue(0);
        setSameAsBill(false);
        setConfigurationTableList([]);
        setConfigurationFooterList([]);
        setConfigurationHeaderList([]);
    }, [path]);

    useEffect(() => {
        const clearCacheWithoutReload = async () => {
            if ("caches" in window) {
                const cacheNames = await caches.keys();
                await Promise.all(cacheNames.map(cache => caches.delete(cache)));
            }
        };

        const hasClearedCache = sessionStorage.getItem("hasClearedCache");
        if (!hasClearedCache) {
            clearCacheWithoutReload();
            sessionStorage.setItem("hasClearedCache", "true");
        }
    }, []);

    return (
        <Suspense fallback={<Loader />}>
            <Router>
                <NavigationHandler />
                <Routes>
                    {routes.map(route => (
                        <Route key={route.path} path={route.path} element={<route.Element />} />
                    ))}
                </Routes>
            </Router>
        </Suspense>
    );
}

export default MainApp;
