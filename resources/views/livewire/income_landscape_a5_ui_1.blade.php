<style>
    .pdf-a5-landscape-preview {
        * {
            margin: 0;
            padding: 0;
            text-indent: 0;
            font-family: "Arial";
            font-size: 13px;
            font-weight: 400;
            box-sizing: border-box;
        }
        @page {
            margin: 20px;
        }
        h1 {
            font-size: 24px;
        }
        @font-face {
            font-family: "Arial";
            src: url(/public/fonts/Arial.ttf) format(truetype);
            font-style: normal;
            font-weight: 400;
            font-display: swap;
        }
        @font-face {
            font-family: "Arial";
            src: url(/public/fonts/arialbd.ttf) format(truetype);
            font-style: normal;
            font-weight: 600;
            font-display: swap;
        }
        .main-table {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
            box-sizing: border-box;
            border: 2px solid #A9A9A9;
            color: #181C32;
        }
        table {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }
        td {
            vertical-align: top;
        }
        .text-primary {
            color: #4f158c;
        }
        .fs-14 {
            font-size: 14px;
        }
        .fs-11 {
            font-size: 11px;
        }
        .fw-6 {
            font-weight: 600;
        }
        .fw-7 {
            font-weight: 700;
        }
        .whitespace-nowrap {
            white-space: nowrap;
        }
        .border-bottom {
            border-bottom: 1px solid #A9A9A9 !important;
        }
        .border-right {
            border-right: 1px solid #A9A9A9 !important;
        }
        .border-top {
            border-top: 1px solid #A9A9A9 !important;
        }
        .border-left {
            border-left: 1px solid #A9A9A9;
        }
        .vertical-top {
            vertical-align: top;
        }
        .vertical-middle {
            vertical-align: middle;
        }
        .vertical-bottom {
            vertical-align: bottom;
        }
        .text-center {
            text-align: center !important;
        }
        .text-start {
            text-align: left !important;
        }
        .text-end {
            text-align: right !important;
        }
        .text-black {
            color: #181C32 !important;
        }
        .signature {
            max-width: 200px;
            height: 50px;
        }
        .qr-code {
            width: 75px;
            height: 75px;
            min-width: 75px;
        }
        .px-8 {
            padding-left: 8px !important;
            padding-right: 8px !important;
        }
        .pt-2 {
            padding-top: 2px !important;
        }
        .pb-2 {
            padding-bottom: 2px !important;
        }
        .pt-4 {
            padding-top: 4px !important;
        }
        .pb-4 {
            padding-bottom: 4px !important;
        }
        .my-2 {
            margin-top: 3.5rem !important;
            margin-bottom: 0.5rem !important;
        }
    }
</style>
<div class="pdf-a5-landscape-preview">
    <div class="main-table">
        <h2 class="pt-4 pb-2 mb-0 text-center company-name-font-size company-font-customization">
            {{ strtoupper($currentCompany->trade_name) }}
        </h2>
        @if(($invoiceSetting['prop_details'] ?? true) && isset($customProp))
            <p class="mb-0 text-center company-address-font-size">{{ $customProp->label_name.' : '.$customProp->label_value }}</p>
        @endif
        <p class="mb-0 text-center company-address-font-size">
            {{ strtoupper($companyBillingAddress->address_1 ?? null) }},
            {{ strtoupper($companyBillingAddress->address_2 ?? null) }},
            {{ strtoupper(getCityName($companyBillingAddress->city_id ?? null)) }},
            {{ strtoupper(getStateName($companyBillingAddress->state_id ?? null)) }},
            {{ strtoupper(getCountryName($companyBillingAddress->country_id ?? null)) }},
            {{ $companyBillingAddress->pin_code ?? null }}
        </p>
        @if ($invoiceSetting['email'] ?? true)
            <a class="text-center text-black company-address-font-size">
                {{ $alternate_email ?? ($currentCompany->user->email ?? null) }}
            </a>
        @endif
        @foreach (printCustomPDFLabelsForSale() as $key => $customLabel)
            <div class="text-center">
                <span class="company-address-font-size" style="{{ $loop->first ? 'margin-right:25px;' : '' }}">
                    {{ $key ?? null }}: {{ $customLabel ?? null }}
                </span>
            </div>
        @endforeach
        <div class="text-center">
            @if ($isCompanyGstApplicable)
                <span class="company-address-font-size" style="margin-right:25px;">
                    {{ $changeLabel['gstin'] ?? 'GSTIN' }}: {{ '  ' . $currentCompany->companyTax->gstin ?? null }}
                </span>
            @endif
            @if ($invoiceSetting['mobile_number'] ?? true)
                <span class="company-address-font-size">
                    {{ $changeLabel['tel'] ?? 'Phone no.' }} :
                    {{ (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') .
                    (isset($invoiceSetting['alternate_phone']) ? $invoiceSetting['alternate_phone'] : '+' . $currentCompany->user->region_code . ' ' . $currentCompany->user->phone) .
                    (isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2']) ? ', +' . $invoiceSetting['region_code_2'] . ' ' : '') .
                    (isset($invoiceSetting['alternate_phone_2']) ? (isset($invoiceSetting['region_code_2']) ? '' : ', ') . $invoiceSetting['alternate_phone_2'] : '') }}
                </span>
            @endif
        </div>
        <div class="px-3 border-bottom">
            <table class="table mb-0">
                <tr>
                    <td class="px-8 pt-4 pb-4 fs-11 text-uppercase fw-7">original </td>
                    <td class="px-8 pt-4 pb-4 fs-11 text-uppercase fw-7 text-end">tax invoice</td>
                </tr>
            </table>
        </div>
        <div class="row border-bottom">
            <div class="px-8 pt-4 pb-4 col vertical-top">
                <h4 class="mb-0 fw-6 header-contents-font-size">
                    shruti
                </h4>
                @if($isCompanyGstApplicable)
                    <p class="mb-0 header-contents-font-size">
                        GSTIN : 24AADCD6XXXXXXX
                    </p>
                @endif
                <p class="m-0 header-contents-font-size">
                    602, Empire State Building, Ring Rd, near Udhna Darwaja, Maan Darwaja, Aman Nagar,
                    Surat,
                    Gujarat
                    39500
                </p>
                <p class="mb-0 header-contents-font-size">
                    ph.: 7600010516
                </p>
            </div>
            @if ($invoiceSetting['ship_to_details'] ?? true)
                <div class="px-8 pt-4 pb-4 col vertical-top">
                    <h4 class="mb-0 fw-6 header-contents-font-size">
                        shruti
                    </h4>
                    @if($isCompanyGstApplicable)
                        <p class="mb-0 header-contents-font-size">
                            GSTIN : 24AADCD6XXXXXXX
                        </p>
                    @endif
                    <p class="m-0 header-contents-font-size">
                        602, Empire State Building, Ring Rd, near Udhna Darwaja, Maan Darwaja, Aman Nagar,
                        Surat,
                        Gujarat
                        39500
                    </p>
                    <p class="mb-0 header-contents-font-size">
                        ph.: 7600010516
                    </p>
                </div>
            @endif
            @if (($invoiceSetting['dispatch_from_details'] ?? false) && isset($companyShippingAddress))
                <div class="px-8 pt-4 pb-4 col vertical-top">
                    <p class="m-0 header-contents-font-size">
                        {{ isset($companyShippingAddress->address_1) ? strtoupper($companyShippingAddress->address_1 .',') : null }}
                        {{ isset($companyShippingAddress->address_2) ? strtoupper($companyShippingAddress->address_2 .',') : null }}
                        {{ isset($companyShippingAddress->city_id) ?  strtoupper(getCityName($companyShippingAddress->city_id).',') : null }}
                        {{ isset($companyShippingAddress->state_id) ? strtoupper(getStateName($companyShippingAddress->state_id).',') : null }}
                        {{ isset($companyShippingAddress->country_id) ? strtoupper(getCountryName($companyShippingAddress->country_id).',') : null }}
                        {{ $companyShippingAddress->pin_code ?? null }}
                    </p>
                </div>
            @endif
            <div class="px-8 pt-4 pb-4 col vertical-top">
                <table class="table">
                    <tr>
                        <td class="p-0 fw-7 header-contents-font-size">
                            {{ $changeLabel['invoice_number'] ?? 'Invoice no' }}:
                        </td>
                        <td class="p-0 header-contents-font-size">
                            MA22/2348
                        </td>
                    </tr>
                    <tr>
                        <td class="p-0 fw-7 header-contents-font-size">
                            {{ $changeLabel['invoice_date'] ?? 'Invoice Date' }}:
                        </td>
                        <td class="p-0 header-contents-font-size">
                            24-08-2023
                        </td>
                    </tr>
                    @if ($invoiceSetting['po_number'] ?? true)
                        <tr>
                            <td class="p-0 fw-7 header-contents-font-size">
                                {{ $changeLabel['po_number_label'] ?? 'PO No.' }}:
                            </td>
                            <td class="p-0 header-contents-font-size">
                                123123123
                            </td>
                        </tr>
                    @endif
                    @if ($invoiceSetting['show_po_date'] ?? true)
                        <tr>
                            <td class="p-0 fw-7 header-contents-font-size">
                                {{ $changeLabel['po_date'] ?? 'PO Date' }}:
                            </td>
                            <td class="p-0 header-contents-font-size">
                                24-08-2023
                            </td>
                        </tr>
                    @endif
                    <tr>
                        <td class="p-0 fw-7 header-contents-font-size">
                            Delivery Challan:
                        </td>
                        <td class="p-0 header-contents-font-size">
                            5485665846
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        @if ($isCompanyGstApplicable || ($invoiceSetting['transport_details'] ?? true))
            <div class="row border-bottom">
                @if($isCompanyGstApplicable)
                    <div class="px-8 pt-4 pb-4 col vertical-top w-25">
                        <table class="table">
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size whitespace-nowrap">
                                    {{ $changeLabel['ack_no'] ?? 'Ack No' }}:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    162314701183939
                                </td>
                            </tr>
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size whitespace-nowrap">
                                    {{ $changeLabel['ack_date'] ?? 'Ack Date' }}:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    18-08-2023
                                </td>
                            </tr>
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size whitespace-nowrap">
                                    {{ $changeLabel['irn'] ?? 'IRN' }}:
                                </td>
                                <td class="p-0 header-contents-font-size" style="word-break: break-all;">
                                    3183488b0385a8206fbe/308d82005218364351287e1cB0XXXXXXXXXXXXX
                                </td>
                            </tr>
                        </table>
                    </div>
                @endif
                @if ($invoiceSetting['transport_details'] ?? true)
                    <div class="px-8 pt-4 pb-4 col vertical-top">
                        <table class="table">
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size">
                                    {{ $changeLabel['transport_name'] ?? 'Transport Name' }}:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    Maharaj
                                </td>
                            </tr>
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size">
                                    GSTIN:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    24AADCD6XXXXXXX
                                </td>
                            </tr>
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size">
                                    {{ $changeLabel['document_no'] ?? 'Document No' }}:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    11
                                </td>
                            </tr>
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size">
                                    {{ $changeLabel['document_date'] ?? 'Document Date' }}:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    24-08-2023
                                </td>
                            </tr>
                        </table>
                    </div>
                @endif
                @if($isCompanyGstApplicable || ($invoiceSetting['transport_details'] ?? true))
                    <div class="px-8 pt-4 pb-4 col vertical-top">
                        <table class="table">
                            @if($isCompanyGstApplicable)
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        {{ $changeLabel['e_way_bill_no'] ?? 'E-way Bill No' }}:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        162314701183939
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        {{ $changeLabel['e_way_bill_date'] ?? 'E-way Bill Date' }}:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        18-08-2023
                                    </td>
                                </tr>
                            @endif
                            @if ($invoiceSetting['transport_details'] ?? true)
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        {{ $changeLabel['transport_vehicle_number'] ?? 'Vehicle No.' }}:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        GJ05ABXXXX
                                    </td>
                                </tr>
                            @endif
                        </table>
                    </div>
                @endif
            </div>
        @endif
        <table cellpadding="0" style="flex-grow: 1;">
            <tr class="border-bottom">
                <td class="px-8 pt-4 pb-4 text-center table-headings-font-size border-right fw-7">
                    {{ $changeLabel['sn'] ?? 'SN' }}
                </td>
                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap fw-7 w-100">
                    {{ $changeLabel['item_name'] ?? 'Item Name' }}
                </td>
                @if($isCompanyGstApplicable && ($showPrintSettings['show_sale_hsn_sac'] ?? true))
                    <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-start fw-7">
                        {{ $changeLabel['hsn_sac'] ?? 'HSN/SAC' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_mrp'] ?? true)
                    <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-center fw-7">
                        {{ $changeLabel['mrp'] ?? 'MRP' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_qty'] ?? true)
                    <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-center fw-7">
                        {{ $changeLabel['qty'] ?? 'Qty' }}
                    </td>
                @endif
                @if ($showPrintSettings['sale_uom_enable'] ?? false)
                    <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-center fw-7">
                        {{ $changeLabel['sale_uom_label'] ?? 'UOM' }}
                    </td>
                @endif
                @if ($isCompanyGstApplicable && ($showPrintSettings['show_sale_rate_with_gst'] ?? false))
                    <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-center fw-7">
                        {{ $changeLabel['rate_with_gst'] ?? 'Rate With GST' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_rate'] ?? true)
                    <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-center fw-7">
                        {{ $changeLabel['rate'] ?? 'Rate' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_discount'] ?? true)
                    <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                        {{ $changeLabel['discount'] ?? 'Dis.' }}
                    </td>
                @endif
                @if ($showPrintSettings['sale_dis_2_enable'] ?? true)
                    <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                        {{ $changeLabel['sale_dis_2_label'] ?? 'Dis. 2' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_total_discount'] ?? true)
                    <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                        {{ $changeLabel['total_discount'] ?? 'Total Dis.' }}
                    </td>
                @endif
                @php
                    $dynamicLastHeadings = collect([
                        ['key' => 'taxable_value', 'name' => $changeLabel['taxable_value'] ?? 'Taxable Value', 'show' => true],
                        ['key' => 'gst', 'name' => $changeLabel['gst'] ?? 'GST (%)', 'show' => $isCompanyGstApplicable && ($showPrintSettings['show_sale_gst'] ?? true)],
                        ['key' => 'gst_amount', 'name' => $changeLabel['gst_amount'] ?? 'GST Amt', 'show' => $isCompanyGstApplicable && ($showPrintSettings['show_sale_gst_amount'] ?? true)],
                        ['key' => 'total_amount', 'name' => $changeLabel['total_amount'] ?? 'Total Amount', 'show' => $isCompanyGstApplicable && ($showPrintSettings['show_sale_total_amount'] ?? true)],
                    ])->filter(fn($h) => $h['show']);
                @endphp
                @foreach($dynamicLastHeadings as $heading)
                    <td class="px-8 pt-4 pb-4 table-headings-font-size whitespace-nowrap text-end fw-7 {{ $loop->last ? '' : 'border-right' }}">
                        {{ $heading['name'] }}
                    </td>
                @endforeach
            </tr>
            <tr>
                <td class="px-8 pt-4 text-center table-contents-font-size border-right">
                    1
                </td>
                <td class="px-8 pt-4 table-contents-font-size border-right fw-6">
                    Adrian Bell
                    @if($invoiceSetting['show_item_sku'] ?? true)
                        <p class="description-font-size">Item Code: 89060105007812324</p>
                    @endif
                </td>
                @if($isCompanyGstApplicable && ($showPrintSettings['show_sale_hsn_sac'] ?? true))
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right"></td>
                @endif
                @if ($showPrintSettings['show_sale_mrp'] ?? true)
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        {{ getPrintPdfCurrencySymbol().'14.29' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_qty'] ?? true)
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        10
                        @if($showPrintSettings['show_sale_unit'] ?? true)
                            <span class="table-contents-font-size">PCS</span>
                        @endif
                    </td>
                @endif
                @if ($showPrintSettings['sale_uom_enable'] ?? false)
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        1
                        @if($showPrintSettings['show_sale_unit'] ?? false)
                            <span class="table-contents-font-size">Box</span>
                        @endif
                    </td>
                @endif
                @if ($isCompanyGstApplicable && ($showPrintSettings['show_sale_rate_with_gst'] ?? false))
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        {{ getPrintPdfCurrencySymbol().'15.00' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_rate'] ?? true)
                    <td class="px-8 pt-4 text-center table-contents-font-size border-right whitespace-nowrap">
                        {{ getPrintPdfCurrencySymbol().'14.29' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_discount'] ?? true)
                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                        0
                    </td>
                @endif
                @if ($showPrintSettings['sale_dis_2_enable'] ?? true)
                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                        0
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_total_discount'] ?? true)
                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                        0.00
                    </td>
                @endif
                @if ($isCompanyGstApplicable)
                    <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                        {{ getPrintPdfCurrencySymbol().'142.90' }}
                    </td>
                    @if ($showPrintSettings['show_sale_gst'] ?? true)
                        <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                            5
                        </td>
                    @endif
                    @if ($showPrintSettings['show_sale_gst_amount'] ?? true)
                        <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                            {{ getPrintPdfCurrencySymbol().'7.15' }}
                        </td>
                    @endif
                    @if ($showPrintSettings['show_sale_total_amount'] ?? true)
                        <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                            {{ getPrintPdfCurrencySymbol().'7.15' }}
                        </td>
                    @endif
                @else
                    <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end">
                        {{ getPrintPdfCurrencySymbol().'142.90' }}
                    </td>
                @endif
            </tr>
            <tr>
                <td class="px-8 pt-4 text-center table-contents-font-size border-right">
                    2
                </td>
                <td class="px-8 pt-4 table-contents-font-size border-right fw-6">
                    Saree
                </td>
                @if($isCompanyGstApplicable && ($showPrintSettings['show_sale_hsn_sac'] ?? true))
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        5407
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_mrp'] ?? true)
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        {{ getPrintPdfCurrencySymbol().'150.00' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_qty'] ?? true)
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        10
                        @if($showPrintSettings['show_sale_unit'] ?? true)
                            <span class="table-contents-font-size">PCS</span>
                        @endif
                    </td>
                @endif
                @if ($showPrintSettings['sale_uom_enable'] ?? false)
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        1
                        @if($showPrintSettings['show_sale_unit'] ?? false)
                            <span class="table-contents-font-size">Box</span>
                        @endif
                    </td>
                @endif
                @if ($isCompanyGstApplicable && ($showPrintSettings['show_sale_rate_with_gst'] ?? false))
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        {{ getPrintPdfCurrencySymbol().'157.50' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_rate'] ?? true)
                    <td class="px-8 pt-4 text-center table-contents-font-size border-right whitespace-nowrap">
                        {{ getPrintPdfCurrencySymbol().'150.00' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_discount'] ?? true)
                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                        0
                    </td>
                @endif
                @if ($showPrintSettings['sale_dis_2_enable'] ?? true)
                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                        0
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_total_discount'] ?? true)
                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                        0.00
                    </td>
                @endif
                @if ($isCompanyGstApplicable)
                    <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                        {{ getPrintPdfCurrencySymbol().'1,500.00' }}
                    </td>
                    @if ($showPrintSettings['show_sale_gst'] ?? true)
                        <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                            5
                        </td>
                    @endif
                    @if ($showPrintSettings['show_sale_gst_amount'] ?? true)
                        <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                            {{ getPrintPdfCurrencySymbol().'75.00' }}
                        </td>
                    @endif
                    @if ($showPrintSettings['show_sale_total_amount'] ?? true)
                        <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                            {{ getPrintPdfCurrencySymbol().'1,575.00' }}
                        </td>
                    @endif
                @else
                    <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end">
                        {{ getPrintPdfCurrencySymbol().'1,500.00' }}
                    </td>
                @endif
            </tr>
            <tr>
                <td class="px-8 pt-4 text-center table-contents-font-size border-right">
                    3
                </td>
                <td class="px-8 pt-4 table-contents-font-size border-right fw-6">
                    Blue Saree
                </td>
                @if($isCompanyGstApplicable && ($showPrintSettings['show_sale_hsn_sac'] ?? true))
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        5407
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_mrp'] ?? true)
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        {{ getPrintPdfCurrencySymbol().'1,126.00' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_qty'] ?? true)
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        5
                        @if($showPrintSettings['show_sale_unit'] ?? true)
                            <span class="table-contents-font-size">PCS</span>
                        @endif
                    </td>
                @endif
                @if ($showPrintSettings['sale_uom_enable'] ?? false)
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        0.5
                        @if($showPrintSettings['show_sale_unit'] ?? false)
                            <span class="table-contents-font-size">MTR</span>
                        @endif
                    </td>
                @endif
                @if ($isCompanyGstApplicable && ($showPrintSettings['show_sale_rate_with_gst'] ?? false))
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        {{ getPrintPdfCurrencySymbol().'1,182.30' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_rate'] ?? true)
                    <td class="px-8 pt-4 text-center table-contents-font-size border-right whitespace-nowrap">
                        {{ getPrintPdfCurrencySymbol().'1,126.00' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_discount'] ?? true)
                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                        0
                    </td>
                @endif
                @if ($showPrintSettings['sale_dis_2_enable'] ?? true)
                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                        0
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_total_discount'] ?? true)
                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                        0.00
                    </td>
                @endif
                @if ($isCompanyGstApplicable)
                    <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                        {{ getPrintPdfCurrencySymbol().'5,630.00' }}
                    </td>
                    @if ($showPrintSettings['show_sale_gst'] ?? true)
                        <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                            5
                        </td>
                    @endif
                    @if ($showPrintSettings['show_sale_gst_amount'] ?? true)
                        <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                            {{ getPrintPdfCurrencySymbol().'281.50' }}
                        </td>
                    @endif
                    @if ($showPrintSettings['show_sale_total_amount'] ?? true)
                        <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                            {{ getPrintPdfCurrencySymbol().'5,911.50' }}
                        </td>
                    @endif
                @else
                    <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end">
                        {{ getPrintPdfCurrencySymbol().'5,630.00' }}
                    </td>
                @endif
            </tr>
            <tr class="h-100 vertical-top">
                <td class="px-8 pt-4 text-center table-contents-font-size border-right">
                    4
                </td>
                <td class="px-8 pt-4 table-contents-font-size border-right fw-6">
                    Cricket Bat
                </td>
                @if($isCompanyGstApplicable && ($showPrintSettings['show_sale_hsn_sac'] ?? true))
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_mrp'] ?? true)
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        {{ getPrintPdfCurrencySymbol().'120.00' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_qty'] ?? true)
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        10
                        @if($showPrintSettings['show_sale_unit'] ?? true)
                            <span class="table-contents-font-size">PCS</span>
                        @endif
                    </td>
                @endif
                @if ($showPrintSettings['sale_uom_enable'] ?? false)
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        1
                        @if($showPrintSettings['show_sale_unit'] ?? false)
                            <span class="table-contents-font-size">Box</span>
                        @endif
                    </td>
                @endif
                @if ($isCompanyGstApplicable && ($showPrintSettings['show_sale_rate_with_gst'] ?? false))
                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                        {{ getPrintPdfCurrencySymbol().'126.00' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_rate'] ?? true)
                    <td class="px-8 pt-4 text-center table-contents-font-size border-right whitespace-nowrap">
                        {{ getPrintPdfCurrencySymbol().'120.00' }}
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_discount'] ?? true)
                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                        0
                    </td>
                @endif
                @if ($showPrintSettings['sale_dis_2_enable'] ?? true)
                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                        0
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_total_discount'] ?? true)
                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                        0.00
                    </td>
                @endif
                @if ($isCompanyGstApplicable)
                    <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end">
                        {{ getPrintPdfCurrencySymbol().'1,200.00' }}
                    </td>
                    @if ($showPrintSettings['show_sale_gst'] ?? true)
                        <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                            5
                        </td>
                    @endif
                    @if ($showPrintSettings['show_sale_gst_amount'] ?? true)
                        <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                            {{ getPrintPdfCurrencySymbol().'60.00' }}
                        </td>
                    @endif
                    @if ($showPrintSettings['show_sale_total_amount'] ?? true)
                        <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end border-left">
                            {{ getPrintPdfCurrencySymbol().'1,260.00' }}
                        </td>
                    @endif
                @else
                    <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end">
                        {{ getPrintPdfCurrencySymbol().'1,200.00' }}
                    </td>
                @endif
            </tr>
            <tr class="border-top border-bottom">
                <td class="px-8 pt-4 text-center table-headings-font-size border-right"></td>
                <td class="px-8 pt-4 table-headings-font-size border-right fw-6">
                    Total
                </td>
                @if($isCompanyGstApplicable && ($showPrintSettings['show_sale_hsn_sac'] ?? true))
                    <td class="px-8 pt-4 text-center table-headings-font-size whitespace-nowrap border-right">
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_mrp'] ?? true)
                    <td class="px-8 pt-4 text-center table-headings-font-size whitespace-nowrap border-right">
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_qty'] ?? true)
                    <td class="px-8 pt-4 text-center table-headings-font-size whitespace-nowrap border-right">
                        35.00
                    </td>
                @endif
                @if ($showPrintSettings['sale_uom_enable'] ?? false)
                    <td class="px-8 pt-4 text-center table-headings-font-size whitespace-nowrap border-right">
                        3.5
                    </td>
                @endif
                @if ($isCompanyGstApplicable && ($showPrintSettings['show_sale_rate_with_gst'] ?? false))
                    <td class="px-8 pt-4 text-center table-headings-font-size whitespace-nowrap border-right">
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_rate'] ?? true)
                    <td class="px-8 pt-4 text-center table-headings-font-size border-right whitespace-nowrap">
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_discount'] ?? true)
                    <td class="px-8 pt-4 table-headings-font-size border-right whitespace-nowrap text-end">
                    </td>
                @endif
                @if ($showPrintSettings['sale_dis_2_enable'] ?? true)
                    <td class="px-8 pt-4 table-headings-font-size border-right whitespace-nowrap text-end">
                    </td>
                @endif
                @if ($showPrintSettings['show_sale_total_discount'] ?? true)
                    <td class="px-8 pt-4 table-headings-font-size border-right whitespace-nowrap text-end">
                    </td>
                @endif
                @if ($isCompanyGstApplicable)
                    <td class="px-8 pt-4 pb-4 table-headings-font-size whitespace-nowrap text-end">
                        {{ getPrintPdfCurrencySymbol().'8,472.90' }}
                    </td>
                    @if ($showPrintSettings['show_sale_gst'] ?? true)
                        <td class="px-8 pt-4 table-headings-font-size whitespace-nowrap text-end border-left"></td>
                    @endif
                    @if ($showPrintSettings['show_sale_gst_amount'] ?? true)
                        <td class="px-8 pt-4 table-headings-font-size whitespace-nowrap text-end border-left"></td>
                    @endif
                    @if ($showPrintSettings['show_sale_total_amount'] ?? true)
                        <td class="px-8 pt-4 table-headings-font-size whitespace-nowrap text-end border-left">
                            {{ getPrintPdfCurrencySymbol().'8,896.05' }}
                        </td>
                    @endif
                @else
                    <td class="px-8 pt-4 table-headings-font-size whitespace-nowrap text-end">
                        {{ getPrintPdfCurrencySymbol().'8,472.90' }}
                    </td>
                @endif
            </tr>
        </table>
        <div style="display: flex;">
            <div style="display: flex; flex-direction: column; min-height: 250px; width: 100%; justify-content: space-between;">
                @if (($showPrintSettings['show_sale_narration'] ?? true) || ($showPrintSettings['show_sale_terms_and_conditions'] ?? true))
                    <div>
                        @if($showPrintSettings['show_sale_narration'] ?? true)
                            <div class="pt-4 pb-4">
                                <h4 class="px-8 pt-4 note-font-size fw-7">
                                    {{ $changeLabel['narration'] ?? 'Note' }} :
                                </h4>
                                <p class="px-8 pt-4 note-font-size">
                                    Lorem ipsum dolor sit amet consectetur. Cursus faucibus mattis vitae pharetra
                                    sagittis elementum quisque erat quis.
                                </p>
                            </div>
                        @endif
                        @if($showPrintSettings['show_sale_terms_and_conditions'] ?? true)
                            <div class="pt-4 pb-4">
                                <h4 class="px-8 pt-4 terms-and-conditions-font-size fw-7">
                                    {{ $changeLabel['terms_and_conditions'] ?? 'Terms and Conditions' }} :
                                </h4>
                                <p class="px-8 pt-4 terms-and-conditions-font-size">
                                    Lorem ipsum dolor sit amet consectetur. Mi tristique velit vestibulum sit.
                                    Suspendisse cursus nibh vulputate mauris euismod venenatis. Sed consequat id sed at
                                    in sed pretium lectus metus. Lacus sed sed dictum vel ut.
                                </p>
                            </div>
                        @endif
                    </div>
                @endif
                <div>
                    @if($showPrintSettings['show_sale_in_words'] ?? true)
                        <div class="px-8 pt-4 pb-4 {{ (($showPrintSettings['show_sale_narration'] ?? true) || ($showPrintSettings['show_sale_terms_and_conditions'] ?? true)) ? 'border-top' : '' }} table-contents-font-size">
                            <span class="fw-7 table-contents-font-size" style="margin-right:4px;">{{ $changeLabel['in_words'] ?? 'In Words' }}:</span>
                            Nine Thousand Two Hundred and Seventy {{ getPrintPdfCurrencyInWords()['name'] }} Only
                        </div>
                    @endif
                    <table cellpadding="0" style="page-break-inside: avoid !important; {{ (($showPrintSettings['show_sale_narration'] ?? true) || ($showPrintSettings['show_sale_terms_and_conditions'] ?? true)) ? '' : 'height: 250px;' }}">
                        <tr class="{{ (($showPrintSettings['show_sale_narration'] ?? true) || ($showPrintSettings['show_sale_terms_and_conditions'] ?? true) || ($showPrintSettings['show_sale_in_words'] ?? true)) ? 'border-top' : '' }}">
                            @if ((isset($invoiceSetting['qr_code']) && $invoiceSetting['qr_code'] == 1 ? true : false) || (isset($invoiceSetting['bank_details']) && $invoiceSetting['bank_details'] == 1 ? true : false) || ($invoiceSetting['show_payment_status'] ?? false))
                                <td class="{{ (($showPrintSettings['show_sale_authorized_signatory'] ?? true) || ($invoiceSetting['signature'] ?? true)) ? 'border-right' : '' }}">
                                    <table>
                                        <tr>
                                            <td>
                                                <table>
                                                    <tr>
                                                        <td>
                                                            <div class="px-8 pt-4 pb-4"
                                                                style="display: flex; align-items:center; gap:16px;">
                                                                @if ((isset($invoiceSetting['qr_code']) && $invoiceSetting['qr_code'] == 1 ? true : false))
                                                                    <div class="qr-code"
                                                                        style="margin-top: 5px; padding-right: 10px; white-space: nowrap;">
                                                                        <svg width="75" height="75" viewBox="0 0 120 120"
                                                                            fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                            <g clip-path="url(#clip0_75_97)">
                                                                                <path
                                                                                    d="M0 67.8402C0 62.6134 0 57.4047 0 52.1597C5.22683 52.1597 10.4356 52.1597 15.7167 52.1597C15.7167 54.5652 15.7167 56.9706 15.7167 59.4484C17.9231 59.4484 20.0392 59.4484 22.2276 59.4484C22.2276 57.061 22.2276 54.6737 22.2276 52.1959C27.5268 52.1959 32.7536 52.1959 38.0527 52.1959C38.0527 54.5833 38.0527 56.9887 38.0527 59.4484C40.2592 59.4484 42.3753 59.4484 44.6179 59.4484C44.6179 57.061 44.6179 54.6375 44.6179 52.1055C47.15 52.1055 49.5735 52.1055 52.0693 52.1055C52.0693 49.6096 52.0693 47.2042 52.0693 44.6903C54.5652 44.6903 56.9706 44.6903 59.4484 44.6903C59.4484 39.9518 59.4484 35.3399 59.4484 30.6375C57.0068 30.6375 54.6014 30.6375 52.1417 30.6375C52.1417 20.3828 52.1417 10.2185 52.1417 0.0180664C54.8907 0.0180664 57.5855 0.0180664 60.3888 0.0180664C60.3888 2.47775 60.3888 4.90126 60.3888 7.4152C62.9209 7.4152 65.3444 7.4152 67.8222 7.4152C67.8222 12.6963 67.8222 17.8869 67.8222 23.1861C65.3625 23.1861 62.939 23.1861 60.4612 23.1861C60.4612 25.3926 60.4612 27.5267 60.4612 29.7332C62.9209 29.7332 65.3444 29.7332 67.8222 29.7332C67.8222 35.0505 67.8222 40.2592 67.8222 45.5584C65.3806 45.5584 62.9751 45.5584 60.4974 45.5584C60.4974 47.7649 60.4974 49.899 60.4974 52.1236C67.8945 52.1236 75.2735 52.1236 82.743 52.1236C82.743 57.0972 82.743 61.9804 82.743 67.0083C87.7528 67.0083 92.6722 67.0083 97.6277 67.0083C97.6277 72.3255 97.6277 77.5343 97.6277 82.7973C90.2306 82.7973 82.8696 82.7973 75.4182 82.7973C75.4182 85.0219 75.4182 87.156 75.4182 89.3806C77.8417 89.3806 80.2291 89.3806 82.7611 89.3806C82.7611 91.8764 82.7611 94.2818 82.7611 96.7596C85.0038 96.7596 87.1198 96.7596 89.3444 96.7596C89.3444 94.318 89.3444 91.9307 89.3444 89.471C94.6436 89.471 99.8342 89.471 105.115 89.471C105.115 91.8764 105.115 94.2638 105.115 96.7777C107.611 96.7777 110.035 96.7777 112.567 96.7777C112.567 99.3097 112.567 101.733 112.567 104.265C115.081 104.265 117.504 104.265 119.982 104.265C119.982 109.564 119.982 114.755 119.982 120.018C114.773 120.018 109.583 120.018 104.283 120.018C104.283 112.657 104.283 105.26 104.283 97.8086C99.581 97.8086 95.0053 97.8086 90.3391 97.8086C90.3391 99.9246 90.3391 102.041 90.3391 104.265C92.7626 104.265 95.1861 104.265 97.6458 104.265C97.6458 109.564 97.6458 114.755 97.6458 120.018C94.9148 120.018 92.22 120.018 89.3986 120.018C89.3986 115.099 89.3986 110.197 89.3986 105.224C87.1379 105.224 85.0219 105.224 82.8154 105.224C82.8154 110.161 82.8154 115.044 82.8154 120.018C77.5343 120.018 72.3436 120.018 67.0264 120.018C67.0264 115.117 67.0264 110.197 67.0264 105.224C64.7837 105.224 62.6496 105.224 60.4431 105.224C60.4431 110.161 60.4431 115.063 60.4431 120.018C57.6398 120.018 54.9088 120.018 52.1598 120.018C52.1598 114.755 52.1598 109.564 52.1598 104.283C54.6014 104.283 57.0068 104.283 59.4665 104.283C59.4665 102.077 59.4665 99.9608 59.4665 97.7182C57.0429 97.7182 54.6194 97.7182 52.1598 97.7182C52.1598 94.8968 52.1598 92.202 52.1598 89.3986C57.0972 89.3986 61.9985 89.3986 66.9359 89.3986C66.9359 87.1741 66.9359 85.058 66.9359 82.8335C61.9985 82.8335 57.1153 82.8335 52.1236 82.8335C52.1236 77.8417 52.1236 72.9585 52.1236 67.9307C47.1138 67.9307 42.2125 67.9307 37.2208 67.9307C37.2208 65.3986 37.2208 63.0113 37.2208 60.5154C32.5365 60.5154 27.9246 60.5154 23.2042 60.5154C23.2042 62.9209 23.2042 65.3444 23.2042 67.8402C15.4273 67.8402 7.74077 67.8402 0 67.8402ZM67.0083 66.9721C67.0083 65.3082 67.0083 63.7528 67.0083 62.1974C67.0083 60.4069 67.0083 60.4069 65.1997 60.4069C63.3368 60.4069 61.4921 60.4069 59.5026 60.4069C59.5026 57.893 59.5026 55.4695 59.5026 53.046C57.2781 53.046 55.1982 53.046 53.0821 53.046C53.0821 57.7121 53.0821 62.324 53.0821 66.954C57.7302 66.9721 62.2879 66.9721 67.0083 66.9721ZM74.4597 81.8568C74.4597 79.3791 74.4597 76.9555 74.4597 74.4416C76.9736 74.4416 79.3971 74.4416 81.8387 74.4416C81.8387 72.2351 81.8387 70.101 81.8387 67.9668C77.1726 67.9668 72.5788 67.9668 67.9488 67.9668C67.9488 72.633 67.9488 77.2087 67.9488 81.8387C70.1191 81.8568 72.217 81.8568 74.4597 81.8568ZM81.8568 97.6639C79.3429 97.6639 76.9194 97.6639 74.4235 97.6639C74.4235 95.1681 74.4235 92.7626 74.4235 90.3572C72.217 90.3572 70.101 90.3572 67.9488 90.3572C67.9488 95.0053 67.9488 99.5991 67.9488 104.211C72.6149 104.211 77.2268 104.211 81.8568 104.211C81.8568 102.023 81.8568 99.9246 81.8568 97.6639Z"
                                                                                    fill="#0E0802" />
                                                                                <path
                                                                                    d="M0 45.4679C0 30.33 0 15.2102 0 0.0180664C15.156 0.0180664 30.2939 0.0180664 45.4861 0.0180664C45.4861 15.1379 45.4861 30.2758 45.4861 45.486C30.3482 45.4679 15.2102 45.4679 0 45.4679ZM37.1485 37.1484C37.1485 27.5086 37.1485 17.9412 37.1485 8.33758C27.5268 8.33758 17.9593 8.33758 8.37378 8.33758C8.37378 17.9593 8.37378 27.5448 8.37378 37.1484C17.9955 37.1484 27.5448 37.1484 37.1485 37.1484Z"
                                                                                    fill="#0E0802" />
                                                                                <path
                                                                                    d="M120 45.5222C104.808 45.5222 89.688 45.5222 74.532 45.5222C74.532 30.3662 74.532 15.2464 74.532 0.0361328C89.6519 0.0361328 104.808 0.0361328 120 0.0361328C120 15.174 120 30.3119 120 45.5222ZM82.8877 8.35565C82.8877 18.0135 82.8877 27.5991 82.8877 37.1484C92.5275 37.1484 102.077 37.1484 111.662 37.1484C111.662 27.5267 111.662 17.9593 111.662 8.35565C102.041 8.35565 92.4913 8.35565 82.8877 8.35565Z"
                                                                                    fill="#0E0802" />
                                                                                <path
                                                                                    d="M0 74.4778C15.1922 74.4778 30.312 74.4778 45.4861 74.4778C45.4861 89.6338 45.4861 104.754 45.4861 119.964C30.3482 119.964 15.2102 119.964 0 119.964C0 104.826 0 89.7061 0 74.4778ZM37.1485 111.662C37.1485 102.005 37.1485 92.4552 37.1485 82.8696C27.5087 82.8696 17.9412 82.8696 8.35569 82.8696C8.35569 92.4914 8.35569 102.059 8.35569 111.662C17.9774 111.662 27.5268 111.662 37.1485 111.662Z"
                                                                                    fill="#0E0802" />
                                                                                <path
                                                                                    d="M89.3987 60.4249C89.3987 57.5854 89.3987 54.9087 89.3987 52.1597C99.581 52.1597 109.745 52.1597 119.964 52.1597C119.964 57.3503 119.964 62.541 119.964 67.804C114.755 67.804 109.546 67.804 104.247 67.804C104.247 65.3624 104.247 62.9389 104.247 60.4068C99.2736 60.4249 94.3723 60.4249 89.3987 60.4249Z"
                                                                                    fill="#0E0802" />
                                                                                <path
                                                                                    d="M104.32 74.4778C109.564 74.4778 114.737 74.4778 119.982 74.4778C119.982 79.7227 119.982 84.9314 119.982 90.2125C117.269 90.2125 114.538 90.2125 111.735 90.2125C111.735 87.7709 111.735 85.3474 111.735 82.8335C109.221 82.8335 106.797 82.8335 104.32 82.8335C104.32 80.0121 104.32 77.2811 104.32 74.4778Z"
                                                                                    fill="#0E0802" />
                                                                                <path
                                                                                    d="M30.5652 30.5833C25.3745 30.5833 20.1839 30.5833 14.9209 30.5833C14.9209 25.3565 14.9209 20.1658 14.9209 14.9209C20.0934 14.9209 25.2841 14.9209 30.5652 14.9209C30.5652 20.1116 30.5652 25.3203 30.5652 30.5833Z"
                                                                                    fill="#0E0802" />
                                                                                <path
                                                                                    d="M105.115 30.5833C99.9066 30.5833 94.6978 30.5833 89.4348 30.5833C89.4348 25.3565 89.4348 20.1477 89.4348 14.9028C94.6797 14.9028 99.8704 14.9028 105.115 14.9028C105.115 20.1477 105.115 25.3384 105.115 30.5833Z"
                                                                                    fill="#0E0802" />
                                                                                <path
                                                                                    d="M14.9209 105.097C14.9209 99.8523 14.9209 94.6617 14.9209 89.4167C20.1658 89.4167 25.3564 89.4167 30.6013 89.4167C30.6013 94.6617 30.6013 99.8523 30.6013 105.097C25.3384 105.097 20.1477 105.097 14.9209 105.097Z"
                                                                                    fill="#0E0802" />
                                                                            </g>
                                                                            <defs>
                                                                                <clipPath id="clip0_75_97">
                                                                                    <rect width="120" height="120"
                                                                                        fill="white" />
                                                                                </clipPath>
                                                                            </defs>
                                                                        </svg>
                                                                    </div>
                                                                @endif
                                                                @if ((isset($invoiceSetting['bank_details']) && $invoiceSetting['bank_details'] == 1 ? true : false))
                                                                <div>
                                                                    <table class="whitespace-nowrap">
                                                                        <tr>
                                                                            <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                Bank:
                                                                            </td>
                                                                            <td class="pt-2 footer-contents-font-size">
                                                                                Axis Bank
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                IFSC Code:
                                                                            </td>
                                                                            <td class="pt-2 footer-contents-font-size">
                                                                                UTIB0002996
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                A/C Number:
                                                                            </td>
                                                                            <td class="pt-2 footer-contents-font-size">
                                                                                *************
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                Bank Branch:
                                                                            </td>
                                                                            <td class="pt-2 footer-contents-font-size">
                                                                                ALTHAN
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                A/C Name:
                                                                            </td>
                                                                            <td class="pt-2 footer-contents-font-size">
                                                                                Shruti
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                UPI ID:
                                                                            </td>
                                                                            <td class="pt-2 footer-contents-font-size">
                                                                                **********@axisbank
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                                @endif
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                            <td>
                                                @if($invoiceSetting['show_payment_status'] ?? false)
                                                    <div class="d-flex justify-content-center">
                                                        <div class="mx-auto my-2 px-2 py-1" style="border:2px solid #4f158c !important; width:fit-content;">
                                                            <h5 class="text-primary text-center fw-7 fs-16"> PARTLY <br> UNPAID</h5>
                                                        </div>
                                                    </div>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            @endif
                            @if(($showPrintSettings['show_sale_authorized_signatory'] ?? true) || ($invoiceSetting['signature'] ?? true))
                                <td class="vertical-bottom border-right" style="width: 200px; position: relative">
                                    <div class="px-8 pt-4 pb-4">

                                        <div class="text-end signature">
                                            {{-- <img src="{{ $currentCompany->company_signature ?? null }}" alt="company-img"
                                                style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;"> --}}
                                        </div>
                                        <p class="text-center verical-bottom footer-contents-font-size">
                                            Receiver Signatory
                                        </p>
                                    </div>
                                </td>
                            @endif
                            @if(($showPrintSettings['show_sale_authorized_signatory'] ?? true) || ($invoiceSetting['signature'] ?? true))
                                <td class="vertical-bottom" style="width: 200px; position: relative">
                                    <div class="px-8 pt-4 pb-4">
                                        @if ($showPrintSettings['show_sale_authorized_signatory'] ?? true)
                                            <p class="text-center footer-headings-font-size fw-7">
                                                For, {{ strtoupper($currentCompany->trade_name) }}
                                            </p>
                                        @endif
                                        @if (($invoiceSetting['signature'] ?? false) && $currentCompany->company_signature != asset('images/preview-img.png'))
                                            <div class="text-center signature" style="margin: 0 auto;">
                                                <img src="{{ $currentCompany->company_signature ?? null }}" alt="company-img"
                                                    style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;">
                                            </div>
                                        @endif
                                        @if ($showPrintSettings['show_sale_authorized_signatory'] ?? true)
                                            <p class="text-center verical-bottom footer-contents-font-size">
                                                {{ $changeLabel['authorized_signatory'] ?? 'Authorized Signatory' }}
                                            </p>
                                        @endif
                                    </div>
                                </td>
                            @endif
                        </tr>
                    </table>
                </div>
            </div>
            <div class="vertical-top border-left" style="white-space: nowrap; display: flex; flex-direction: column; justify-content: space-between; ">
                <div>
                    <table cellpadding="0" style="flex-grow: 1;">
                        <tr>
                            <td class="px-8 pt-4 pb-2 table-contents-font-size vertical-top">
                                Packing Charge Income
                            </td>
                            <td class="px-8 pt-4 pb-2 table-contents-font-size vertical-top text-end">
                                {{ getPrintPdfCurrencySymbol().'100.00' }}
                            </td>
                        </tr>
                        <tr>
                            <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                Freight Charge Income
                            </td>
                            <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                {{ getPrintPdfCurrencySymbol().'100.00' }}
                            </td>
                        </tr>
                        <tr>
                            <td class="px-8 pb-2 table-contents-font-size vertical-top fw-7">
                                {{ $isCompanyGstApplicable ? $changeLabel['sub_total'] ?? 'Taxable Value' : $changeLabel['sub_total'] ?? 'Sub Total' }}
                            </td>
                            <td class="px-8 pb-2 table-contents-font-size vertical-top text-end fw-7">
                                {{ getPrintPdfCurrencySymbol().'8,672.90' }}
                            </td>
                        </tr>
                        @if($isCompanyGstApplicable)
                            <tr>
                                <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                    {{ $changeLabel['cgst'] ?? 'CGST' }}
                                </td>
                                <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                    {{ getPrintPdfCurrencySymbol().'272.00' }}
                                </td>
                            </tr>
                            <tr>
                                <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                    {{ $changeLabel['sgst'] ?? 'SGST' }}
                                </td>
                                <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                    {{ getPrintPdfCurrencySymbol().'272.00' }}
                                </td>
                            </tr>
                            <tr>
                                <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                    {{ $changeLabel['igst'] ?? 'IGST' }}
                                </td>
                                <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                    {{ getPrintPdfCurrencySymbol().'0.00' }}
                                </td>
                            </tr>
                            <tr>
                                <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                    {{ $changeLabel['cess'] ?? 'CESS' }}
                                </td>
                                <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                    {{ getPrintPdfCurrencySymbol().'45.00' }}
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                {{ $changeLabel['tcs'] ?? 'TCS' }}
                            </td>
                            <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                {{ getPrintPdfCurrencySymbol().'8.00' }}
                            </td>
                        </tr>
                        <tr>
                            <td class="px-8 pb-4 table-contents-font-size vertical-top">
                                {{ $changeLabel['round_off'] ?? 'Round off' }}
                            </td>
                            <td class="px-8 pb-4 table-contents-font-size vertical-top text-end">
                                {{ getPrintPdfCurrencySymbol().'0.10' }}
                            </td>
                        </tr>

                    </table>
                </div>
                <div>
                    <table class="vertical-bottom">
                        <tr class="border-top">
                            <td class="px-8 pt-4 pb-4 text-primary total-font-size fw-7">
                                {{ $changeLabel['total'] ?? 'Total' }}
                            </td>
                            <td class="px-8 pt-4 pb-4 text-primary text-end total-font-size fw-7">
                                {{ getPrintPdfCurrencySymbol().'9,270.00' }}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
