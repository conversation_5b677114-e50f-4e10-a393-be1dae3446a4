<!DOCTYPE html>
<html lang="en">
@php
    if (!isset($isA5Pdf)) {
        $isA5Pdf = false;
        if (getCompanyExpensePDFFormat() == \App\Models\CompanySetting::PDF_FORMAT[\App\Models\CompanySetting::A5]) {
            $isA5Pdf = true;
        }
    }
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
        <title>{{ isset($fileName) ? $fileName : $customerDetail->name }}</title>
        @include('company.pdf.sale_pdf.print_custom_font_css_variables')
        <style>
            h1 {
                font-size: 27px;
            }
            * {
                margin: 0;
                padding: 0;
                text-indent: 0;
                font-family: "{{ $fontStyleName }}";
                font-size: 13px;
                font-weight: 400;
                box-sizing: border-box;
            }
            @page {
                margin: 20px;
            }
            .main-table {
                display: flex;
                flex-direction: column;
                min-height: 100vh;
                width: 100%;
                box-sizing: border-box;
                border: 2px solid black;
            }
            table {
                display: table;
                width: 100%;
                border-collapse: collapse;
            }
            .text-primary {
                color: #4f158c;
            }
            .address {
                font-size: 11px;
                /* line-height: 14px; */
                padding-left: 8px;
                padding-right: 8px;
            }
            .phone{
                font-size: 11px;
                /* line-height: 14px; */
                padding-left: 8px;
                padding-right: 8px;
            }
            td {
                vertical-align: top;
            }
            .fs-13 {
                font-size: 13px;
            }
            .fs-12 {
                font-size: 12px;
            }
            .fw-6 {
                font-weight: 600;
            }
            .whitespace-nowrap {
                white-space: nowrap;
            }
            .border-bottom {
                border-bottom: 1px solid black;
            }
            .border-right {
                border-right: 1px solid black;
            }
            .border-top {
                border-top: 1px solid black;
            }
            .border-left {
                border-left: 1px solid black;
            }
            .vertical-top {
                vertical-align: top;
            }
            .vertical-middle {
                vertical-align: middle;
            }
            .vertical-bottom {
                vertical-align: bottom;
            }
            .text-center {
                text-align: center;
            }
            .text-start {
                text-align: left;
            }
            .text-end {
                text-align: right;
            }
            .table-heading {
                padding: 3px 8px;
                text-align: left;
                position: relative;
                /* background-color: #eeeeee !important; */
            }
            .signature {
                max-width: 210px;
                height: 100px;
                margin-left:auto;
            }
            .desc {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                font-size: 12px;
                position: relative;
                padding-left: 5px;
                padding-right: 5px;
            }
            .desc::before {
                position: absolute;
                content: "(";
                top: 0;
                left: 0;
                font-size: 12px;
            }
            .desc::after {
                position: absolute;
                content: ")";
                bottom: 2px;
                right: 0;
                font-size: 12px;
            }
            .item-table tr:nth-last-child(-n+2):not(:last-child) {
                font-size:12px;
                border-bottom:1px solid black;
                padding:4px 8px 0 4px;
                height:100%;
                vertical-align:top;
            }
            .d-none{
                display: none;
            }

            .min-width-250 {
                min-width: 250px !important;
            }

            .min-width-150 {
                min-width: 150px !important;
            }
        </style>
    @include('company.pdf.sale_pdf.print_custom_font_css')
    </head>
    <body>
        <div class="main-table">
            @if(($invoiceSetting['show_slogan'] ?? false) && isset($invoiceSetting['slogan']))
                <div class="text-center border-bottom" style="padding: 4px 2px 2px 2px">
                    <h6 class="mb-1 company-address-font-size">
                        {{ $invoiceSetting['slogan'] }}
                    </h6>
                </div>
            @endif
            <div class="text-center border-bottom" style="padding: 4px 2px 2px 2px">
                <h6 class="fw-6 text-primary" style="font-size: 15px">
                    {{ $defaultPrintTitle ?? 'Purchase Return Invoice' }}
                </h6>
            </div>

             {{-- Logo / Company Name Section Start --}}
            <table cellpadding="0">
                <tr>
                    <td class="vertical-middle" style="{{ $isA5Pdf ? 'padding: 5px 8px;' : 'padding: 10px 25px;' }} width: 100px; max-width:100px; height: 100px">
                        @if (isset($currentCompany->company_logo) && ($invoiceSetting['expense_logo'] ?? true) &&  $currentCompany->company_logo != asset('images/preview-img.png'))
                            <img src={{ $currentCompany->company_logo ?? '' }} alt="Logo" style="object-fit: contain;" width="100" >
                        @endif
                    </td>
                    <td class="vertical-middle text-center" style="margin-left:-50px; width:60%;">
                        <h1 class="company-name-font-size company-font-customization">{{ strtoupper($currentCompany->trade_name) }}</h1>
                        @if(($invoiceSetting['prop_details'] ?? true) && isset($customProp))
                            <p class="mt-1 fw-6 company-address-font-size">{{ $customProp->label_name.' : '.$customProp->label_value }}</p>
                        @endif
                        <p class="company-address-font-size">
                            {{ strtoupper($companyBillingAddress->address_1 ?? null) }},
                            {{ strtoupper($companyBillingAddress->address_2 ?? null) }},
                            {{ strtoupper(getCityName($companyBillingAddress->city_id ?? null)) }},
                            {{ strtoupper(getStateName($companyBillingAddress->state_id ?? null)) }},
                            {{ strtoupper(getCountryName($companyBillingAddress->country_id ?? null)) }},
                            {{ $companyBillingAddress->pin_code ?? null }}
                        </p>
                        <p class="company-address-font-size">
                            @if (getCustomInvoiceSetting('expense_mobile_number'))
                            {{ $changeLabel['expense_tel'] ?? 'Tel' }} :
                            {{
                                (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') .
                                (isset($invoiceSetting['alternate_phone']) ? $invoiceSetting['alternate_phone'] : ('+' . $currentCompany->user->region_code . ' ' . $currentCompany->user->phone)) .
                                (isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2']) ? ', +' . $invoiceSetting['region_code_2'] . ' ' : '') .
                                (isset($invoiceSetting['alternate_phone_2']) ? (isset($invoiceSetting['region_code_2']) ? '' : ', ') . $invoiceSetting['alternate_phone_2'] : '')
                            }}
                            @endif
                            @if (getCustomInvoiceSetting('expense_mobile_number') && getCustomInvoiceSetting('expense_email'))
                                |
                            @endif
                            @if (getCustomInvoiceSetting('expense_email'))
                                {{ (isset(getCompanySettings()['alternate_email']) ? getCompanySettings()['alternate_email'] : $currentCompany->user->email) ?? null }}
                            @endif</p>
                            @if ($isCompanyGstApplicable)
                                <p class="company-address-font-size">{{ $changeLabel['expense_gstin'] ?? 'GSTIN' }} : {{ '  ' . $currentCompany->companyTax->gstin ?? null }}</p>
                            @endif
                            @foreach (printCustomPDFLabelsForExpense() as $key => $customLabel)
                                <p class="company-address-font-size">{{ $key ?? null }}: {{ $customLabel ?? null }}</p>
                            @endforeach
                    </td>
                    <td class="vertical-middle text-end" style="padding: 10px 25px; width: 100px; max-width:100px; height: 100px">

                    </td>
                </tr>
            </table>
            {{-- Logo / Company Name Section End --}}

            {{-- Bill To / Ship to / Invoice Details Section Start --}}
            <table cellpadding="0">
                <tr class="border-bottom">
                    <td class="border-right vertical-top" style="width:33.33%;">
                        <p class="text-primary border-bottom border-top fw-6 table-heading header-labels-font-size"
                            >
                            {{ $changeLabel['expense_bill_to'] ?? 'Purchase From' }}:
                        </p>
                        <h4 class="fw-6 header-contents-font-size" style="padding: 4px 0 1px 8px;">
                            {{ strtoupper($customerDetail->name) }}
                        </h4>
                        @if (isset($billingAddress))
                            <p class="address header-contents-font-size">
                                @if ($billingAddress->address_1 != null)
                                    {{ strtoupper($billingAddress->address_1) }}
                                @endif
                                @if ($billingAddress->address_2 != null)
                                    {{ strtoupper($billingAddress->address_2) }},<br>
                                @endif
                                {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                                {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                                {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                                {{ $billingAddress->pin_code ?? null }}
                            </p>
                        @endif
                        <p class="phone header-contents-font-size" >
                            @if ($customerDetail->model->phone_1 != null || $customerDetail->model->phone_2 != null)
                            Contact No:
                                @if ($customerDetail->model->phone_1 != null)
                                    +{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}
                                @endif
                                @if ($customerDetail->model->phone_1 != null && $customerDetail->model->phone_2 != null)
                                    |
                                @endif
                                @if ($customerDetail->model->phone_2 != null)
                                    <span class="whitespace-nowrap header-contents-font-size">
                                        +{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 }}
                                    </span>
                                @endif
                            @endif
                        </p>
                        @if (!empty($customerDetail->model->person_email))
                            <p class="header-contents-font-size" style="padding: 2px 0px 0px 8px;">
                                Email: {{ $customerDetail->model->person_email ?? null }}
                            </p>
                        @endif
                        @if ($showGst)
                            <p class="header-contents-font-size" style="padding: 2px 0px 0px 8px;">
                                GSTIN: {{ $transaction->gstin ?? $customerDetail->model->gstin ?? null }}
                            </p>
                        @endif
                        @if (! empty($panNumber) && $showPanNumber)
                        <p class="header-contents-font-size" style="padding: 2px 0px 0px 8px;">
                            PAN: {{ $panNumber ?? null }}
                        </p>
                    @endif
                    </td>
                    @if (getCustomInvoiceSetting('expense_ship_to_details'))
                    <td class="border-right vertical-top" style="width:33.33%;">
                        <p class="text-primary border-bottom border-top fw-6 table-heading header-labels-font-size">
                            {{ $changeLabel['expense_ship_to'] ?? 'Dispatch From' }}:
                        </p>
                        <h4 class="fw-6 header-contents-font-size" style="padding: 4px 0 1px 8px;">
                            {{ strtoupper($transaction->shipping_name ?? $customerDetail->name) }}
                        </h4>
                        <p class="address header-contents-font-size">
                            @if (isset($shippingAddress->address_1))
                                {{ strtoupper($shippingAddress->address_1)}},
                            @endif
                            @if (isset($shippingAddress->address_2))
                                {{ strtoupper($shippingAddress->address_2) }},
                            @endif
                            {{ isset($shippingAddress->city_id) ? strtoupper(getCityName($shippingAddress->city_id)) . ',' : '' }}
                            {{ isset($shippingAddress->state_id) ? strtoupper(getStateName($shippingAddress->state_id)) . ',' : '' }}
                            {{ isset($shippingAddress->country_id) ? strtoupper(getCountryName($shippingAddress->country_id)) : '' }},
                            {{ $shippingAddress->pin_code ?? null }}
                        </p>
                        <p class="phone header-contents-font-size">
                             @if ($customerDetail->model->phone_1 != null || $customerDetail->model->phone_2 != null)
                             Contact No:
                                 @if ($customerDetail->model->phone_1 != null)
                                     +{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}
                                 @endif
                                 @if ($customerDetail->model->phone_1 != null && $customerDetail->model->phone_2 != null)
                                     ,
                                 @endif
                                 @if ($customerDetail->model->phone_2 != null)
                                     +{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 }}
                                 @endif
                             @endif
                        </p>
                        @if ($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                            <p class="header-contents-font-size" style="padding: 2px 0px 0px 8px;">
                                GSTIN: {{$transaction->shipping_gstin ?? null }}
                            </p>
                        @endif
                        @if (! empty($panNumber) && $showPanNumber)
                            <p class="header-contents-font-size" style="padding: 0px 0px 2px 8px;">
                                PAN: {{ $panNumber ?? null }}
                            </p>
                        @endif
                        </td>
                    @endif
                    <td class="vertical-top" style="width:33.33%;">
                        <p class="text-primary border-bottom border-top fw-6 table-heading header-labels-font-size">
                            {{ $changeLabel['expense_invoice_details'] ?? 'Invoice Details' }}:
                        </p>
                        <div style="padding:0 8px !important;">
                            <table class="table">
                                <tr>
                                    <td class="header-contents-font-size fw-6" style="padding: 4px 1px 1px 1px;">
                                        {{ $changeLabel['expense_invoice_number'] ?? 'Debit Note No' }}:
                                    </td>
                                    <td class="text-end header-contents-font-size" style="padding: 4px 1px 1px 1px;">
                                        {{ $transaction->voucher_number }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="header-contents-font-size fw-6" style="padding: 4px 1px 1px 1px;">
                                        {{ $changeLabel['expense_invoice_date'] ?? 'Debit Note Date' }}:
                                    </td>
                                    <td class="header-contents-font-size text-end" style="padding: 4px 1px 1px 1px; ">
                                        {{ $voucherDate }}
                                    </td>
                                </tr>
                                @if(isset($originalInvoiceNumber) && !empty($originalInvoiceNumber) && isset($originalInvoiceDate) && !empty($originalInvoiceDate))
                                <tr>
                                    <td class="header-contents-font-size fw-6" style="padding: 4px 1px 1px 1px;">
                                        Original Voucher No:
                                    </td>
                                    <td class="text-end header-contents-font-size" style="padding: 4px 1px 1px 1px;">
                                        {{ Illuminate\Support\Str::limit($originalInvoiceNumber, 20, '...')  }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="header-contents-font-size fw-6" style="padding: 4px 1px 1px 1px;">
                                        Original Voucher Date:
                                    </td>
                                    <td class="header-contents-font-size text-end" style="padding: 4px 1px 1px 1px; ">
                                        {{ isset($originalInvoiceDate) ? \Carbon\Carbon::parse($originalInvoiceDate)->format('d-m-Y') : null }}
                                    </td>
                                </tr>
                                @endif
                                <tr>
                                    <td class="header-contents-font-size fw-6" style="padding: 4px 1px 1px 1px;">
                                        {{ $changeLabel['expense_supplier_inv_no'] ?? 'Supplier Inv Number:' }}
                                    </td>
                                    <td class="header-contents-font-size text-end" style="padding: 4px 1px 1px 1px;">
                                        {{ $transaction->supplier_purchase_return_number }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="header-contents-font-size fw-6" style="padding: 4px 1px 4px 1px;">
                                       {{ $changeLabel['expense_supplier_inv_date'] ??  'Supplier Inv Date:' }}
                                    </td>
                                    <td class="header-contents-font-size text-end" style="padding: 4px 1px 4px 1px;">
                                        {{ isset($transaction->supplier_purchase_return_date) ? \Carbon\Carbon::parse($transaction->supplier_purchase_return_date)->format('d-m-Y') : '' }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </td>
                </tr>
            </table>
            {{-- Bill To / Ship to / Invoice Details Section End --}}

            {{-- Transport Details Section Start --}}
            @if (getCustomInvoiceSetting('expense_transport_details') || isset($eWayBill) || !empty($transaction->transporter_vehicle_number) || (isset($transaction->eway_bill_number) || isset($transaction->eway_bill_date)))
                <table cellpadding="0">
                    <tr class="border-bottom">
                        @if(getCustomInvoiceSetting('expense_transport_details'))
                        <td class="vertical-top" style="min-width: 260px">
                            <table style="display: table">
                                <tr>
                                    <td class="header-contents-font-size fw-6" style="padding: 3px 0 0 8px; width: 110px;">
                                        {{ $changeLabel['expense_transport_name'] ?? 'Transport Name' }}:
                                    </td>
                                    <td style="padding: 3px 0 0 0px;" class="header-contents-font-size">
                                        {{ $transaction->transport->transporter_name ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="header-contents-font-size" style="padding: 3px 0 0 8px;font-weight: bold;">
                                        {{ $changeLabel['document_no'] ?? 'Document No' }}:
                                    </td>
                                    <td style="padding: 3px 0 0 0px;" class="header-contents-font-size">
                                        {{ $transaction->transporter_document_number ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="header-contents-font-size" style="padding: 3px 0 0 8px;font-weight: bold;">
                                        {{ $changeLabel['document_date'] ?? 'Document Date' }}:
                                    </td>
                                    <td style="padding: 3px 0 0 0px;" class="header-contents-font-size">
                                        {{ !empty($transaction->transporter_document_date) ? \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') : '' }}
                                    </td>
                                </tr>
                            </table>
                        </td>
                        @endif
                        @if(isset($eWayBill) || !empty($transaction->transporter_vehicle_number) || (isset($transaction->eway_bill_number) || isset($transaction->eway_bill_date)))
                        <td class="vertical-top {{ $invoiceSetting['transport_details'] ? 'border-left' : '' }}" style="min-width: 260px">
                            <table class="vertical-top">
                                @if(isset($eWayBill))
                                    <tr>
                                        <td class="header-contents-font-size vertical-top" style="padding: 2px 0 0 8px;font-weight: bold;">
                                            E-way Bill No:
                                        </td>
                                        <td class="header-contents-font-size vertical-top" style="padding: 2px 0 0 8px;">
                                            {{ $eWayBill?->eway_bill_no ?? $transaction->eway_bill_number ?? null }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="header-contents-font-size vertical-top" style="padding: 0px 0 2px 8px;font-weight: bold;">
                                            E-way Bill Date:
                                        </td>
                                        <td class="header-contents-font-size vertical-top" style="padding: 0px 0 2px 8px;" >
                                            {{ \Carbon\Carbon::parse($eWayBill?->eway_bill_date)->format('d-m-Y') ?? \Carbon\Carbon::parse($transaction->eway_bill_date)->format('d-m-Y') ?? null }}
                                        </td>
                                    </tr>
                                @endif
                                @if(!empty($transaction->transporter_vehicle_number))
                                <tr>
                                    <td class="header-contents-font-size vertical-top" style="padding: 3px 0 4px 8px;font-weight: bold;">
                                        {{ $changeLabel['expense_vehicle_number'] ?? 'Vehicle No' }}:
                                    </td>
                                    <td class="header-contents-font-size vertical-top" style="padding: 3px 0 4px 0px;">
                                        {{ $transaction->transporter_vehicle_number ?? '' }}
                                    </td>
                                </tr>
                                @endif
                            </table>
                        </td>
                    @endif
                    </tr>
                </table>
            @endif
            {{-- Transport Details eWay Section End --}}

            {{-- Custom Fields Section Start --}}
            @if (count($customFieldValues) > 0)
                @php
                    $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
                @endphp
                <table cellpadding="0" class="item-table">
                    @foreach ($customFields->chunk(3) as $chunk)
                        <tr class="border-bottom">
                            @foreach ($chunk as $customField)
                                <td class="{{ $loop->last ? '' : 'border-right' }}" style="padding: 6px 8px; width:150px; {{ $isA5Pdf ? 'font-size: 9px; line-height: 8px;' : '' }}">
                                    <span style="font-weight: bold;{{ $isA5Pdf ? 'font-size: 9px; line-height: 8px;' : '' }}">{{ $customField['label_name'] ?? '' }}</span> : {{ $customField['value'] ?? '' }}
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </table>
            @endif
            {{-- Custom Fields Section End --}}

            {{-- Item Table Section Start --}}
            @if($itemType == \App\Models\SaleTransaction::ITEM_INVOICE)
                @php
                    $customFieldItemsValues = collect($transactionItems[0]['customItemsValues'])->where('is_show_in_print', true)->values();
                    $customFieldItemsHeaders = collect($customFieldItemsValues)->pluck('label_name')->toArray();
                    $cfNumberTypeFieldTotals = [];
                @endphp
                <table cellpadding="0" style="flex-grow:1;"  class="item-table">
                    <tr class="border-bottom">
                    <td class="table-headings-font-size border-right text-center" style="padding: 6px 8px;font-weight: bold; width:25px;">
                        {{ $changeLabel['expense_sn'] ?? 'SN' }}
                    </td>
                    <td class="table-headings-font-size border-right whitespace-nowrap {{ getCompanyPdfFormat(true,true) == \App\Models\CompanySetting::A5 ? 'min-width-150' : 'min-width-250' }}" style="padding: 6px 8px;font-weight: bold;">
                        {{ $changeLabel['expense_item_name'] ?? 'Item Name' }}
                    </td>
                    @if (count($customFieldItemsHeaders) > 0)
                            @foreach ($customFieldItemsHeaders as $customFieldItemHeader)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-start"style="padding: 6px 8px; font-weight: bold; width:50px; ">
                                    {{ $customFieldItemHeader ?? '' }}
                                </td>
                            @endforeach
                        @endif
                    @if ($isCompanyGstApplicable)
                        @if($showPrintSettings['show_purchase_hsn_sac'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-start"style="padding: 6px 8px; font-weight: bold; width:50px;">
                                {{ $changeLabel['expense_hsn_sac'] ?? 'HSN/SAC' }}
                            </td>
                        @endif
                        @if($showPrintSettings['show_purchase_gst'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-center"style="padding: 6px 8px; font-weight: bold;width:50px;">
                                {{ $changeLabel['expense_gst'] ?? 'GST (%)' }}
                            </td>
                        @endif
                    @endif
                    @if($showPrintSettings['show_purchase_qty'] ?? true)
                        <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:50px;">
                            {{ $changeLabel['expense_qty'] ?? 'Qty' }}
                        </td>
                    @endif
                    @if($showPrintSettings['expense_uom_enable'] ?? false)
                        @if ($transactionItems->sum('secondary_quantity') != 0.0)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:50px;">
                                {{ $changeLabel['expense_uom_label'] ?? 'UOM' }}
                            </td>
                        @endif
                    @endif
                    @if($showPrintSettings['show_purchase_mrp'] ?? true)
                        @if ($transactionItems->sum('mrp') != 0.0)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:50px;">
                                {{ $changeLabel['expense_mrp'] ?? 'MRP' }}
                            </td>
                        @endif
                    @endif
                    @if ($isCompanyGstApplicable)
                    @if($showPrintSettings['show_purchase_rate_with_gst'] ?? false)
                    <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:50px;">
                        {{ $changeLabel['expense_rate_with_gst'] ?? 'Rate With GST' }}
                    </td>
                    @endif
                    @endif
                    @if($showPrintSettings['show_purchase_rate'] ?? true)
                        <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:50px;">
                            {{ $changeLabel['expense_rate'] ?? 'Rate' }}
                        </td>
                    @endif
                    @if ($transactionItems->sum('discount_value') != 0.0)
                        @if($showPrintSettings['show_purchase_discount'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:50px;">
                                {{ $changeLabel['expense_discount'] ?? 'Dis.' }}
                            </td>
                        @endif
                    @endif
                    @if ($transactionItems->sum('discount_value_2') != 0.0)
                        @if($showPrintSettings['expense_dis_2_enable'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:50px;">
                                {{ $changeLabel['expense_dis_2_label'] ?? 'Dis. 2' }}
                            </td>
                        @endif
                    @endif
                    @if ($transactionItems->sum('total_discount_amount') != 0.0)
                        @if($showPrintSettings['show_purchase_total_discount'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:50px;">
                                {{ $changeLabel['expense_total_discount'] ?? 'Total Dis.' }}
                            </td>
                        @endif
                    @endif
                    @if ($isCompanyGstApplicable)
                        <td class="table-headings-font-size whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:50px;" >
                            {{ $changeLabel['expense_taxable_value'] ?? 'Taxable Value' }}
                        </td>
                        @else
                        <td class="table-headings-font-size whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold ; width:50px;">
                            {{ $changeLabel['expense_taxable_value'] ?? 'Amount' }}
                        </td>
                    @endif
                    </tr>
                    @foreach ($transactionItems as $key => $item)
                    @php
                        $uniqueId = ++$key;
                        $ids = collect($customFieldItemsValues)->pluck('custom_field_item_id')->values();
                        $printCustomFields = collect($item['customItemsValues'])->where('is_show_in_print', true)->whereIn('custom_field_item_id', $ids)->values();
                        foreach ($item['customItemsValues'] as $customField) {
                            $cfId = $customField['custom_field_id'];
                            if ($customField['show_total'] && $customField['custom_field_type'] == \App\Models\ItemCustomField::CF_TYPE_NUMBER) {
                                $value = (float) $customField['value'] ?? 0;
                                if (! isset($cfNumberTypeFieldTotals[$cfId])) {
                                    $cfNumberTypeFieldTotals[$cfId] = 0;
                                }
                                $cfNumberTypeFieldTotals[$cfId] += $value;
                            } else {
                                $cfNumberTypeFieldTotals[$cfId] = null;
                            }
                        }
                        $customItemsInventoryValues = $item['customItemsInventoryValues'] ?? [];
                    @endphp
                    <tr>
                        <td class="table-contents-font-size border-right text-center" style="padding: 4px 8px 0 8px;">
                            {{ $uniqueId }}
                        </td>
                        <td class="table-contents-font-size border-right fw-6" style="padding: 4px 8px 0 8px;">
                            {{ $item->items->item_name ?? null }}
                            @if ($item->items->sku != null && ($invoiceSetting['show_expense_item_sku'] ?? true))
                                <p class="description-font-size">Item Code:
                                    {{ $item->items->sku ?? null }}</p>
                            @endif
                            <p style="word-break: break-word;" class="description-font-size">
                                {!! !empty($item->consolidating_items_to_invoice) ? '(' . consolidatingItemsToInvoice($item->consolidating_items_to_invoice) . ')' : null !!}
                            </p>
                            <p style="word-break: break-word;" class="description-font-size">
                                {!! !empty($item->additional_description) ? nl2br('(' . $item->additional_description . ')') : null !!}
                            </p>
                            @if (count($customItemsInventoryValues) > 0)
                                @foreach ($customItemsInventoryValues as $customItemsInventoryValue)
                                    <p style="word-break: break-word; font-style: italic; color: #888888;" class="description-font-size">{{ $customItemsInventoryValue }}</p>
                                @endforeach
                            @endif
                        </td>
                        @if (count($printCustomFields) > 0)
                            @foreach ($printCustomFields as $customFieldItemsValue)
                                <td class="table-contents-font-size whitespace-nowrap text-start border-right" style="padding: 4px 8px 0 8px;">
                                    {{ $customFieldItemsValue['value'] ?? '' }}
                                </td>
                            @endforeach
                        @endif
                        @if ($isCompanyGstApplicable)
                            @if($showPrintSettings['show_purchase_hsn_sac'] ?? true)
                                <td class="table-contents-font-size whitespace-nowrap text-start border-right" style="padding: 4px 8px 0 8px;">
                                    {{ $item->hsn_code ?? $item->items->model->hsn_sac_code ?? null }}
                                </td>
                            @endif
                            @if($showPrintSettings['show_purchase_gst'] ?? true)
                                <td class="table-contents-font-size border-right whitespace-nowrap text-center" style="padding: 4px 8px 0 8px;">
                                    {{ $item->gst_tax_percentage ?? '0.0' }}
                                </td>
                            @endif
                        @endif
                        @if($showPrintSettings['show_purchase_qty'] ?? true)
                            <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                {{ $item->primary_quantity }}
                                @if($showPrintSettings['show_purchase_unit'] ?? true)
                                {{ $item->primary_unit_name }}
                                @endif
                            </td>
                        @endif
                        @if($showPrintSettings['expense_uom_enable'] ?? false)
                            @if ($transactionItems->sum('secondary_quantity') != 0.0)
                                <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                    {{ round( round( $item->secondary_quantity,2),2) }}
                                    @if($showPrintSettings['show_purchase_unit'] ?? true)
                                        {{ $item->secondary_unit_name }}
                                    @endif
                                </td>
                            @endif
                        @endif
                        @if($showPrintSettings['show_purchase_mrp'] ?? true)
                            @if ($transactionItems->sum('mrp') != 0.0)
                                <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                    {{ !empty($item->mrp) ? $pdfSymbol.getCurrencyFormatFor3digit($item->mrp) : '-' }}
                                </td>
                            @endif
                        @endif
                        @if ($isCompanyGstApplicable)
                        @if($showPrintSettings['show_purchase_rate_with_gst'] ?? false)
                        <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                            {{ $pdfSymbol.getCurrencyFormatFor3digit($item->rpu_with_gst) }}
                        </td>
                        @endif
                        @endif
                        @if($showPrintSettings['show_purchase_rate'] ?? true)
                            <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                {{ $pdfSymbol.getCurrencyFormatFor3digit($item->rpu_without_gst) }}
                            </td>
                        @endif

                        @if ($transactionItems->sum('discount_value') != 0.0)
                            @if($showPrintSettings['show_purchase_discount'] ?? true)
                                @if ($item->discount_type == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                        {{ $pdfSymbol.getCurrencyFormatFor3digit($item->discount_value) ?? '0.0' }}
                                    </td>
                                @else
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                        {{ $item->discount_value . '(%)' ?? '0.0(%)' }}
                                    </td>
                                @endif
                            @endif
                        @endif
                        @if ($transactionItems->sum('discount_value_2') != 0.0)
                            @if($showPrintSettings['expense_dis_2_enable'] ?? true)
                                @if ($item->discount_type_2 == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                        {{ $pdfSymbol.getCurrencyFormatFor3digit($item->discount_value_2) ?? '0.0' }}
                                    </td>
                                @else
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                        {{ $item->discount_value_2 . '(%)' ?? '0.0(%)' }}
                                    </td>
                                @endif
                            @endif
                        @endif
                        @if ($transactionItems->sum('total_discount_amount') != 0.0)
                            @if($showPrintSettings['show_purchase_total_discount'] ?? true)
                                <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                    {{ $pdfSymbol.getCurrencyFormatFor3digit($item->total_discount_amount) ?? '0.0' }}
                                </td>
                            @endif
                        @endif
                        <td class="table-contents-font-size whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;" >
                            {{ $pdfSymbol.getCurrencyFormat(round($item->total ?? '0.0', getCompanyFixedDigitNumber())) }}
                        </td>
                    </tr>
                    @endforeach

                    <tr class="border-bottom">
                    <td class="table-headings-font-size border-right text-center fw-6"></td>
                    <td class="table-headings-font-size border-right fw-6" style="padding: 4px 8px;">
                        Total
                    </td>
                    @if (count($customFieldItemsValues) > 0)
                        @foreach ($customFieldItemsValues as $customFieldItemsValue)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-start fw-6" style="padding: 4px 8px;">
                                {{ $cfNumberTypeFieldTotals[$customFieldItemsValue['custom_field_id']] ?? '' }}
                            </td>
                        @endforeach
                    @endif
                    @if ($isCompanyGstApplicable)
                        @if($showPrintSettings['show_purchase_hsn_sac'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-center fw-6" style="padding: 4px 8px;"></td>
                        @endif
                        @if($showPrintSettings['show_purchase_gst'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-center fw-6" style="padding: 4px 8px;"></td>
                        @endif
                    @endif

                    @if($showPrintSettings['show_purchase_qty'] ?? true)
                        <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6" style="padding: 4px 8px;">
                            {{ getCurrencyFormat($transactionItems->sum('primary_quantity')) }}
                        </td>
                    @endif
                    @if($showPrintSettings['expense_uom_enable'] ?? false)
                        @if ($transactionItems->sum('secondary_quantity') != 0.0)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6" style="padding: 4px 8px;">
                                {{ getCurrencyFormat($transactionItems->sum('secondary_quantity')) }}
                            </td>
                        @endif
                    @endif
                    @if($showPrintSettings['show_purchase_mrp'] ?? true)
                        @if ($transactionItems->sum('mrp') != 0.0)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6" style="padding: 4px 8px;">
                            </td>
                        @endif
                    @endif
                    @if ($isCompanyGstApplicable)
                    @if($showPrintSettings['show_purchase_rate_with_gst'] ?? false)
                        <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6" style="padding: 4px 8px;"></td>
                    @endif
                    @endif
                    @if($showPrintSettings['show_purchase_rate'] ?? true)
                        <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6" style="padding: 4px 8px;"></td>
                    @endif
                    @if ($transactionItems->sum('discount_value') != 0.0)
                        @if($showPrintSettings['show_purchase_discount'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6"style="padding: 4px 8px;"></td>
                        @endif
                    @endif
                    @if ($transactionItems->sum('discount_value_2') != 0.0)
                        @if($showPrintSettings['expense_dis_2_enable'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6"style="padding: 4px 8px;"></td>
                        @endif
                    @endif
                    @if ($transactionItems->sum('total_discount_amount') != 0.0)
                        @if($showPrintSettings['show_purchase_total_discount'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6"style="padding: 4px 8px;">
                                {{ $pdfSymbol.getCurrencyFormat($transactionItems->sum('total_discount_amount' ?? 0.0)) }}
                            </td>
                        @endif
                    @endif
                    <td class="table-headings-font-size whitespace-nowrap text-end fw-6"style="padding: 4px 8px;">
                        {{ $pdfSymbol.getCurrencyFormat($transaction->gross_value) }}
                    </td>
                    </tr>
                </table>
            @endif
            {{-- Item Table Section End --}}

            {{-- Accounting Invoice Section Start--}}
            @if($itemType == \App\Models\SaleTransaction::ACCOUNTING_INVOICE)
                @php
                    $customFieldLedgerValues = collect($transactionLedgers[0]['customLedgerValues'])->where('is_show_in_print', true)->values();
                    $customFieldLedgerHeaders = collect($customFieldLedgerValues)->pluck('label_name')->toArray();
                @endphp
                <table cellpadding="0" style="flex-grow: 1;"  class="item-table">
                <tr class="border-bottom">
                    <td class="table-headings-font-size border-right text-center" style="padding: 6px 8px;font-weight: bold; width:25px;">
                        {{ $changeLabel['expense_sn'] ?? 'SN' }}
                    </td>
                    <td class="table-headings-font-size border-right whitespace-nowrap" style="padding: 6px 8px;font-weight:bold;">
                        Particulars
                    </td>
                    @if (count($customFieldLedgerHeaders) > 0)
                        @foreach ($customFieldLedgerHeaders as $customFieldLedgerHeader)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-start"style="padding: 6px 8px; font-weight: bold; width:50px;">
                                {{ $customFieldLedgerHeader ?? '' }}
                            </td>
                        @endforeach
                    @endif
                    @if ($isCompanyGstApplicable)
                    @if($showPrintSettings['show_purchase_hsn_sac'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-start"style="padding: 6px 8px; font-weight: bold; width:50px;">
                                {{ $changeLabel['expense_hsn_sac'] ?? 'HSN/SAC' }}
                            </td>
                        @endif
                        <td class="table-headings-font-size border-right whitespace-nowrap text-center"style="padding: 6px 8px; font-weight: bold; width: 80px;" >
                            {{ $changeLabel['expense_gst'] ?? 'GST (%)' }}
                        </td>
                        @if($showPrintSettings['show_purchase_rate_with_gst'] ?? false)
                        <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:100px;">
                            {{ $changeLabel['expense_rate_with_gst'] ?? 'Rate With GST' }}
                        </td>
                        @endif
                    @endif
                    <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:100px;">
                        {{ $changeLabel['expense_rate'] ?? 'Rate' }}
                    </td>
                    @if ($transactionLedgers->sum('discount_value') != 0.0)
                        @if($showPrintSettings['show_purchase_discount'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width: 80px;">
                                {{ $changeLabel['expense_discount'] ?? 'Dis.' }}
                            </td>
                        @endif
                    @endif
                    @if ($transactionLedgers->sum('discount_value_2') != 0.0)
                        @if($showPrintSettings['expense_dis_2_enable'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width: 80px;">
                                {{ $changeLabel['expense_dis_2_label'] ?? 'Dis. 2' }}
                            </td>
                        @endif
                    @endif
                    @if ($transactionLedgers->sum('total_discount_amount') != 0.0)
                        @if($showPrintSettings['show_purchase_total_discount'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width: 80px;">
                                {{ $changeLabel['expense_total_discount'] ?? 'Total Dis.' }}
                            </td>
                        @endif
                    @endif
                    @if ($isCompanyGstApplicable)
                        <td class="table-headings-font-size whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:100px;" >
                            {{ $changeLabel['expense_taxable_value'] ?? 'Taxable Value' }}
                        </td>
                        @else
                        <td class="table-headings-font-size whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold;width: 100px;">
                            {{ $changeLabel['expense_taxable_value'] ?? 'Amount' }}
                        </td>
                    @endif
                </tr>
                @foreach ($transactionLedgers as $key => $ledger)
                @php
                    $uniqueId = ++$key;
                    $ids = collect($customFieldLedgerValues)->pluck('custom_field_item_id')->values();
                    $printCustomFields = collect($ledger['customLedgerValues'])->whereIn('custom_field_item_id', $ids)->values();
                @endphp
                    <tr>
                        <td class="table-contents-font-size border-right text-center" style="padding: 4px 8px 0 8px;">
                            {{ $uniqueId }}
                        </td>
                        <td class="table-contents-font-size border-right fw-6" style="padding: 4px 8px 0 8px;">
                            {{ $ledger->ledgers->name ?? null }}
                            <p style="word-break: break-word;" class="description-font-size">
                                {!! !empty($ledger->additional_description) ? nl2br('(' . $ledger->additional_description . ')') : null !!}
                            </p>
                        </td>
                        @if (count($printCustomFields) > 0)
                            @foreach ($printCustomFields as $customFieldLedgerValue)
                                <td class="table-contents-font-size whitespace-nowrap text-start border-right" style="padding: 4px 8px 0 8px;">
                                    {{ $customFieldLedgerValue['value'] ?? '' }}
                                </td>
                            @endforeach
                        @endif
                        @if ($isCompanyGstApplicable)
                        @if($showPrintSettings['show_purchase_hsn_sac'] ?? true)
                            <td class="table-contents-font-size whitespace-nowrap text-start border-right" style="padding: 4px 8px 0 8px;">
                                {{ $ledger->ledgers->model->hsn_sac_code ?? null }}
                            </td>
                        @endif
                        <td class="table-contents-font-size border-right whitespace-nowrap text-center" style="padding: 4px 8px 0 8px;">
                            {{ $ledger->gst_tax_percentage ?? '0.0' }}
                        </td>
                        @if($showPrintSettings['show_purchase_rate_with_gst'] ?? false)
                        <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                            {{ $pdfSymbol.getCurrencyFormatFor3digit($ledger->rpu_with_gst) }}
                        </td>
                        @endif
                        @endif
                        <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">

                            {{ $pdfSymbol.getCurrencyFormatFor3digit($ledger->rpu_without_gst) }}
                        </td>
                        @if ($transactionLedgers->sum('discount_value') != 0.0)
                            @if($showPrintSettings['show_purchase_discount'] ?? true)
                                @if ($ledger->discount_type == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                        {{ $pdfSymbol.getCurrencyFormatFor3digit($ledger->discount_value) ?? '0.0' }}
                                    </td>
                                @else
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                        {{ $ledger->discount_value . '(%)' ?? '0.0(%)' }}
                                    </td>
                                @endif
                            @endif
                        @endif
                        @if ($transactionLedgers->sum('discount_value_2') != 0.0)
                            @if($showPrintSettings['expense_dis_2_enable'] ?? true)
                                @if ($ledger->discount_type_2 == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                        {{ $pdfSymbol.getCurrencyFormatFor3digit($ledger->discount_value_2) ?? '0.0' }}
                                    </td>
                                @else
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                        {{ $ledger->discount_value_2 . '(%)' ?? '0.0(%)' }}
                                    </td>
                                @endif
                            @endif
                        @endif
                        @if ($transactionLedgers->sum('total_discount_amount') != 0.0)
                            @if($showPrintSettings['show_purchase_total_discount'] ?? true)
                                <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                                    {{ $pdfSymbol.getCurrencyFormatFor3digit($ledger->total_discount_amount) ?? '0.0' }}
                                </td>
                            @endif
                        @endif
                        <td class="table-contents-font-size whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;" >
                            {{ $pdfSymbol.getCurrencyFormat(round($ledger->total ?? '0.0', getCompanyFixedDigitNumber())) }}
                        </td>
                    </tr>
                @endforeach

                <tr class="border-bottom">
                    <td class="table-headings-font-size border-right text-center fw-6" style=""></td>
                    <td class="table-headings-font-size border-right fw-6" style="padding: 4px 8px;">
                        Total
                    </td>
                    @if (count($customFieldLedgerValues) > 0)
                        @foreach ($customFieldLedgerValues as $customFieldLedgerValue)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-center fw-6" style="padding: 4px 8px; "></td>
                        @endforeach
                    @endif
                    @if ($isCompanyGstApplicable)
                    @if($showPrintSettings['show_purchase_hsn_sac'] ?? true)
                        <td class="table-headings-font-size border-right whitespace-nowrap text-center fw-6" style="padding: 4px 8px;"></td>
                    @endif
                    <td class="table-headings-font-size border-right whitespace-nowrap text-center fw-6" style="padding: 4px 8px;"></td>
                    @if($showPrintSettings['show_purchase_rate_with_gst'] ?? false)
                    <td class="table-headings-font-size border-right whitespace-nowrap text-center fw-6" style="padding: 4px 8px;"></td>
                    @endif
                    @endif
                    <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6" style="padding: 4px 8px;"></td>

                    @if ($transactionLedgers->sum('discount_value') != 0.0)
                        @if($showPrintSettings['show_purchase_discount'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6" style="padding: 4px 8px;"></td>
                        @endif
                    @endif
                    @if ($transactionLedgers->sum('discount_value_2') != 0.0)
                        @if($showPrintSettings['expense_dis_2_enable'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6" style="padding: 4px 8px;"></td>
                        @endif
                    @endif
                    @if ($transactionLedgers->sum('total_discount_amount') != 0.0)
                        @if($showPrintSettings['show_purchase_total_discount'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6"style="padding: 4px 8px;">
                                {{ $pdfSymbol.getCurrencyFormat($transactionLedgers->sum('total_discount_amount' ?? 0.0)) }}
                            </td>
                        @endif
                    @endif
                    <td class="table-headings-font-size whitespace-nowrap text-end fw-6"style="padding: 4px 8px;">
                        {{ $pdfSymbol.getCurrencyFormat($transaction->gross_value) }}
                    </td>
                </tr>
                </table>
            @endif
            {{-- Accounting Invoice Section End--}}

            {{-- Bank / Terms Of Payment / Broker Section Start --}}
            <table cellpadding="0">
                <tr>
                    <td>
                        <div>
                            @if (getCustomInvoiceSetting('expense_broker_details'))
                            <table>
                                <tr class="vertical-top">
                                    <td class="header-contents-font-size vertical-top fw-6" style="padding: 5px 8px 0px 8px;">
                                        Broker:
                                    </td>
                                    <td class="header-contents-font-size vertical-bottom" style="padding: 5px 8px 0px 0px white-space: normal;">

                                        {{ $transaction->brokerDetails->broker_name ?? '' }}
                                    </td>
                                </tr>
                                @if($isCompanyGstApplicable)
                                <tr>
                                    <td class="header-contents-font-size whitespace-nowrap fw-6" style="padding: 3px 8px 1px 8px;">
                                        GSTIN:
                                    </td>
                                    <td class="header-contents-font-size" style="padding: 3px 8px 1px 0px;white-space: nowrap;">
                                        {{ $transaction->brokerDetails->gstin ?? '' }}
                                    </td>
                                </tr>
                                @endif
                            </table>
                            @endif
                        </div>
                    </td>
                    <td class="vertical-top" style="{{ $isA5Pdf ? 'width: 150px;' : 'width: 200px;' }} border-left: 1px solid black" >
                        <table>
                            @foreach ($additionalCharges as $additionalCharge)
                                <tr class="{{ $loop->last ? 'border-bottom' : '' }}">
                                    <td class="table-headings-font-size vertical-top" style="padding: 4px 8px 2px 8px;">
                                        {{ $additionalCharge['ledger_name'] }}
                                    </td>
                                    <td class="table-headings-font-size vertical-top text-end" style="padding: 4px 8px 2px 8px;">
                                        {{ $pdfSymbol.getCurrencyFormat($additionalCharge['amount'] ?? '0.0') }}
                                    </td>
                                </tr>
                            @endforeach
                            <tr>
                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 4px 8px 2px 8px;">
                                    {{ $isCompanyGstApplicable ? $changeLabel['expense_sub_total'] ?? 'Taxable Value' : $changeLabel['expense_sub_total'] ?? 'Sub Total' }}:
                                </td>
                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 4px 8px 2px 8px;">
                                    {{ $pdfSymbol.getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber())) }}
                                </td>
                            </tr>
                            @if ($isCompanyGstApplicable)
                                @if ($transaction->cgst != 0)
                                    <tr>
                                        <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 2px 8px;">
                                            {{ $changeLabel['expense_cgst'] ?? 'CGST' }}:
                                        </td>
                                        <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 2px 8px;">
                                            {{ $pdfSymbol.getCurrencyFormat(round($transaction->cgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endif
                                @if ($transaction->sgst != 0)
                                    <tr>
                                        <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 2px 8px;">
                                            {{ $changeLabel['expense_sgst'] ?? 'SGST' }}:
                                        </td>
                                        <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 2px 8px;">
                                            {{ $pdfSymbol.getCurrencyFormat(round($transaction->sgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endif
                                @if ($transaction->igst != 0)
                                    <tr>
                                        <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 2px 8px;">
                                            {{ $changeLabel['expense_igst'] ?? 'IGST' }}:
                                        </td>
                                        <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 2px 8px;">
                                            {{ $pdfSymbol.getCurrencyFormat(round($transaction->igst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endif
                            @endif
                            @if ($transaction->tcs_amount != 0)
                            <tr>
                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 2px 8px;">
                                    {{ $changeLabel['expense_tcs'] ?? 'TCS' }}:
                                </td>
                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 2px 8px;">
                                    {{ $pdfSymbol.getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                </td>
                            </tr>
                            @endif
                            @if ($transaction->cess != 0)
                            <tr>
                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 2px 8px;">
                                    {{ $changeLabel['expense_cess'] ?? 'Cess' }}:
                                </td>
                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 2px 8px;">
                                    {{ $pdfSymbol.getCurrencyFormat($transaction->cess ?? '0.0') }}
                                </td>
                            </tr>
                            @endif
                            @if ($transaction->rounding_amount != 0)
                            <tr>
                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 4px 8px;">
                                    {{ $changeLabel['expense_round_off'] ?? 'Round off' }}:
                                </td>
                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 4px 8px;">
                                    {{ $pdfSymbol.getCurrencyFormat($transaction->rounding_amount ?? '0.0') }}
                                </td>
                            </tr>
                            @endif
                            @php
                                if (empty($addLess)) {
                                    $total = $transaction->grand_total;
                                } else {
                                    $addLessSum = array_sum(array_column($addLess, 'amount'));
                                    $total =  $transaction->grand_total - $addLessSum;
                                    $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                                    $total = $total + $addLessSumTotal;
                                }
                            @endphp
                            @foreach (collect($addLess)->where('is_show_in_print',1) as $addLessItem)
                                <tr class="{{ $loop->first ? 'border-top' : '' }}">
                                    <td class="table-headings-font-size vertical-top"
                                        style="padding: 3px 8px 4px 8px;">
                                        {{ $addLessItem['ledger_name'] }}
                                    </td>
                                    <td class="table-headings-font-size vertical-top text-end"
                                        style="padding: 3px 8px 4px 8px;">
                                        {{ $pdfSymbol.getCurrencyFormat($addLessItem['amount'] ?? '0.0') }}
                                    </td>
                                </tr>
                            @endforeach
                        </table>
                    </td>
                </tr>
            </table>
            {{-- Bank / Terms Of Payment / Broker Section End --}}

            {{-- Total Section Start --}}
            <table cellpadding="0">
                <tr class="{{ ($showPrintSettings['show_purchase_narration'] ?? true) || ($showPrintSettings['show_purchase_authorized_signatory'] ?? true) || ($invoiceSetting['expense_signature'] ?? true) ? 'border-bottom' : '' }}">
                    <td class="border-right vertical-middle {{ ($showPrintSettings['show_purchase_in_words'] ?? true) ? 'border-top' : '' }}">
                        @if($showPrintSettings['show_purchase_in_words'] ?? true)
                            <p class="table-contents-font-size fw-6" style="display: flex; padding: 6px 3px 6px 8px;">
                                {{ $changeLabel['expense_in_words'] ?? 'In Words' }}:
                                <span class="table-contents-font-size" style="margin-left: 3px; font-weight: 400;">
                                    {{ getAmountToWord($total ?? '0.0') }} Only
                                </span>
                            </p>
                        @endif
                    </td>
                    <td class="border-top" style="{{ $isA5Pdf ? 'width: 150px;' : 'width: 200px;' }}">
                        <table class="vertical-bottom">
                            <tr>
                                <td class="text-primary total-font-size" style="padding: 6px 8px;font-weight: bold;">
                                    {{ $changeLabel['total'] ?? 'Total' }}:
                                </td>
                                <td class="text-primary text-end total-font-size" style="padding: 6px 8px;font-weight: bold;">
                                    {{ $pdfSymbol.getCurrencyFormat($total ?? '0.0') }}
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            {{-- Total Section End --}}

            {{-- GST Details Section Start --}}
            @if ($isCompanyGstApplicable && !empty($checkHsnCodeExist) && ($invoiceSetting['expense_hsn_summary'] ?? true))
                <table cellpadding="0">
                <tr class="border-bottom" style="width: 100%">
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                        SN
                    </td>
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                        HSN/SAC
                    </td>
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                        Taxable Amount
                    </td>
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                        GST (%)
                    </td>
                    @if ($cgst != 0.0)
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                        CGST
                    </td>
                    @endif
                    @if ($sgst != 0.0)
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                        SGST
                    </td>
                    @endif
                    @if ($igst != 0.0)
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                        IGST
                    </td>
                    @endif
                    <td class="footer-headings-font-size text-center" style="padding: 4px 8px; font-weight: bold;">
                        Total Tax
                    </td>
                </tr>
                @php
                    $uniquekey = 1;
                @endphp
                @foreach ($checkHsnCodeExist as $key => $item)
                    @foreach ($item as $hsnCode => $data)
                        <tr>
                            <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px;">
                                {{ $uniquekey++ }}
                            </td>
                            <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px;">
                                {{ !empty($hsnCode) ? $hsnCode : '-' }}
                            </td>
                            <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px;">
                                {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['taxableValue'][$hsnCode], getCompanyFixedDigitNumber()) ?? 0) }}
                            </td>
                            <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px;">
                                {{ !empty($key) ? $key : '-' }}
                            </td>
                            @if ($cgst != 0.0)
                                <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px;">
                                    {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['cgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @endif
                            @if ($sgst != 0.0)
                            <td class="footer-contents-font-size border-right text-center"style="padding: 2px 8px;">
                                {{$pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['sgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                            </td>
                            @endif
                            @if ($igst != 0.0)
                            <td class="footer-contents-font-size border-right text-center"style="padding: 2px 8px;">
                                {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['igst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                            </td>
                            @endif
                            @php
                                $totalTax = round($checkTAXtExist[$key]['cgst'][$hsnCode], getCompanyFixedDigitNumber()) + round($checkTAXtExist[$key]['sgst'][$hsnCode], getCompanyFixedDigitNumber()) + round($checkTAXtExist[$key]['igst'][$hsnCode], getCompanyFixedDigitNumber());
                            @endphp
                            <td class="footer-contents-font-size text-center" style="padding: 2px 8px;">
                                {{ $pdfSymbol.getCurrencyFormat(round($totalTax ?? 0, getCompanyFixedDigitNumber())) }}
                            </td>
                        </tr>
                    @endforeach
                @endforeach
                <tr class="footer-headings-font-size border-bottom border-top fw-6" style="padding: 2px 8px;">
                    <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px;"></td>
                    <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px;">
                        Total
                    </td>
                    <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px;">
                        {{ $pdfSymbol.getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber())) }}
                    </td>
                    <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px;"></td>
                    @if ($cgst != 0.0)
                    <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px;">
                        {{ $pdfSymbol.getCurrencyFormat(round($cgst, getCompanyFixedDigitNumber())) }}
                    </td>
                    @endif
                    @if ($sgst != 0.0)
                        <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px;">
                            {{ $pdfSymbol.getCurrencyFormat(round($sgst, getCompanyFixedDigitNumber())) }}
                        </td>
                    @endif
                    @if ($igst != 0.0)
                        <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px;">
                            {{ $pdfSymbol.getCurrencyFormat(round($igst, getCompanyFixedDigitNumber())) }}
                        </td>
                    @endif
                    <td class="footer-headings-font-size text-center fw-6" style="padding: 4px 8px;">
                        @php
                            $grandTotalTax = $cgst + $sgst + $igst;
                        @endphp
                        {{ $pdfSymbol.getCurrencyFormat(round($grandTotalTax, getCompanyFixedDigitNumber())) }}
                    </td>
                </tr>
                </table>
            @endif
            {{-- GST Details Section End --}}

            {{-- Narration / Signature Section Start --}}
            <table cellpadding="0" style="page-break-inside: avoid !important">
                <tr>
                    @if($showPrintSettings['show_purchase_narration'] ?? true)
                        @if($transaction->narration)
                            <td class="vertical-top border-right">
                                @if ($transaction->narration)
                                    <div class="">
                                        <h4 class="note-font-size border-bottom fw-6" style="padding: 4px 8px;">
                                            {{ $changeLabel['expense_narration'] ?? 'Notes' }}:
                                        </h4>
                                        <div style="padding: 4px 8px;">
                                            <p class="note-font-size">
                                                {!! nl2br($transaction->narration) !!}
                                            </p>
                                        </div>
                                    </div>
                                @endif
                            </td>
                        @endif
                    @endif
                    @if(($showPrintSettings['show_purchase_authorized_signatory'] ?? true) || ($invoiceSetting['expense_signature'] ?? true))
                        <td class="vertical-bottom" style="width: 217px; position: relative">
                            <div style="padding:4px 8px; margin-left:auto;">
                                @if ($showPrintSettings['show_purchase_authorized_signatory'] ?? true)
                                <p class="footer-headings-font-size fw-6 text-end" style="margin-top:10px;">
                                    For, {{ strtoupper($currentCompany->trade_name) }}
                                </p>
                                @endif
                                <div class="text-end signature">
                                    @if (($invoiceSetting['expense_signature'] ?? true) && ($currentCompany->company_signature != asset('images/preview-img.png')))
                                        <img src="{{ $currentCompany->company_signature ?? null }}" alt="company-img" style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;">
                                    @endif
                                </div>
                                @if ($showPrintSettings['show_purchase_authorized_signatory'] ?? true)
                                <p class="verical-bottom text-end footer-headings-font-size" style="">
                                    {{ $changeLabel['expense_authorized_signatory'] ?? 'Authorized Signatory' }}
                                </p>
                                @endif
                            </div>
                        </td>
                    @endif
                </tr>
            </table>
            {{-- Narration / Signature Section End --}}
        </div>
    </body>
</html>
