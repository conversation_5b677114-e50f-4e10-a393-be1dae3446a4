<?php

namespace App\Actions\CustomFieldsItemMaster\Inventory;

use App\Models\ExpenseCreditNoteItemTransaction;
use App\Models\ExpenseDebitNoteItemTransaction;
use App\Models\IncomeCreditNoteItemTransaction;
use App\Models\IncomeDebitNoteItemTransaction;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\Master\ItemMaster;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseReturnItemTransaction;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleTransactionItem;
use Lorisleiva\Actions\Concerns\AsAction;

class CalculateAvailableQTY
{
    use AsAction;

    public function handle($combination)
    {
        $inwardInventory = ItemCustomFieldCombinationInventory::where('item_custom_field_combination_id', $combination->id)
            ->whereIn('model_type', [
                PurchaseItemTransaction::class,
                ExpenseCreditNoteItemTransaction::class,
                SaleReturnItemTransaction::class,
                IncomeCreditNoteItemTransaction::class,
                ItemMaster::class,
            ])
            ->sum('quantity');

        $outwardInventory = ItemCustomFieldCombinationInventory::where('item_custom_field_combination_id', $combination->id)
            ->whereIn('model_type', [
                SaleTransactionItem::class,
                IncomeDebitNoteItemTransaction::class,
                PurchaseReturnItemTransaction::class,
                ExpenseDebitNoteItemTransaction::class
            ])
            ->sum('quantity');

        $combination->available_quantity = $inwardInventory - $outwardInventory;
        $combination->save();

        return $combination;
    }
}
