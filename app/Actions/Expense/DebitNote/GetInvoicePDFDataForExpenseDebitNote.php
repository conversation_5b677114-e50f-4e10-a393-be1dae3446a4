<?php

namespace App\Actions\Expense\DebitNote;

use App\Actions\CommonAction\PreparePDFItemDataAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\PrepareCustomFieldInventoryDataForPdfPreview;
use App\Actions\CustomFieldsItemMaster\Transaction\PrepareCustomFieldDataForPdfPreview;
use App\Actions\v1\PdfConfiguration\GetPdfAdjustmentsAction;
use App\Models\Address;
use App\Models\Company;
use App\Models\CompanySetting;
use App\Models\Configuration\ExpenseDebitNote;
use App\Models\ExpenseDebitNoteItemTransaction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\InvoicesLabel;
use App\Models\ItemCustomField;
use App\Models\Master\ExpenseDebitNoteTransactionMaster;
use App\Models\PrintSetting;
use App\Models\PurchaseTransaction;
use App\Models\TransactionCustomField;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class GetInvoicePDFDataForExpenseDebitNote
{
    use AsAction;

    public function handle($debitNoteId)
    {
        $data = [];
        $data['taxInvoice'] = 'Voucher No';
        $data['transaction'] = ExpenseDebitNoteTransaction::with(
            'addresses',
            'supplier.model',
            'transport',
            'debitNoteItems.items',
            'debitNoteLedgers.ledgers',
            'addLess.ledger',
            'additionalCharges.ledger'
        )->whereId($debitNoteId)->firstOrFail();
        $data['currentCompany'] = Company::with('billingAddress', 'companyTax', 'user', 'mailConfiguration')->findOrFail($data['transaction']->company_id);
        session(['current_company' => $data['currentCompany']]);

        // Process Add/Less and Additional Charges
        $data['addLess'] = prepareAddLess($data['transaction']->addLess);
        $data['additionalCharges'] = prepareAdditionalCharges($data['transaction']->additionalCharges);

        // Custom fields
        $data['customFieldValues'] = GetTransactionCustomFieldsAction::run(TransactionCustomField::EXPENSE_DEBIT_NOTE, $debitNoteId, ExpenseDebitNoteTransaction::class);
        $purchaseTransaction = PurchaseTransaction::whereId($data['transaction']->original_inv_no)->first();
        $data['originalInvoiceNumber'] = $purchaseTransaction->voucher_number ?? null;
        $data['originalInvoiceDate'] = $purchaseTransaction->voucher_date ?? null;

        $data['companyBillingAddress'] = $data['currentCompany']->addresses->where('address_type', Company::BILLING_ADDRESS)->first();
        $data['configuration'] = ExpenseDebitNote::first();
        $data['transactionItems'] = PreparePDFItemDataAction::run($data['transaction']->debitNoteItems);
        if (! empty($data['transactionItems'])) {
            foreach ($data['transactionItems'] as $key => $item) {
                $data['transactionItems'][$key]['customItemsValues'] = PrepareCustomFieldDataForPdfPreview::run(ItemCustomField::EXPENSE_DEBIT_NOTE, $item->id, ExpenseDebitNoteItemTransaction::class);
                if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                    $data['transactionItems'][$key]['customItemsInventoryValues'] = PrepareCustomFieldInventoryDataForPdfPreview::run($item->id, ExpenseDebitNoteItemTransaction::class, $item->item_id);
                }
            }
        }

        $data['transactionLedgers'] = $data['transaction']->debitNoteLedgers;

        $data['customerDetail'] = $data['transaction']->supplier;
        $data['ledgerShippingAddress'] = $data['customerDetail']->model->shippingAddress;
        $data['billingAddress'] = $data['transaction']->addresses->where('address_type', ExpenseDebitNoteTransaction::BILLING_ADDRESS)->first();
        if ($data['transaction']->same_as_billing) {
            $data['shippingAddress'] = $data['billingAddress'];
            $data['transaction']->shipping_gstin = $data['transaction']->gstin;
        } else {
            $data['shippingAddress'] = Address::whereId($data['transaction']->shipping_address_id)->first() ?? $data['transaction']->addresses->where('address_type', ExpenseDebitNoteTransaction::SHIPPING_ADDRESS)->first();
        }

        $data['invoiceDate'] = Carbon::Parse($data['transaction']->original_inv_date)->format('d-m-Y');
        $data['voucherDate'] = Carbon::Parse($data['transaction']->voucher_date)->format('d-m-Y');
        $data['invoiceNo'] = $data['transaction']->supplier_purchase_return_number;
        $data['itemType'] = $data['transaction']->dn_item_type;
        $data['dueDate'] = null;
        $data['creditPeriod'] = null;
        $data['isCompanyGstApplicable'] = $data['currentCompany']->is_gst_applicable ? true : false;

        $data['showGst'] = false;

        if (! $data['isCompanyGstApplicable']) {
            if (! empty($data['customerDetail']->model->gstin)) {
                $data['showGst'] = true;
            }
        } elseif (! empty($data['transaction']->gstin)) {
            $data['showGst'] = true;
        }

        $data['invoiceSetting'] = CompanySetting::pluck('value', 'key')->toArray();
        $data['changeLabel'] = InvoicesLabel::whereTransactionType(InvoicesLabel::EXPENSE_TRANSACTION)->pluck('label_value', 'label_name')->toArray();
        $data['panNumber'] = $data['customerDetail']->model->pan_card_number ?? null;
        $data['showPanNumber'] = isset($data['invoiceSetting']['expense_pan_no']) ? $data['invoiceSetting']['expense_pan_no'] : true;
        $expenseDebitNoteTransactionMaster = ExpenseDebitNoteTransactionMaster::first();
        $data['defaultPrintTitle'] = ! empty($expenseDebitNoteTransactionMaster) ? $expenseDebitNoteTransactionMaster->value('default_title_of_print') : null;
        $data['showPrintSettings'] = PrintSetting::pluck('status', 'name')->toArray();

        $isA5Pdf = isset($data['invoiceSetting']['expense_pdf_format']) ? $data['invoiceSetting']['expense_pdf_format'] == CompanySetting::A5 : false;
        $data['customFontSize'] = GetPdfAdjustmentsAction::run($isA5Pdf);
        $data['customProp'] = InvoicesLabel::where('is_custom_label', InvoicesLabel::PROP_NAME)->select('label_value', 'label_name')->first();

        return $data;
    }
}
