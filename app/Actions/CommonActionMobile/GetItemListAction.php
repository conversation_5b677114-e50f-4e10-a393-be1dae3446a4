<?php

namespace App\Actions\CommonActionMobile;

use App\Actions\CustomFieldsItemMaster\GetCustomFieldItemsValue;
use App\Actions\ItemMaster\GetItemClosingStock;
use App\Models\Master\ItemMaster;
use Lorisleiva\Actions\Concerns\AsAction;

class GetItemListAction
{
    use AsAction;

    public function handle($input = [])
    {
        $search = $input['search'] ?? null;
        $skip = $input['skip'] ?? 0;
        $sku = $input['sku'] ?? 0;
        $status = $input['status'] ?? null;
        $transactionType = $input['transaction_type'] ?? null;

        $query = ItemMaster::whereCompanyId(getCurrentCompany()->id);

        if ($search) {
            $words = explode(' ', $search);
            foreach ($words as $word) {
                $query->where(function ($q) use ($word) {
                    $q->whereRaw('lower(item_name) LIKE ?', ['%'.strtolower($word).'%'])->orWhere('sku', 'like', '%'.$word.'%');
                });
            }
        }

        if ($sku) {
            $query->where('sku', 'LIKE', '%'.$sku.'%');
        }

        if (! is_null($status) && in_array($status, [0, 1])) {
            $query->where('status', $status);
        }

        $itemMasters = $query->orderByDesc('is_favorite')
            ->orderBy('id', 'DESC')
            ->limit(50)
            ->offset($skip)
            ->get();

        $itemMasters = $itemMasters->map(function ($itemMaster) use ($transactionType) {
            $id = $itemMaster->id;
            $itemName = $itemMaster->item_name;
            $status = $itemMaster->status;
            $itemSKU = $itemMaster->sku;
            $methodOfValuation = ItemMaster::LAST_PURCHASE_COST;
            $itemData = GetItemClosingStock::run($id, [], $methodOfValuation);
            $itemOpeningStock = $itemData['opening_balance_amount'];
            $itemClosingStock = $itemData['closing_balance_qty'];
            $itemLastPurchaseCost = $itemData['inward_rate'];
            $itemLastSellingPrice = $itemData['outward_rate'];
            $itemBarcode = $itemMaster->getItemBarcodeAttribute();
            $itemMedia = $itemMaster->getItemImageAttribute();
            $customFieldsItemValues = $transactionType ? GetCustomFieldItemsValue::run($id, $transactionType) : [];

            return [
                'id' => $id,
                'item_name' => $itemName,
                'item_type' => $itemMaster->item_type,
                'description' => $itemMaster->model->description,
                'is_favorite' => $itemMaster->is_favorite,
                'status' => $status,
                'item_type_name' => $itemMaster->item_type == ItemMaster::ITEM_MASTER_GOODS ? 'Goods' : 'Service',
                'sku' => $itemSKU,
                'mrp' => $itemMaster->model->mrp ?? null,
                'group_id' => $itemMaster->group_id,
                'group_name' => $itemMaster->group->name ?? null,
                'gst_rate_id' => $itemMaster->model->gst_tax_id ?? null,
                'gst_rate' => $itemMaster->model->gstTax->tax_rate ?? null,
                'unit_of_measurement' => $itemMaster->model->unit_of_measurement ?? null,
                'income_ledger_id' => $itemMaster->model->income_ledger_id ?? null,
                'expense_ledger_id' => $itemMaster->model->expense_ledger_id ?? null,
                'gst_cess_rate' => $itemMaster->model->gst_cess_rate ?? null,
                'sale_price_type' => $itemMaster->model->sale_price_type ?? null,
                'selling_price_with_gst' => $itemMaster->model->selling_price_with_gst ?? null,
                'selling_price_without_gst' => $itemMaster->model->selling_price_without_gst ?? null,
                'opening_stock' => $itemOpeningStock,
                'closing_stock' => $itemClosingStock,
                'ledger_closing_balance' => 0,
                'ledger_closing_type' => null,
                'purchase_price_type' => $itemMaster->model->purchase_price_type ?? null,
                'purchase_price_with_gst' => $itemMaster->model->purchase_price_with_gst ?? null,
                'purchase_price_without_gst' => $itemMaster->model->purchase_price_without_gst ?? null,
                'discount_type' => $itemMaster->model->discount_type ?? null,
                'discount_value' => $itemMaster->model->discount_value ?? null,
                'purchase_discount_type' => $itemMaster->model->purchase_discount_type ?? null,
                'purchase_discount_value' => $itemMaster->model->purchase_discount_value ?? null,
                'secondary_unit_of_measurement' => $itemMaster->model->secondary_unit_of_measurement ?? null,
                'decimal_places' => $itemMaster->model->decimal_places ?? 2,
                'decimal_places_for_rate' => $itemMaster->model->decimal_places_for_rate ?? 2,
                'conversion_rate' => $itemMaster->model->conversion_rate ?? null,
                'barcode' => $itemBarcode,
                'media' => $itemMedia,
                'custom_fields_item_values' => $customFieldsItemValues,
            ];
        })->toArray();

        return $itemMasters;
    }
}
