<?php

namespace App\Models\Master;

use App\Actions\ItemMaster\GetItemClosingStock;
use App\Actions\Ledger\GetLedgerClosingBalanceAndType;
use App\Models\Company;
use App\Models\CompanyGroup;
use App\Models\Contracts\JsonResourceful;
use App\Models\Media;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseReturnItemTransaction;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleTransactionItem;
use App\Traits\HasCompany;
use App\Traits\HasJsonResourcefulData;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * App\Models\Master\ItemMaster
 *
 * @property int $id
 * @property int $company_id
 * @property string $item_name
 * @property int $group_id
 * @property int $item_type
 * @property int $model_id
 * @property string $model_type
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read array|null $commonData
 * @property-read Company $company
 * @property-read CompanyGroup $group
 * @property-read \App\Models\Master\ItemMasterGoods|\App\Models\Master\ItemMasterService $model
 * @property-read Collection|PurchaseItemTransaction[] $purchaseItems
 * @property-read int|null $purchase_items_count
 * @property-read Collection|PurchaseReturnItemTransaction[] $purchaseReturnItems
 * @property-read int|null $purchase_return_items_count
 * @property-read Collection|SaleTransactionItem[] $saleItems
 * @property-read int|null $sale_items_count
 * @property-read Collection|SaleReturnItemTransaction[] $saleReturnItems
 * @property-read int|null $sale_return_items_count
 *
 * @method static Builder|ItemMaster newModelQuery()
 * @method static Builder|ItemMaster newQuery()
 * @method static Builder|ItemMaster query()
 * @method static Builder|ItemMaster whereCompanyId($value)
 * @method static Builder|ItemMaster whereCreatedAt($value)
 * @method static Builder|ItemMaster whereGroupId($value)
 * @method static Builder|ItemMaster whereId($value)
 * @method static Builder|ItemMaster whereItemName($value)
 * @method static Builder|ItemMaster whereItemType($value)
 * @method static Builder|ItemMaster whereModelId($value)
 * @method static Builder|ItemMaster whereModelType($value)
 * @method static Builder|ItemMaster whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ItemMaster extends Model implements Auditable, HasMedia, JsonResourceful
{
    use HasCompany,HasJsonResourcefulData, InteractsWithMedia;
    use HasFactory;
    use \OwenIt\Auditing\Auditable;

    public const ITEM_MASTER_GOODS = 1;

    public const ITEM_MASTER_SERVICE = 2;

    // item type const
    public const ITEM_MASTER_TYPES = [
        self::ITEM_MASTER_GOODS => 'Goods',
        self::ITEM_MASTER_SERVICE => 'Service',
    ];

    public const IS_GST_APPLICABLE = 1;

    public const IS_GST_NOT_APPLICABLE = 0;

    public const IS_RCM_APPLICABLE = 1;

    public const IS_RCM_NOT_APPLICABLE = 0;

    public const AVERAGE_COST = 1;

    public const FIRST_IN_FIRST_OUT = 2;

    public const LAST_IN_FIRST_OUT = 3;

    public const LAST_PURCHASE_COST = 4;

    public const LAST_SELLING_PRICE = 5;

    public const METHOD_OF_STOCK_VALUATION = [
        self::AVERAGE_COST => 'Average Cost',
        self::FIRST_IN_FIRST_OUT => 'First in First Out',
        self::LAST_IN_FIRST_OUT => 'Last in First out',
        self::LAST_PURCHASE_COST => 'Last Purchase Cost',
        self::LAST_SELLING_PRICE => 'Last Selling Price',
    ];

    public const PRIMARY_UNIT = 1;

    public const SECONDARY_UNIT = 2;

    public const UNIT_TYPE = [
        self::PRIMARY_UNIT => 'Primary Unit',
        self::SECONDARY_UNIT => 'Secondary Unit',
    ];

    public const RPU_WITHOUT_GST = 1;

    public const RPU_WITH_GST = 2;

    public const RPU_TYPE = [
        self::RPU_WITHOUT_GST => 'Rate without GST',
        self::RPU_WITH_GST => 'Rate With GST',
    ];

    public const BARCODE = 'barcode';

    public const ITEM_IMAGE = 'item_image';

    public const SAVE_BUTTON = 1;

    public const SAVE_AND_NEW_BUTTON = 2;

    public const ITEM = 1;

    public const NEGATIVE_ITEM = 2;

    public const STOCK_ITEM_TYPE = [
        self::ITEM => 'Items',
        self::NEGATIVE_ITEM => 'Negative Items',
    ];

    public $table = 'item_masters';

    public $fillable = [
        'company_id',
        'item_name',
        'group_id',
        'item_type',
        'model_type',
        'model_id',
        'sku',
        'via_api',
        'is_import',
        'vastra_id',
        'status',
        'is_use_in_other_companies',
        'is_favorite',
    ];

    public $attribute = ['item_barcode'];

    protected $appends = ['item_image'];

    public function transformAudit(array $data): array
    {
        if ($data['event'] == 'created') {
            Arr::set($data, 'title', '<b>'.$this->getAttribute('item_name').'</b> Item was created.');
        } elseif ($data['event'] == 'updated') {
            Arr::set($data, 'title', '<b>'.$this->getAttribute('item_name').'</b> Item was edited.');
        } elseif ($data['event'] == 'deleted') {
            Arr::set($data, 'title', '<b>'.$this->getAttribute('item_name').'</b> Item was deleted.');
        }

        return $data;
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(CompanyGroup::class, 'group_id', 'id');
    }

    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function purchaseItems()
    {
        return $this->hasMany(PurchaseItemTransaction::class, 'item_id', 'id');
    }

    public function purchaseReturnItems()
    {
        return $this->hasMany(PurchaseReturnItemTransaction::class, 'item_id', 'id');
    }

    public function saleItems()
    {
        return $this->hasMany(SaleTransactionItem::class, 'item_id', 'id');
    }

    public function saleReturnItems()
    {
        return $this->hasMany(SaleReturnItemTransaction::class, 'item_id', 'id');
    }

    public function prepareLinks(): array
    {
        return [
            //
        ];
    }

    public function relationLinks(): array
    {
        return [
            //
        ];
    }

    public function prepareAttributes(): array
    {
        $commonCode = $this->commonData;
        $itemData = GetItemClosingStock::run($this, $commonCode['openingStock']);
        $ledgerData = GetLedgerClosingBalanceAndType::run($this->id);

        $openingStock = 0;
        if (isset($commonCode['financialYearOpeningBalances'][$this->id])) {
            $openingStock = $commonCode['financialYearOpeningBalances'][$this->id]->opening_balance_qty
                * $commonCode['financialYearOpeningBalances'][$this->id]->opening_balance_rate;
        }
        // else {
        //     $openingStock = ($this->model->quantity * $this->model->rate) ?? 0;
        // }

        return [
            'id' => $this->id,
            'item_name' => $this->item_name,
            'item_type' => $this->item_type,
            'description' => $this->model->description ?? null,
            'item_type_name' => ! empty($this->item_type) ? ItemMaster::ITEM_MASTER_TYPES[$this->item_type] : '',
            'sku' => $this->sku,
            'mrp' => $this->model->mrp,
            'group_id' => $this->group_id,
            'group_name' => $this->group->name,
            'gst_rate' => $this->model->gstTax?->id,
            'unit_of_measurement' => $this->model->unit_of_measurement,
            'income_ledger_id' => $this->model->income_ledger_id,
            'expense_ledger_id' => $this->model->expense_ledger_id,
            'gst_cess_rate' => $this->model->gst_cess_rate,
            'sale_price_type' => $this->model->sale_price_type,
            'selling_price_with_gst' => $this->model->selling_price_with_gst,
            'selling_price_without_gst' => $this->model->selling_price_without_gst,
            'closing_stock' => $itemData['closing_balance_qty'],
            'ledger_closing_balance' => $ledgerData['closing_bal'],
            'ledger_closing_type' => $ledgerData['closing_bal_type'],
            'opening_stock' => $openingStock,
            'purchase_price_type' => $this->model->purchase_price_type,
            'purchase_price_with_gst' => $this->model->purchase_price_with_gst,
            'purchase_price_without_gst' => $this->model->purchase_price_without_gst,
            'discount_type' => $this->model->discount_type,
            'discount_value' => $this->model->discount_value,
            'purchase_discount_type' => $this->model->purchase_discount_type,
            'purchase_discount_value' => $this->model->purchase_discount_value,
            'secondary_unit_of_measurement' => $this->model->secondary_unit_of_measurement,
            'conversion_rate' => $this->model->conversion_rate,
        ];
    }

    public function getItemBarcodeAttribute(): string
    {
        /** @var Media $media */
        $media = $this->getMedia(self::BARCODE)->first();

        return ! empty($media) ? $media->getFullUrl() : '';
    }

    public function getItemImageAttribute(): string
    {
        /** @var Media $media */
        $media = $this->getMedia(self::ITEM_IMAGE)->first();

        return ! empty($media) ? $media->getFullUrl() : '';
    }
}
