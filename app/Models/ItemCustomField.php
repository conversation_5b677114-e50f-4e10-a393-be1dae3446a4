<?php

namespace App\Models;

use App\Traits\HasCompany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ItemCustomField extends Model
{
    use HasCompany, HasFactory;

    protected $table = 'item_custom_field';

    protected $fillable = [
        'company_id',
        'label_name',
        'custom_field_type',
        'enable_for_all',
        'open_in_popup',
        'status',
        'show_total',
        'ordering',
        'via_api',
    ];

    protected $casts = [
        'enable_for_all' => 'boolean',
        'open_in_popup' => 'boolean',
        'status' => 'boolean',
        'show_total' => 'boolean',
    ];

    public const MONTH = 1;

    public const YEAR = 2;

    public const CF_TYPE_SERIAL_NO_WITH_ONE_QTY = 1;

    public const CF_TYPE_SERIAL_NO_WITH_MULTI_QTY = 2;

    public const CF_TYPE_BATCH_NUMBER = 3;

    public const CF_TYPE_EXPIRY_DATE = 4;

    public const CF_TYPE_MANUFACTURING_DATE = 5;

    public const CF_TYPE_WARRANTY = 6;

    public const CF_TYPE_TEXT = 7;

    public const CF_TYPE_NUMBER = 8;

    public const CF_TYPE_DATE = 9;

    public const CF_TYPE_DATETIME = 10;

    public const CF_TYPE_DROPDOWN = 11;

    public const CUSTOM_FIELD_ARRAY_WITH_INPUT_TYPE = [
        // self::CF_TYPE_SERIAL_NO_WITH_ONE_QTY => [
        //     'id' => self::CF_TYPE_SERIAL_NO_WITH_ONE_QTY,
        //     'value' => 'Serial Number With One Quantity',
        //     'input_type' => TransactionCustomField::INPUT_TYPE_TEXT,
        //     'open_in_popup' => true,
        // ],
        self::CF_TYPE_SERIAL_NO_WITH_MULTI_QTY => [
            'id' => self::CF_TYPE_SERIAL_NO_WITH_MULTI_QTY,
            'value' => 'Serial Number With Multiple Quantity',
            'input_type' => TransactionCustomField::INPUT_TYPE_TEXT,
            'open_in_popup' => true,
        ],
        // self::CF_TYPE_BATCH_NUMBER => [
        //     'id' => self::CF_TYPE_BATCH_NUMBER,
        //     'value' => 'Batch Number',
        //     'input_type' => TransactionCustomField::INPUT_TYPE_TEXT,
        //     'open_in_popup' => false,
        // ],
        // self::CF_TYPE_EXPIRY_DATE => [
        //     'id' => self::CF_TYPE_EXPIRY_DATE,
        //     'value' => 'Expiry Date',
        //     'input_type' => TransactionCustomField::INPUT_TYPE_DATE,
        //     'open_in_popup' => false,
        // ],
        // self::CF_TYPE_MANUFACTURING_DATE => [
        //     'id' => self::CF_TYPE_MANUFACTURING_DATE,
        //     'value' => 'Manufacturing Date',
        //     'input_type' => TransactionCustomField::INPUT_TYPE_DATE,
        //     'open_in_popup' => false,
        // ],
        self::CF_TYPE_WARRANTY => [
            'id' => self::CF_TYPE_WARRANTY,
            'value' => 'Warranty',
            'input_type' => TransactionCustomField::INPUT_TYPE_TEXT,
            'open_in_popup' => false,
            'field_type' => [
                self::MONTH => 'Month',
                self::YEAR => 'Year',
            ],
        ],
        self::CF_TYPE_TEXT => [
            'id' => self::CF_TYPE_TEXT,
            'value' => 'Text',
            'input_type' => TransactionCustomField::INPUT_TYPE_TEXT,
            'open_in_popup' => false,
        ],
        self::CF_TYPE_NUMBER => [
            'id' => self::CF_TYPE_NUMBER,
            'value' => 'Number',
            'input_type' => TransactionCustomField::INPUT_TYPE_NUMBER,
            'open_in_popup' => false,
        ],
        self::CF_TYPE_DATE => [
            'id' => self::CF_TYPE_DATE,
            'value' => 'Date',
            'input_type' => TransactionCustomField::INPUT_TYPE_DATE,
            'open_in_popup' => false,
        ],
        self::CF_TYPE_DATETIME => [
            'id' => self::CF_TYPE_DATETIME,
            'value' => 'Date Time',
            'input_type' => TransactionCustomField::INPUT_TYPE_DATETIME,
            'open_in_popup' => false,
        ],
        self::CF_TYPE_DROPDOWN => [
            'id' => self::CF_TYPE_DROPDOWN,
            'value' => 'Dropdown',
            'input_type' => TransactionCustomField::INPUT_TYPE_SELECT,
            'open_in_popup' => false,
        ],
    ];

    public const SALE = 1;

    public const SALE_RETURN = 2;

    public const INCOME_DEBIT_NOTE = 3;

    public const INCOME_CREDIT_NOTE = 4;

    public const INCOME_ESTIMATE_QUOTE = 5;

    public const DELIVERY_CHALLAN = 6;

    public const PURCHASE_ORDER = 7;

    public const PURCHASE = 8;

    public const PURCHASE_RETURN = 9;

    public const EXPENSE_DEBIT_NOTE = 10;

    public const EXPENSE_CREDIT_NOTE = 11;

    public const RECURRING = 12;

    public const ALL_TRANSACTION_TYPE = [
        self::SALE => SaleTransactionItem::class,
        self::SALE_RETURN => SaleReturnItemTransaction::class,
        self::INCOME_DEBIT_NOTE => IncomeDebitNoteItemTransaction::class,
        self::INCOME_CREDIT_NOTE => IncomeCreditNoteItemTransaction::class,
        self::INCOME_ESTIMATE_QUOTE => IncomeEstimateQuoteItemInvoice::class,
        self::DELIVERY_CHALLAN => DeliveryChallanTransactionItem::class,
        self::PURCHASE_ORDER => PurchaseOrderItemInvoice::class,
        self::PURCHASE => PurchaseItemTransaction::class,
        self::PURCHASE_RETURN => PurchaseReturnItemTransaction::class,
        self::EXPENSE_DEBIT_NOTE => ExpenseDebitNoteItemTransaction::class,
        self::EXPENSE_CREDIT_NOTE => ExpenseCreditNoteItemTransaction::class,
        self::RECURRING => RecurringInvoiceItem::class,
    ];

    public function options()
    {
        return $this->hasMany(ItemCustomFieldOption::class, 'custom_field_id', 'id');
    }

    public function customFieldDefaultValue()
    {
        return $this->hasOne(ItemCustomFieldDefaultValue::class, 'custom_field_id', 'id')->where('item_id', null);
    }

    public function customFieldDefaultFormula()
    {
        return $this->hasOne(ItemCustomFieldFormula::class, 'custom_field_id', 'id')->where('item_id', null);
    }

    public function customFieldItemSettings()
    {
        return $this->hasMany(ItemCustomFieldSetting::class, 'custom_field_id', 'id');
    }

    public function customFieldTransactionSettings()
    {
        return $this->hasMany(ItemCustomFieldTransactionSetting::class, 'custom_field_id', 'id');
    }
}
