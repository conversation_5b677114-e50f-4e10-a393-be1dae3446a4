<?php

namespace App\Http\Livewire;

use App\Models\Analytic;
use App\Models\ChannelPartner;
use App\Models\Company;
use App\Models\LeadSource;
use App\Models\SalesPerson;
use App\Models\Subscription;
use App\Repositories\CompanyWithoutFranchiseRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Livewire\WithPagination;

class CompanyWithoutFranchise extends CustomLivewireTableComponent
{
    use WithPagination;

    protected $listeners = [
        'refresh' => '$refresh', 'searchTable', 'searchColumn', 'sortColumn', 'companyStatusFilter', 'scheduledDemoFilter', 'subscribeStatusFilter', 'registerFilter', 'dateFilter', 'getEmailFilter',
    ];

    public $paginate = true;

    public $perPage = 50;

    public $columnSearchKeys = [
        'company_name',
        'email',
        'company_count',
        'region_code',
        'phone',
        'register_by',
        'last_used_at',
        'created_at',
        'subscription_status',
        'trial_expire_date',
        'business_category',
        'state',
        'city',
        'is_gst_applicable',
        'is_subscription_page_view',
        'is_buy_now_page_view',
        'transaction_count',
        'total_transaction_count',
    ];

    public $subscribeStatus = '';

    public $companyStatus = '';

    public $scheduledDemo = '';

    public $registerByFilter = '';

    public $startDate = '';

    public $endDate = '';

    public $defaultSorting = 'companies.created_at';

    public $emailFilterValue = 2;

    public $type;

    public function mount()
    {
        $this->setSearchColumns([
            'trade_name', 'companies.created_at', 'companies.is_gst_applicable',
        ]);

        $this->setSearchColumns([
            'user.email', 'user.region_code', 'user.phone', 'user.analytic.registered_via', 'account.last_used_at', 'account.trial_ends_at', 'businessCategory.category_name',
            'billingAddress.state.name', 'billingAddress.city.name', 'user.userLeadSource.salesPerson.name', 'user.userLeadSource.leadSource.name',
            'account.is_subscription_page_view',
            'account.is_buy_now_page_view',
            'user.referral_code',
        ], 'relations');
    }

    public function render(): Factory|View|Application
    {
        // $companyWithoutFranchise = 'company_without_franchise';
        // Cache::forget($companyWithoutFranchise);

        $companyWithoutFranchiseFilterCacheKey = 'company_without_franchise_filter_cache_key';
        Cache::forget($companyWithoutFranchiseFilterCacheKey);

        /* Commented out as per @gopalbhai's request: using the last 3 months as the default date range to improve page load performance */
        // $this->startDate = ! empty($this->startDate) ? $this->startDate : getFinancialYearStartDate();
        // $this->endDate = ! empty($this->endDate) ? $this->endDate : Carbon::today()->format('Y-m-d');
        $this->startDate = ! empty($this->startDate) ? $this->startDate : Carbon::now()->subMonths(2)->startOfMonth()->format('Y-m-d'); // We use subMonths(2) because we want to retrieve data for the last 3 months, including the current month.
        $this->endDate = ! empty($this->endDate) ? $this->endDate : Carbon::now()->endOfMonth()->format('Y-m-d');

        $query = Company::select('companies.*')
            ->with(['billingAddress.state', 'billingAddress.city', 'businessCategory', 'user.analytic', 'user.userLeadSource.salesPerson', 'user.userLeadSource.leadSource', 'user.userLeadSource.channelPartner.user', 'account.subscriptions'])
            ->whereBetween('companies.created_at', [Carbon::parse($this->startDate)->startOfDay(), Carbon::parse($this->endDate)->endOfDay()])
            ->whereNull('franchise_id')
            ->leftJoin('users', 'users.id', '=', 'companies.user_id')
            ->leftJoin('accounts', 'accounts.id', '=', 'companies.account_id')
            ->leftJoin('business_categories', 'business_categories.id', '=', 'companies.business_category_id')
            ->leftJoin('user_lead_sources', 'user_lead_sources.user_id', '=', 'companies.user_id')
            ->leftJoin('sales_person', 'sales_person.id', '=', 'user_lead_sources.sales_person_id')
            ->leftJoin('lead_sources', 'lead_sources.id', '=', 'user_lead_sources.lead_source_id')
            ->leftJoin('channel_partners', 'channel_partners.id', '=', 'user_lead_sources.channel_partner_id')
            ->when(! empty($this->subscribeStatus), function ($q) {
                $q->whereHas('account', function ($query) {
                    if ($this->subscribeStatus == Company::PAID) {
                        $query->whereHas('subscriptions', function ($q) {
                            $q->where('status', Subscription::ACTIVE)
                                ->whereRaw('DATE(end_date) >= ?', [Carbon::now()->toDateString()]);
                        });
                    } elseif ($this->subscribeStatus == Company::SUBSCRIPTION_EXPIRED) {
                        $query->whereDoesntHave('subscriptions', function ($q) {
                            $q->where('status', Subscription::ACTIVE);
                        })->whereHas('subscriptions', function ($q) {
                            $q->where('status', Subscription::INACTIVE)
                                ->whereRaw('DATE(end_date) < ?', [Carbon::now()->toDateString()]);
                        });
                    } elseif ($this->subscribeStatus == Company::TRIAL_NOT_STARTED) {
                        $query->where('is_trial_started', false)->whereNotNull('trial_ends_at');
                    } elseif ($this->subscribeStatus == Company::EXPIRE) {
                        $query->whereDate('trial_ends_at', '<', Carbon::today()->toDateString())->where('is_trial_started', true);
                    } elseif ($this->subscribeStatus == Company::TRIAL_ACTIVE) {
                        $query->where('is_trial_started', true)->whereDate('trial_ends_at', '>=', Carbon::today()->toDateString());
                    }
                });
            })
            ->when($this->companyStatus != '', function ($query) {
                $query->where('companies.status', $this->companyStatus);
            })
            ->when($this->scheduledDemo != '', function ($q) {
                $q->whereHas('user', function ($query) {
                    $query->where('scheduled_demo', $this->scheduledDemo);
                });
            })
            ->when(! empty($this->registerByFilter), function ($q) {
                $q->whereHas('user.analytic', function ($query) {
                    $query->where('registered_via', $this->registerByFilter);
                });
            })
            ->when(! empty($this->searchColumn['company_name']['value']), function ($q) {
                $q->where('companies.trade_name', 'LIKE', '%'.$this->searchColumn['company_name']['value'].'%');
            })
            ->when(! empty($this->searchColumn['email']['value']), function ($q) {
                $q->whereHas('user', function ($query) {
                    $query->where('email', 'LIKE', '%'.$this->searchColumn['email']['value'].'%');
                });
            })
            ->when(! empty($this->searchColumn['referral_code']['value']), function ($q) {
                $q->whereHas('user', function ($query) {
                    $query->where('referral_code', 'LIKE', '%'.$this->searchColumn['referral_code']['value'].'%');
                });
            })
            ->when(! empty($this->searchColumn['region_code']['value']), function ($q) {
                $q->whereHas('user', function ($query) {
                    $query->where('region_code', 'LIKE', '%'.$this->searchColumn['region_code']['value'].'%');
                });
            })
            ->when(! empty($this->searchColumn['phone']['value']), function ($q) {
                $q->whereHas('user', function ($query) {
                    $query->where('phone', 'LIKE', '%'.$this->searchColumn['phone']['value'].'%');
                });
            })
            ->when(! empty($this->searchColumn['register_by']['value']), function ($q) {
                $q->whereHas('user.analytic', function ($query) {
                    if (str_starts_with(strtolower($this->searchColumn['register_by']['value']), 'd')) {
                        $query->where('registered_via', Analytic::DESKTOP_WEB);
                    } elseif (str_starts_with(strtolower($this->searchColumn['register_by']['value']), 'm')) {
                        $query->whereIn('registered_via', [Analytic::MOBILE_APP, Analytic::MOBILE_WEB]);
                    } elseif (str_starts_with(strtolower($this->searchColumn['register_by']['value']), 'mobile')) {
                        $query->whereIn('registered_via', [Analytic::MOBILE_APP, Analytic::MOBILE_WEB]);
                    } elseif (str_starts_with(strtolower($this->searchColumn['register_by']['value']), 'mobile a')) {
                        $query->where('registered_via', Analytic::MOBILE_APP);
                    } elseif (str_starts_with(strtolower($this->searchColumn['register_by']['value']), 'mobile w')) {
                        $query->where('registered_via', Analytic::MOBILE_WEB);
                    } else {
                        $query->whereRaw('1 = 0');
                    }
                });
            })
            ->when(! empty($this->searchColumn['last_used_at']['value']), function ($q) {
                if (! validDate($this->searchColumn['last_used_at']['value'])) {
                    return;
                }
                $dateParts = explode('-', $this->searchColumn['last_used_at']['value']);

                $q->whereHas('account', function ($q) use ($dateParts) {
                    if (! empty($dateParts[0])) {
                        if (strlen($dateParts[0]) <= 2) {
                            $q->whereRaw('DAY(last_used_at) = ?', [$dateParts[0]]);
                        } else {
                            $q->whereRaw('YEAR(last_used_at) = ?', [$dateParts[0]]);
                        }
                    }
                    if (! empty($dateParts[1])) {
                        $q->whereRaw('MONTH(last_used_at) = ?', [$dateParts[1]]);
                    }
                    if (! empty($dateParts[2])) {
                        $q->whereRaw('YEAR(last_used_at) = ?', [$dateParts[2]]);
                    }
                });
            })
            ->when(! empty($this->searchColumn['created_at']['value']), function ($q) {
                if (! validDate($this->searchColumn['created_at']['value'])) {
                    return;
                }
                $dateParts = explode('-', $this->searchColumn['created_at']['value']);
                if (! empty($dateParts[0])) {
                    if (strlen($dateParts[0]) <= 2) {
                        $q->whereRaw('DAY(companies.created_at) = ?', [$dateParts[0]]);
                    } else {
                        $q->whereRaw('YEAR(companies.created_at) = ?', [$dateParts[0]]);
                    }
                }
                if (! empty($dateParts[1])) {
                    $q->whereRaw('MONTH(companies.created_at) = ?', [$dateParts[1]]);
                }
                if (! empty($dateParts[2])) {
                    $q->whereRaw('YEAR(companies.created_at) = ?', [$dateParts[2]]);
                }
            })
            ->when(! empty($this->searchColumn['subscription_status']['value']), function ($q) {
                $q->whereHas('account', function ($query) {
                    if (str_starts_with(strtolower($this->searchColumn['subscription_status']['value']), 'e')) {
                        $query->whereDate('trial_ends_at', '<', Carbon::today()->toDateString());
                    } elseif (str_starts_with(strtolower($this->searchColumn['subscription_status']['value']), 't')) {
                        $query->whereDate('trial_ends_at', '>=', Carbon::today()->toDateString());
                    } elseif (str_starts_with(strtolower($this->searchColumn['subscription_status']['value']), 'p')) {
                        $query->whereHas('subscriptions', function ($q) {
                            $q->where('status', Subscription::ACTIVE)
                                ->whereRaw('DATE(end_date) >= ?', [Carbon::now()->toDateString()]);
                        });
                    }
                });
            })
            ->when(! empty($this->searchColumn['is_subscription_page_view']['value']), function ($q) {
                $q->whereHas('account', function ($query) {
                    $value = strtolower($this->searchColumn['is_subscription_page_view']['value']);
                    if (str_starts_with($value, 'ye')) {
                        $query->where('is_subscription_page_view', true);
                    }

                    if (str_starts_with($value, 'n')) {
                        $query->where('is_subscription_page_view', false);
                    }
                });
            })
            ->when(! empty($this->searchColumn['is_buy_now_page_view']['value']), function ($q) {
                $q->whereHas('account', function ($query) {
                    $value = strtolower($this->searchColumn['is_buy_now_page_view']['value']);
                    if (str_starts_with($value, 'ye')) {
                        $query->where('is_buy_now_page_view', true);
                    }

                    if (str_starts_with($value, 'n')) {
                        $query->where('is_buy_now_page_view', false);
                    }
                });
            })
            ->when(! empty($this->searchColumn['trial_expire_date']['value']), function ($q) {
                $q->whereHas('account', function ($query) {
                    if (! validDate($this->searchColumn['trial_expire_date']['value'])) {
                        return;
                    }
                    $dateParts = explode('-', $this->searchColumn['trial_expire_date']['value']);
                    if (! empty($dateParts[0])) {
                        if (strlen($dateParts[0]) <= 2) {
                            $query->whereRaw('DAY(trial_ends_at) = ?', [$dateParts[0]]);
                        } else {
                            $query->whereRaw('YEAR(trial_ends_at) = ?', [$dateParts[0]]);
                        }
                    }
                    if (! empty($dateParts[1])) {
                        $query->whereRaw('MONTH(trial_ends_at) = ?', [$dateParts[1]]);
                    }
                    if (! empty($dateParts[2])) {
                        $query->whereRaw('YEAR(trial_ends_at) = ?', [$dateParts[2]]);
                    }
                });
            })
            ->when(! empty($this->searchColumn['business_category']['value']), function ($q) {
                $q->whereHas('businessCategory', function ($query) {
                    $query->where('category_name', 'LIKE', '%'.$this->searchColumn['business_category']['value'].'%');
                });
            })
            ->when(! empty($this->searchColumn['state']['value']), function ($q) {
                $q->whereHas('billingAddress.state', function ($query) {
                    $query->where('name', 'LIKE', '%'.$this->searchColumn['state']['value'].'%');
                });
            })
            ->when(! empty($this->searchColumn['city']['value']), function ($q) {
                $q->whereHas('billingAddress.city', function ($query) {
                    $query->where('name', 'LIKE', '%'.$this->searchColumn['city']['value'].'%');
                });
            })
            ->when(! empty($this->searchColumn['is_gst_applicable']['value']), function ($q) {
                $q->whereHas('user', function ($query) {
                    if (str_starts_with(strtolower($this->searchColumn['is_gst_applicable']['value']), 'y')) {
                        $query->where('is_gst_applicable', true);
                    } elseif (str_starts_with(strtolower($this->searchColumn['is_gst_applicable']['value']), 'n')) {
                        $query->where('is_gst_applicable', false);
                    }
                });
            })
            ->when(! empty($this->searchColumn['sales_person']['value']), function ($q) {
                $q->whereHas('user.userLeadSource.salesPerson', function ($query) {
                    $query->where('name', 'LIKE', '%'.$this->searchColumn['sales_person']['value'].'%');
                });
            })
            ->when(! empty($this->searchColumn['lead_source']['value']), function ($q) {
                $q->whereHas('user.userLeadSource.leadSource', function ($query) {
                    $query->where('name', 'LIKE', '%'.$this->searchColumn['lead_source']['value'].'%');
                });
            })
            ->when(! empty($this->searchColumn['transaction_count']['value']), function ($q) {
                $q->havingRaw('
                    CAST((
                        (SELECT COUNT(*) FROM sales_transactions WHERE sales_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM sale_return_transactions WHERE sale_return_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM income_credit_note_transactions WHERE income_credit_note_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM income_debit_note_transactions WHERE income_debit_note_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM income_estimate_quote_transactions WHERE income_estimate_quote_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM delivery_challan_transactions WHERE delivery_challan_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM purchase_transactions WHERE purchase_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM purchase_return_transactions WHERE purchase_return_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM expense_credit_note_transactions WHERE expense_credit_note_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM expense_debit_note_transactions WHERE expense_debit_note_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM purchase_order_transactions WHERE purchase_order_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM receipt_transactions WHERE receipt_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM payment_transactions WHERE payment_transactions.company_id = companies.id and deleted_at is null) +
                        (SELECT COUNT(*) FROM journal_transactions WHERE journal_transactions.company_id = companies.id and deleted_at is null)
                    ) AS CHAR) LIKE ?', [$this->searchColumn['transaction_count']['value'].'%']);
            })
            ->when(! empty($this->searchColumn['total_transaction_count']['value']), function ($q) {
                $q->havingRaw('(
                    (SELECT COUNT(*) FROM sales_transactions WHERE sales_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND sales_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM sale_return_transactions WHERE sale_return_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND sale_return_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM income_credit_note_transactions WHERE income_credit_note_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND income_credit_note_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM income_debit_note_transactions WHERE income_debit_note_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND income_debit_note_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM income_estimate_quote_transactions WHERE income_estimate_quote_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND income_estimate_quote_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM delivery_challan_transactions WHERE delivery_challan_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND delivery_challan_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM purchase_transactions WHERE purchase_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND purchase_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM purchase_return_transactions WHERE purchase_return_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND purchase_return_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM expense_credit_note_transactions WHERE expense_credit_note_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND expense_credit_note_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM expense_debit_note_transactions WHERE expense_debit_note_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND expense_debit_note_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM purchase_order_transactions WHERE purchase_order_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id)) +
                    (SELECT COUNT(*) FROM receipt_transactions WHERE receipt_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND receipt_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM payment_transactions WHERE payment_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND payment_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM journal_transactions WHERE journal_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND journal_transactions.deleted_at IS NULL)
                ) >= ?', [$this->searchColumn['total_transaction_count']['value']]);
            })
            ->when(! empty($this->searchColumn['company_count']['value']), function ($q) {
                $q->havingRaw('
                    CAST((SELECT COUNT(*) FROM companies AS c WHERE c.account_id = accounts.id) AS CHAR) LIKE ?', [$this->searchColumn['company_count']['value'].'%']);
            })
            ->addSelect(DB::raw('(
                SELECT
                    (SELECT COUNT(*) FROM sales_transactions WHERE sales_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND sales_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM sale_return_transactions WHERE sale_return_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND sale_return_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM income_credit_note_transactions WHERE income_credit_note_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND income_credit_note_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM income_debit_note_transactions WHERE income_debit_note_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND income_debit_note_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM income_estimate_quote_transactions WHERE income_estimate_quote_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND income_estimate_quote_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM delivery_challan_transactions WHERE delivery_challan_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND delivery_challan_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM purchase_transactions WHERE purchase_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND purchase_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM purchase_return_transactions WHERE purchase_return_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND purchase_return_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM expense_credit_note_transactions WHERE expense_credit_note_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND expense_credit_note_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM expense_debit_note_transactions WHERE expense_debit_note_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND expense_debit_note_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM purchase_order_transactions WHERE purchase_order_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id)) +
                    (SELECT COUNT(*) FROM receipt_transactions WHERE receipt_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND receipt_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM payment_transactions WHERE payment_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND payment_transactions.deleted_at IS NULL) +
                    (SELECT COUNT(*) FROM journal_transactions WHERE journal_transactions.company_id IN
                        (SELECT c.id FROM companies c WHERE c.account_id = companies.account_id) AND journal_transactions.deleted_at IS NULL)
                    ) AS total_transaction_count
            '))
            ->addSelect(DB::raw('(
                (SELECT COUNT(*) FROM sales_transactions WHERE sales_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM sale_return_transactions WHERE sale_return_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM income_credit_note_transactions WHERE income_credit_note_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM income_debit_note_transactions WHERE income_debit_note_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM income_estimate_quote_transactions WHERE income_estimate_quote_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM delivery_challan_transactions WHERE delivery_challan_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM purchase_transactions WHERE purchase_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM purchase_return_transactions WHERE purchase_return_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM expense_credit_note_transactions WHERE expense_credit_note_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM expense_debit_note_transactions WHERE expense_debit_note_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM purchase_order_transactions WHERE purchase_order_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM receipt_transactions WHERE receipt_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM payment_transactions WHERE payment_transactions.company_id = companies.id and deleted_at is null) +
                (SELECT COUNT(*) FROM journal_transactions WHERE journal_transactions.company_id = companies.id and deleted_at is null)
                ) as transaction_count
            '))
            ->addSelect(DB::raw('(SELECT COUNT(*) FROM companies AS c WHERE c.account_id = accounts.id AND c.deleted_at IS NULL) as company_count'));

        if ($this->type == 'user-on-boarded') {
            $query = $query->whereHas('user', function ($q) {
                $q->whereNotNull('referral_code')->where('referral_code', '!=', '');
            });
        }

        if ($this->type == 'user-on-trial') {
            $query = $query->whereHas('user', function ($q) {
                $q->whereNotNull('referral_code')->where('referral_code', '!=', '');
            })->whereHas('account', function ($q) {
                $q->where('is_trial_started', true)->whereNotNull('trial_ends_at');
            });
        }

        $query = $this->applySearch($query);
        $query = $this->applySortingIntoQuery($query);

        if ($this->emailFilterValue == 2) {
            $paginatedItem = $query->where('is_primary_company', true)->paginate($this->perPage);
            $data = $paginatedItem;
        } else {
            $paginatedItem = $query->paginate($this->perPage);
            $data = $paginatedItem;
        }

        /** @var CompanyWithoutFranchiseRepository $CompanyWithoutFranchiseRepo */
        $CompanyWithoutFranchiseRepo = App::make(CompanyWithoutFranchiseRepository::class);
        if ($this->emailFilterValue == 2) {
            $data = $CompanyWithoutFranchiseRepo->prepareDataForEmailWiseTable($data);
        } else {
            $data = $CompanyWithoutFranchiseRepo->prepareDataForTable($data);
        }
        // $data = $this->prepareAllDataWithSearchingAndSorting($data);

        $rows['data'] = $data;
        $rows['salesPersons'] = SalesPerson::pluck('name', 'id');
        $rows['leadSources'] = LeadSource::pluck('name', 'id');
        $rows['channelPartners'] = ChannelPartner::with('user')->whereHas('user', function ($query) {
            $query->where('status', true);
        })->get()->pluck('user.full_name', 'id')->toArray();
        $rows['subscription_type'] = $this->subscribeStatus;
        $rows['company_status'] = $this->companyStatus;
        $rows['register_by'] = $this->registerByFilter;
        $rows['email_filter_value'] = $this->emailFilterValue;
        $rows['startDate'] = $this->startDate;
        $rows['endDate'] = $this->endDate;
        $rows['fields'] = $this->getFields();
        $rows['scheduled_demo'] = $this->scheduledDemo;

        $filters['data'] = [
            'emailFilterValue' => $this->emailFilterValue,
            'subscribeStatus' => $this->subscribeStatus,
            'registerByFilter' => $this->registerByFilter,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'type' => $this->type,
        ];

        Cache::put($companyWithoutFranchiseFilterCacheKey, $filters);
        // Cache::put($companyWithoutFranchise, $rows);

        return view('livewire.company-without-franchise-table', compact('rows', 'paginatedItem'));
    }

    public function subscribeStatusFilter($value): void
    {
        $this->resetPage();
        $this->subscribeStatus = $value;
    }

    public function companyStatusFilter($value): void
    {
        $this->resetPage();
        $this->companyStatus = $value;
    }

    public function scheduledDemoFilter($value): void
    {
        $this->resetPage();
        $this->scheduledDemo = $value;
    }

    public function registerFilter($value): void
    {
        $this->resetPage();
        $this->registerByFilter = $value;
    }

    public function dateFilter($date): void
    {
        $this->resetPage();
        $date = explode(' - ', $date);
        $this->startDate = $date[0];
        $this->endDate = $date[1];
    }

    public function getEmailFilter($value)
    {
        $this->emailFilterValue = $value;
    }

    public function getFields(): array
    {
        return [
            [
                'name' => 'Company Name',
                'column_name' => 'company_name',
                'is_sortable' => true,
                'sort' => 'trade_name',
                'is_sorting' => array_key_exists('company_name', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['company_name'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Email',
                'column_name' => 'email',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('email', $this->sortColumn),
                'sort' => 'users.email',
                'sorting_direction' => $this->sortColumn['email'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Referral Code',
                'column_name' => 'referral_code',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('referral_code', $this->sortColumn),
                'sort' => 'users.referral_code',
                'sorting_direction' => $this->sortColumn['referral_code'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Country Code',
                'column_name' => 'region_code',
                'is_sortable' => true,
                'sort' => 'companies.region_code',
                'is_sorting' => array_key_exists('region_code', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['region_code'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Phone',
                'column_name' => 'phone',
                'is_sortable' => false,
                'is_sorting' => array_key_exists('phone', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['phone'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Company Count',
                'column_name' => 'company_count',
                'is_sortable' => true,
                'sort' => 'company_count',
                'is_sorting' => array_key_exists('company_count', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['company_count'] ?? '',
                'is_searchable' => true,
                'is_show' => $this->emailFilterValue == 2 ? true : false,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Transaction Count',
                'column_name' => 'transaction_count',
                'is_sortable' => true,
                'sort' => 'transaction_count',
                'is_sorting' => array_key_exists('transaction_count', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['transaction_count'] ?? '',
                'is_searchable' => true,
                'is_show' => $this->emailFilterValue == 1 ? true : false,
                'show_total' => false,
                'is_number' => true,
            ],
            [
                'name' => 'Transaction Count',
                'column_name' => 'total_transaction_count',
                'is_sortable' => true,
                'sort' => 'total_transaction_count',
                'is_sorting' => array_key_exists('total_transaction_count', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['total_transaction_count'] ?? '',
                'is_searchable' => true,
                'is_show' => $this->emailFilterValue == 2 ? true : false,
                'show_total' => false,
                'is_number' => true,
            ],
            [
                'name' => 'Register By',
                'column_name' => 'register_by',
                'is_sortable' => false,
                'is_sorting' => array_key_exists('register_by', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['register_by'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Last Used At',
                'column_name' => 'last_used_at',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('last_used_at', $this->sortColumn),
                'sort' => 'accounts.last_used_at',
                'sorting_direction' => $this->sortColumn['last_used_at'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Created At',
                'column_name' => 'created_at',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('created_at', $this->sortColumn),
                'sort' => 'companies.created_at',
                'sorting_direction' => $this->sortColumn['created_at'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Subscription Status',
                'column_name' => 'subscription_status',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('subscription_status', $this->sortColumn),
                'sort' => 'accounts.trial_ends_at',
                'sorting_direction' => $this->sortColumn['subscription_status'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Trial Expiry Date',
                'column_name' => 'trial_expire_date',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('trial_expire_date', $this->sortColumn),
                'sort' => 'accounts.trial_ends_at',
                'sorting_direction' => $this->sortColumn['trial_expire_date'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Verified',
                'column_name' => 'is_verified',
                'is_sortable' => true,
                'sort' => 'users.is_verified',
                'is_sorting' => array_key_exists('is_verified', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['is_verified'] ?? '',
                'is_searchable' => false,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Sign up With Google',
                'column_name' => 'sign_in_with_google',
                'is_sortable' => true,
                'sort' => 'users.sign_in_with_google',
                'is_sorting' => array_key_exists('sign_in_with_google', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['sign_in_with_google'] ?? '',
                'is_searchable' => false,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Business Category',
                'column_name' => 'business_category',
                'is_sortable' => true,
                'sort' => 'business_categories.category_name',
                'is_sorting' => array_key_exists('business_category', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['business_category'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'State',
                'column_name' => 'state',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('state', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['state'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'City',
                'column_name' => 'city',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('city', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['city'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Subscription Page View',
                'column_name' => 'is_subscription_page_view',
                'is_sortable' => true,
                'sort' => 'accounts.is_subscription_page_view',
                'is_sorting' => array_key_exists('is_subscription_page_view', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['is_subscription_page_view'] ?? '',
                'is_searchable' => true,
                'is_show' => $this->emailFilterValue == 2 ? true : false,
                'show_total' => false,
                'is_number' => true,
            ],
            [
                'name' => 'Buy Now Page View',
                'column_name' => 'is_buy_now_page_view',
                'is_sortable' => true,
                'sort' => 'accounts.is_buy_now_page_view',
                'is_sorting' => array_key_exists('is_buy_now_page_view', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['is_buy_now_page_view'] ?? '',
                'is_searchable' => true,
                'is_show' => $this->emailFilterValue == 2 ? true : false,
                'show_total' => false,
                'is_number' => true,
            ],
            [
                'name' => 'Status',
                'column_name' => 'status',
                'is_sortable' => false,
                'is_sorting' => array_key_exists('status', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['status'] ?? '',
                'is_searchable' => false,
                'is_show' => $this->emailFilterValue == 2 ? true : false,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Is GST Applicable',
                'column_name' => 'is_gst_applicable',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('is_gst_applicable', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['is_gst_applicable'] ?? '',
                'is_searchable' => true,
                'is_show' => $this->emailFilterValue == 2 ? false : true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Sales Person',
                'column_name' => 'sales_person',
                'is_sortable' => true,
                'sort' => 'sales_person.name',
                'is_sorting' => array_key_exists('sales_person', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['sales_person'] ?? '',
                'is_searchable' => false,
                'is_show' => $this->emailFilterValue == 2 ? true : false,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Lead Source',
                'column_name' => 'lead_source',
                'is_sortable' => true,
                'sort' => 'lead_sources.name',
                'is_sorting' => array_key_exists('lead_source', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['lead_source'] ?? '',
                'is_searchable' => false,
                'is_show' => $this->emailFilterValue == 2 ? true : false,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Demo Type',
                'column_name' => 'demo_type',
                'is_sortable' => true,
                'sort' => 'user_lead_sources.demo_type',
                'is_sorting' => array_key_exists('demo_type', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['demo_type'] ?? '',
                'is_searchable' => false,
                'is_show' => $this->emailFilterValue == 2 ? true : false,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Channel Partner',
                'column_name' => 'channel_partner',
                'is_sortable' => true,
                'sort' => 'users.first_name',
                'is_sorting' => array_key_exists('channel_partner', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['channel_partner'] ?? '',
                'is_searchable' => false,
                'is_show' => $this->emailFilterValue == 2 ? true : false,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Action',
                'column_name' => 'action',
                'is_sortable' => false,
                'is_sorting' => array_key_exists('action', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['action'] ?? '',
                'is_searchable' => false,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
        ];
    }
}
