<?php

namespace App\Http\Controllers\API\v2;

use App\Actions\CommonAction\GetAdditionalChargesLedgerListAction;
use App\Actions\CommonAction\GetAddLessLedgerListAction;
use App\Actions\CommonAction\GetItemDetailsAction;
use App\Actions\CommonAction\GetPartyDetailsAction;
use App\Actions\CommonAction\GetTcsLedgerListAction;
use App\Actions\CommonAction\GetTcsTdsDetailsAction;
use App\Actions\CommonAction\GetTdsLedgerListAction;
use App\Actions\CommonActionMobile\GetPaymentModeListAction;
use App\Actions\PriceList\PriceListItemPriceApiActions;
use App\Actions\v1\CommonAction\GetPdfPreviewAction;
use App\Http\Controllers\API\v1\AppBaseAPIController;
use App\Models\CompanyGroup;
use App\Models\CompanyTile;
use App\Models\EwayBill;
use App\Models\Ledger;
use App\Models\Master\Customer;
use App\Models\Master\ItemMaster;
use App\Models\Master\PriceListGroups;
use App\Models\SaleTransaction;
use App\Repositories\EWayBillRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class CommonAPIController extends AppBaseAPIController
{
    public function paymentModeList($type)
    {
        $response = GetPaymentModeListAction::run($type);

        return $this->sendResponse($response, 'Payment Mode List Fetched Successfully.');
    }

    public function additionalChargesLedgerList(Request $request)
    {
        $input = $request->all();

        $response = GetAdditionalChargesLedgerListAction::run($input);

        return $this->sendResponse($response, 'Additional Charges Ledger List Fetched Successfully.');
    }

    public function addLessLedgerList(Request $request)
    {
        $input = $request->all();

        $response = GetAddLessLedgerListAction::run($input);

        return $this->sendResponse($response, 'AddLess Ledger List Fetched Successfully.');
    }

    public function partyDetails($partyId)
    {
        $response = GetPartyDetailsAction::run($partyId);

        return $this->sendResponse($response, 'Party Details Fetched Successfully.');
    }

    public function tcsRateList($type)
    {
        $response = GetTcsLedgerListAction::run($type);

        return $this->sendResponse($response, 'Tcs Rate List Fetched Successfully.');
    }

    public function tdsRateList($type)
    {
        $response = GetTdsLedgerListAction::run($type);

        return $this->sendResponse($response, 'Tds Rate List Fetched Successfully.');
    }

    public function tdsTcsDetails($ledgerId, $partyId = null)
    {
        $response = GetTcsTdsDetailsAction::run($ledgerId, $partyId);

        return $this->sendResponse($response, 'Tcs Tds Details Fetched Successfully.');
    }

    public function priceListItemPrice(Request $request)
    {
        $input = $request->all();
        $customerId = $input['customer_id'] ?? null;
        $itemIds = $input['item_ids'] ?? [];
        $search = $input['search'] ?? null;
        $skip = $input['skip'] ?? 0;
        $status = $input['status'] ?? null;
        $transactionType = $input['transaction_type'] ?? null;

        $query = ItemMaster::with(['model.gstTax', 'model.gstGstCessRate', 'model.unitOfMeasurement'])->whereCompanyId(getCurrentCompany()->id);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->whereRaw('lower(item_name) LIKE ?', ['%'.strtolower($search).'%'])->orWhere('sku', 'like', '%'.$search.'%');
            });
        }

        if (! empty($itemIds)) {
            $query->orderByRaw(
                'CASE WHEN id IN ('.implode(',', array_map('intval', $itemIds)).') THEN 0 ELSE 1 END'
            );
        }

        if (! is_null($status) && in_array($status, [0, 1])) {
            $query->where('status', $status);
        }

        $items = $query->orderBy('id', 'DESC')
            ->limit(count($itemIds) + 50)
            ->offset($skip)
            ->get();
        $customerLedger = Ledger::with('model')->whereId($customerId)->first();
        $collection = collect();
        if (! empty($customerLedger)) {
            $customer = PriceListGroups::with('priceList.items')
                ->whereHas('priceList', function ($q) {
                    $q->where('status', 1);
                })
                ->where('model_type', Customer::class)
                ->where('model_id', $customerLedger->id)
                ->get();

            $customerGroup = PriceListGroups::with('priceList.items')
                ->where('model_type', CompanyGroup::class)
                ->whereHas('priceList', function ($q) {
                    $q->where('status', 1);
                })
                ->where('model_id', $customerLedger->group_id)
                ->get();

            foreach ($customerGroup as $item) {
                $collection = $collection->merge($item->priceList->items);
            }
            foreach ($customer as $item) {
                $collection = $collection->merge($item->priceList->items);
            }
        }

        $data = [];
        // TO_COMMENT =>
        $data['modalShow'] = false;
        $data['modal_show'] = false;
        foreach ($items as $item) {
            $response = GetItemDetailsAction::run($item, $transactionType);
            $checkPriceList = $collection->where('item_id', $item->id)->first();
            PriceListItemPriceApiActions::run($response, $customerLedger);

            if ($checkPriceList) {
                // TO_COMMENT =>
                $response['itemMaster']->model->selling_price_with_gst = $checkPriceList->selling_price_with_gst;
                // TO_COMMENT =>
                $response['itemMaster']->model->selling_price_without_gst = $checkPriceList->selling_price_without_gst;
                // TO_COMMENT =>
                $response['itemMaster']->model->mrp = $checkPriceList->mrp;
                // TO_COMMENT =>
                $response['itemMaster']->model->discount_value = $checkPriceList->discount;
                // TO_COMMENT =>
                $response['itemMaster']->model->discount_type = $checkPriceList->discount != null ? SaleTransaction::DISCOUNT_TYPE_PERCENTAGE : null;
                // TO_COMMENT =>
                $response['itemMaster']->model->change_price_list = true;
                // TO_COMMENT =>
                $data['modalShow'] = true;

                $response['item_master']->model->selling_price_with_gst = $checkPriceList->selling_price_with_gst;
                $response['item_master']->model->selling_price_without_gst = $checkPriceList->selling_price_without_gst;
                $response['item_master']->model->mrp = $checkPriceList->mrp;
                $response['item_master']->model->discount_value = $checkPriceList->discount;
                $response['item_master']->model->discount_type = $checkPriceList->discount != null ? SaleTransaction::DISCOUNT_TYPE_PERCENTAGE : null;
                $response['item_master']->model->change_price_list = true;
                $data['modal_show'] = true;
            }
            // TO_COMMENT =>
            $response['itemMaster']->model->method_of_stock_valuation = (int) $response['itemMaster']->model->method_of_stock_valuation;
            $response['item_master']->model->method_of_stock_valuation = (int) $response['item_master']->model->method_of_stock_valuation;
            $data['items'][] = $response;
        }

        return $this->sendResponse($data, 'Item Details Fetched Successfully.');
    }

    public function lockTransactionDate()
    {
        $getTransactionsLockDate = getTransactionsLockDate();
        $keys = ['income', 'expense', 'payment', 'receipt'];

        $response = array_map(function ($key) use ($getTransactionsLockDate) {
            return [
                'key' => $key,
                'value' => isset($getTransactionsLockDate[$key]) ? Carbon::parse($getTransactionsLockDate[$key])->format('Y-m-d') : null,
            ];
        }, $keys);

        return $this->sendResponse($response, 'Lock Transaction Date Fetched Successfully.');
    }

    public function eWayDetails($id)
    {
        $company = getCurrentCompany();
        $eWayBill = EwayBill::with(['purchaseReturnTransaction.supplier', 'saleTransaction.customer'])
            ->where('id', $id)
            ->where('company_id', $company->id)
            ->first();

        if (empty($eWayBill)) {
            return $this->sendError('Eway Bill Not Found.');
        }
        $data = collect([$eWayBill]);

        /** @var EWayBillRepository $eWayBillRepository */
        $eWayBillRepository = App::make(EWayBillRepository::class);
        $response = $eWayBillRepository->prepareDataForTable($data);

        return $this->sendResponse($response, 'Eway Details Fetched Successfully.');
    }

    public function transactionPdfPreview($transactionType, $transactionId)
    {
        $response = GetPdfPreviewAction::run($transactionId, $transactionType);

        return $this->sendResponse($response, 'PDF Preview Fetched Successfully.');
    }

    public function favoriteReports()
    {
        $companyTile = CompanyTile::firstOrFail();
        $tileMenu = collect(json_decode($companyTile->meta_data, true));

        $favoriteReports = $tileMenu['favorite_reports'] ?? [];

        $response = collect($favoriteReports)->map(function ($report) {
            return [
                'menu'  => (int)$report['menu'],
                'order' => (int)$report['order'],
                'name'  => (string)CompanyTile::REPORT_NAME[$report['menu']] ?? '',
            ];
        })->values();

        return $this->sendResponse($response, 'Favorite Reports fetched successfully.');
    }

    public function markAsFavorite(Request $request)
    {
        $input = $request->all();
        $reportId = $input['report_id'] ?? null;

        if(empty($reportId)) {
            return $this->sendError('Report Id is required.');
        }

        $companyTile = CompanyTile::firstOrFail();
        $tileMenu = collect(json_decode($companyTile->meta_data, true)); // decode as array

        $favoriteReportArray = $tileMenu['favorite_reports'] ?? [];

        $exists = collect($favoriteReportArray)
            ->pluck('menu')
            ->contains($reportId);

        if ($exists) {
            $favoriteReportArray = collect($favoriteReportArray)
                ->reject(fn ($item) => $item['menu'] == $reportId)
                ->values()
                ->toArray();

            $message = 'Report Removed from Favorite Successfully';
        } else {
            $maxOrder = collect($favoriteReportArray)
                ->pluck('order')
                ->filter(fn ($order) => is_numeric($order))
                ->max() ?? 0;

            $favoriteReportArray[] = [
                'menu' => $reportId,
                'order' => $maxOrder + 1,
            ];

            $message = 'Report Added to Favorite Successfully';
        }

        $tileMenu['favorite_reports'] = $favoriteReportArray;
        $companyTile->meta_data = json_encode($tileMenu);
        $companyTile->save();

        return $this->sendSuccess($message);
    }
}
