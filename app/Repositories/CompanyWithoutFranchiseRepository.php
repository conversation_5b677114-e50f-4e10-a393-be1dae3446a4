<?php

namespace App\Repositories;

use App\Actions\CurrentlyActiveUsers;
use App\Models\Analytic;
use App\Models\City;
use App\Models\Company;
use App\Models\DeliveryChallanTransaction;
use App\Models\ExpenseCreditNoteTransaction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\IncomeEstimateQuoteTransaction;
use App\Models\JournalTransaction;
use App\Models\PaymentTransaction;
use App\Models\PurchaseOrderTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use App\Models\ReceiptTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SaleTransaction;
use App\Models\State;
use App\Models\Subscription;
use Carbon\Carbon;

/**
 * Class CompanyWithoutFranchiseRepository
 */
class CompanyWithoutFranchiseRepository extends BaseRepository
{
    /**
     * @var array
     */
    public $fieldSearchable = [
        'trade_name',
    ];

    /**
     * Return searchable fields
     */
    public function getFieldsSearchable(): array
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Company::class;
    }

    public function prepareDataForTable($rows): array
    {
        $data = [];
        $states = State::tobase()->pluck('name', 'id')->toArray();
        $cities = City::tobase()->pluck('name', 'id')->toArray();

        foreach ($rows as $row) {
            $lastUpdatedAt = $row->account->last_used_at;
            $lastUpdatedAt = ! empty($lastUpdatedAt) ? $lastUpdatedAt : null;
            $status = '';
            if (empty($row->account->trial_ends_at)) {
                $activeSubscription = $row->account->subscriptions()
                    ->where('status', Subscription::ACTIVE)
                    ->where('end_date', '>', Carbon::today()->toDateTimeString())
                    ->first();
                if ($activeSubscription) {
                    $status = Company::TRIAL_PERIOD_STATUS[Company::PAID];
                } else {
                    $status = Company::TRIAL_PERIOD_STATUS[Company::SUBSCRIPTION_EXPIRED];
                }
            } elseif (! $row->account->is_trial_started) {
                $status = Company::TRIAL_PERIOD_STATUS[Company::TRIAL_NOT_STARTED];
            } elseif (Carbon::parse($row->account->trial_ends_at) < Carbon::today()) {
                $status = Company::TRIAL_PERIOD_STATUS[Company::EXPIRE];
            } elseif (Carbon::parse($row->account->trial_ends_at) > Carbon::today()) {
                $status = Company::TRIAL_PERIOD_STATUS[Company::TRIAL_ACTIVE];
            }

            // $transactionCount = $this->getTransactionCount($row['id']);
            $userAnalyticData = getAnalyticData($row->user->id);
            $registerBy = ! empty($userAnalyticData) ? $userAnalyticData['registered_via'] : '';
            $registerBy = ! empty($registerBy) ? Analytic::REGISTERED_BY[$registerBy] : '';
            $data[] = [
                'id' => $row['id'],
                'account_id' => $row->account->id,
                'company_name' => $row->trade_name ?? '',
                'email' => $row->user->email ?? '',
                'region_code' => $row->user->region_code ?? '',
                'phone' => $row->user->phone ?? '',
                'transaction_count' => $row->transaction_count ?? 0,
                'register_by' => $registerBy,
                'business_category' => ! empty($row->businessCategory) ? $row->businessCategory->category_name : '',
                'state' => ! empty($row->billingAddress) && ! empty($row->billingAddress->state_id) ? $states[$row->billingAddress->state_id] : '',
                'city' => ! empty($row->billingAddress) && ! empty($row->billingAddress->city_id) ? $cities[$row->billingAddress->city_id] : '',
                'is_gst_applicable' => $row->is_gst_applicable ? 'Yes' : 'No',
                'is_verified' => $row->user->is_verified,
                'sign_in_with_google' => $row->user->sign_in_with_google,
                'last_used_at' => ! empty($lastUpdatedAt) ? Carbon::parse($lastUpdatedAt)->format('d-m-Y h:i A') : '',
                'created_at' => Carbon::parse($row->created_at)->format('d-m-Y h:i A') ?? '',
                'subscription_status' => $status,
                'trial_expire_date' => $status != Company::TRIAL_PERIOD_STATUS[Company::TRIAL_NOT_STARTED] && ! empty($row->account->trial_ends_at) ? Carbon::parse($row->account->trial_ends_at)->format('d-m-Y h:i A') : '',
                'referral_code' => $row->user->referral_code ?? null,
            ];
        }

        return $data;
    }

    public function prepareDataForEmailWiseTable($rows, $export = false): array
    {
        $data = [];
        $states = State::tobase()->pluck('name', 'id')->toArray();
        $cities = City::tobase()->pluck('name', 'id')->toArray();
        $userStatues = CurrentlyActiveUsers::run();
        // $onlineUsers = array_filter($userStatues, function ($user) {
        //     return $user['is_current_device'];
        // });
        $onlineUsers = array_column($userStatues, 'user_id');

        foreach ($rows as $company) {
            $row = $company;
            $lastUpdatedAt = $company->account->last_used_at;
            $lastUpdatedAt = ! empty($lastUpdatedAt) ? $lastUpdatedAt : null;

            $status = '';
            if (empty($row->account->trial_ends_at)) {
                $activeSubscription = $row->account->subscriptions()
                    ->where('status', Subscription::ACTIVE)
                    ->where('end_date', '>', Carbon::today()->toDateTimeString())
                    ->first();
                if ($activeSubscription) {
                    $status = Company::TRIAL_PERIOD_STATUS[Company::PAID];
                } else {
                    $status = Company::TRIAL_PERIOD_STATUS[Company::SUBSCRIPTION_EXPIRED];
                }
            } elseif (! $row->account->is_trial_started) {
                $status = Company::TRIAL_PERIOD_STATUS[Company::TRIAL_NOT_STARTED];
            } elseif (Carbon::parse($row->account->trial_ends_at) < Carbon::today()) {
                $status = Company::TRIAL_PERIOD_STATUS[Company::EXPIRE];
            } elseif (Carbon::parse($row->account->trial_ends_at) > Carbon::today()) {
                $status = Company::TRIAL_PERIOD_STATUS[Company::TRIAL_ACTIVE];
            }

            $analyticData = getAnalyticData($row->user->id);
            $registerBy = ! empty($analyticData) ? $analyticData['registered_via'] : '';
            $registerBy = ! empty($registerBy) ? Analytic::REGISTERED_BY[$registerBy] : '';

            $data[] = [
                'id' => $row['id'],
                'account_id' => $row->account->id,
                'company_name' => $row->trade_name ?? '',
                'email' => $row->user->email ?? '',
                'region_code' => $row->user->region_code ?? '',
                'phone' => $row->user->phone ?? '',
                'register_by' => $registerBy,
                'business_category' => ! empty($row->businessCategory) ? $row->businessCategory->category_name : '',
                'state' => ! empty($row->billingAddress) && ! empty($row->billingAddress->state_id) ? $states[$row->billingAddress->state_id] : '',
                'city' => ! empty($row->billingAddress) && ! empty($row->billingAddress->city_id) ? $cities[$row->billingAddress->city_id] : '',
                'is_verified' => $row->user->is_verified,
                'sign_in_with_google' => $row->user->sign_in_with_google,
                'last_used_at' => ! empty($lastUpdatedAt) ? Carbon::parse($lastUpdatedAt)->format('d-m-Y h:i A') : '',
                'created_at' => Carbon::parse($row->created_at)->format('d-m-Y h:i A') ?? '',
                'subscription_status' => $status,
                'trial_expire_date' => $status != Company::TRIAL_PERIOD_STATUS[Company::TRIAL_NOT_STARTED] && ! empty($row->account->trial_ends_at) ? Carbon::parse($row->account->trial_ends_at)->format('d-m-Y h:i A') : '',
                'company_count' => $row->company_count ?? 0,
                'total_transaction_count' => $row->total_transaction_count ?? 0,
                'sales_person' => isset($row->user->userLeadSource) && ! empty($row->user->userLeadSource->salesPerson) ? ($export ? $row->user->userLeadSource->salesPerson->name : $row->user->userLeadSource->salesPerson->id) : '',
                'lead_source' => isset($row->user->userLeadSource) && ! empty($row->user->userLeadSource->leadSource) ? ($export ? $row->user->userLeadSource->leadSource->name : $row->user->userLeadSource->leadSource->id) : '',
                'demo_type' => isset($row->user->userLeadSource) && ! is_null($row->user->userLeadSource->demo_type) ?
                ($row->user->userLeadSource->demo_type == Company::OFFLINE ? Company::OFFLINE_STR :
                    ($row->user->userLeadSource->demo_type == Company::ONLINE ? Company::ONLINE_STR : '')) : '',
                'is_subscription_page_view' => $row->account->is_subscription_page_view ? ($export ? 'Yes' : true) : ($export ? 'No' : false),
                'is_buy_now_page_view' => $row->account->is_buy_now_page_view ? ($export ? 'Yes' : true) : ($export ? 'No' : false),
                'status' => in_array($row->user->id, $onlineUsers) ? 'Online' : 'Offline',
                'channel_partner' => isset($row->user->userLeadSource) && ! empty($row->user->userLeadSource->channelPartner) ? ($export ? $row->user->userLeadSource->channelPartner->user->full_name : $row->user->userLeadSource->channelPartner->id) : '',
                'referral_code' => $row->user->referral_code ?? null,
            ];
        }

        return $data;
    }

    public function getTransactionCount($companyId)
    {
        $transactionCount = 0;
        $transactionCount += SaleTransaction::whereCompanyId($companyId)->count();
        $transactionCount += SaleReturnTransaction::whereCompanyId($companyId)->count();
        $transactionCount += IncomeCreditNoteTransaction::whereCompanyId($companyId)->count();
        $transactionCount += IncomeDebitNoteTransaction::whereCompanyId($companyId)->count();
        $transactionCount += IncomeEstimateQuoteTransaction::whereCompanyId($companyId)->count();
        $transactionCount += DeliveryChallanTransaction::whereCompanyId($companyId)->count();

        $transactionCount += PurchaseTransaction::whereCompanyId($companyId)->count();
        $transactionCount += PurchaseReturnTransaction::whereCompanyId($companyId)->count();
        $transactionCount += ExpenseCreditNoteTransaction::whereCompanyId($companyId)->count();
        $transactionCount += ExpenseDebitNoteTransaction::whereCompanyId($companyId)->count();
        $transactionCount += PurchaseOrderTransaction::whereCompanyId($companyId)->count();

        $transactionCount += ReceiptTransaction::whereCompanyId($companyId)->count();
        $transactionCount += PaymentTransaction::whereCompanyId($companyId)->count();
        $transactionCount += JournalTransaction::whereCompanyId($companyId)->count();

        return $transactionCount;
    }
}
