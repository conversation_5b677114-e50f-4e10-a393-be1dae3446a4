<?php

namespace App\Repositories\v1;

use App\Actions\CommonAction\AdvancePaymentAction;
use App\Actions\CommonAction\CheckPartyLedgerMobileNumberAction;
use App\Actions\CommonAction\DirectCustomerSupplierCreate;
use App\Actions\CustomFields\StoreTransactionCustomFieldsAction;
use App\Actions\CustomFields\UpdateTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Actions\CustomFieldsItemMaster\Inventory\StoreItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Inventory\UpdateItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\StoreCFTransactionWiseAction;
use App\Actions\CustomFieldsItemMaster\Transaction\UpdateCFTransactionWiseAction;
use App\Models\AdditionalChargesForIncomeDebitNoteTransaction;
use App\Models\AddLessForIncomeDebitNoteTransaction;
use App\Models\GstTax;
use App\Models\IncomeDebitNoteItemTransaction;
use App\Models\IncomeDebitNoteLedgerTransaction;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldSetting;
use App\Models\Ledger;
use App\Models\Master\ItemMaster;
use App\Models\PaymentDetailsForIncomeDebitNoteTransaction;
use App\Models\ReceiptTransaction;
use App\Models\ReceiptTransactionItem;
use App\Models\SettleAdvancePayment;
use App\Models\TaxClassificationDetails;
use App\Models\TransactionCustomField;
use App\Repositories\API\v1\BaseAPIRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class IncomeDebitNoteTransactionRepository
 */
class IncomeDebitNoteTransactionRepository extends BaseAPIRepository
{
    public $classificationNatureType = null;

    public $isRCMApplicable = false;

    public $isGSTEnabled = null;

    public $oldIncomeDebitNoteTransaction = null;

    public function model()
    {
        return IncomeDebitNoteTransaction::class;
    }

    public function store($input)
    {

        try {
            DB::beginTransaction();
            unset($input['_token']);

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['customer_ledger_id'], Ledger::CUSTOMER);
                $input['customer_ledger_id'] = $partyId;
            }

            /* Ledger mobile number */
            if (isset($input['party_phone_number'])) {
                $ledgerData = [
                    'ledger_id' => $input['customer_ledger_id'],
                    'phone_1' => $input['party_phone_number'],
                    'region_iso_1' => $input['region_iso'],
                    'region_code_1' => $input['region_code'],
                ];
                CheckPartyLedgerMobileNumberAction::run($ledgerData);
            }

            /* Prepare Income Debit transaction */
            $incomeDebitNoteTransactionData = $this->prepareIncomeDebitNoteData($input);

            /* Create Income Debit Note */
            $incomeDebitNoteTransaction = IncomeDebitNoteTransaction::create($incomeDebitNoteTransactionData);

            /* Store Income Debit Note Document */
            if (isset($input['income_debit_note_document']) && ! empty($input['income_debit_note_document'])) {
                foreach ($input['income_debit_note_document'] as $image) {
                    $incomeDebitNoteTransaction->addMedia($image)->toMediaCollection(IncomeDebitNoteTransaction::INCOME_DEBIT_NOTE_DOCUMENT, config('app.media_disc'));
                }
            }

            /* Store Income Debit Note items or ledgers based on invoice type */
            if ($this->isGSTEnabled) {
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['dn_item_type'] == IncomeDebitNoteTransaction::ITEM_INVOICE) {
                $this->storeItems($input['items'], $incomeDebitNoteTransaction);
            }

            if ($input['dn_item_type'] == IncomeDebitNoteTransaction::ACCOUNTING_INVOICE) {
                $this->storeLedgers($input['ledgers'], $incomeDebitNoteTransaction);
            }

            /* Store Addresses */
            $this->storeAddresses($incomeDebitNoteTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($incomeDebitNoteTransaction->shipping_address_id) && ! empty($incomeDebitNoteTransaction->shippingAddress)) {
                $incomeDebitNoteTransaction->update([
                    'shipping_address_id' => $incomeDebitNoteTransaction->shippingAddress->id,
                ]);
            }

            /* Store Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->storeAdditionalCharges($input['additional_charges'], $incomeDebitNoteTransaction->id);
            }

            /* Store Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->storeAddLess($input['add_less'], $incomeDebitNoteTransaction->id);
            }

            /* Store Payment Details And Create Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->storePaymentDetails($input['payment_details'], $incomeDebitNoteTransaction);
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                AdvancePaymentAction::run($input['advance_payment'], $incomeDebitNoteTransaction);
            }

            /* Store Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                StoreTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::INCOME_DEBIT_NOTE,
                    $incomeDebitNoteTransaction->id,
                    IncomeDebitNoteTransaction::class
                );
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($incomeDebitNoteTransaction);

            DB::commit();

            return $incomeDebitNoteTransaction;
        } catch (Exception $exception) {
            DB::rollBack();

            throw $exception;
        }
    }

    private function prepareIncomeDebitNoteData($input): array
    {
        /* Destructure input arrays for clarity */
        $otherDetails = $input['other_details'] ?? [];
        $brokerDetails = $input['broker_details'] ?? [];
        $transportDetails = $input['transport_details'] ?? [];
        $ewayBillDetails = $input['eway_bill_details'] ?? [];
        $tcsDetails = $input['tcs_details'] ?? [];
        $tdsDetails = $input['tds_details'] ?? [];

        /* calculate credit period date */
        $creditPeriod = $otherDetails['credit_period'] ?? null;
        $creditPeriodType = $otherDetails['credit_period_type'] ?? null;
        $invoiceDate = Carbon::parse($input['date']);
        $creditPeriodDueDate = $creditPeriod != null && $creditPeriodType != null ? calculateCreditPeriodDate($creditPeriod, $creditPeriodType, $invoiceDate) : null;

        $shippingName = $input['shipping_address']['shipping_name'] ?? $input['shipping_name'] ?? null;
        $shippingGSTIN = $input['shipping_address']['shipping_gstin'] ?? $input['shipping_gstin'] ?? null;
        $addressName = $input['shipping_address']['address_name'] ?? $input['address_name'] ?? null;
        $partyNameSameAsAddressName = $input['shipping_address']['party_name_same_as_address_name'] ?? $input['party_name_same_as_address_name'] ?? false;
        if ($partyNameSameAsAddressName) {
            $addressName = $shippingName;
        }

        return [
            'company_id' => $input['company']->id,
            'payment_mode' => null, // Removed field
            'payment_type_ledger_id' => null, // Removed field
            'full_invoice_number' => $input['full_invoice_number'],
            'debit_note_number' => $input['debit_note_number'],
            'date' => Carbon::parse($input['date']),
            'customer_ledger_id' => $input['customer_ledger_id'],
            'party_phone_number' => $input['party_phone_number'] ?? null,
            'region_iso' => $input['region_iso'] ?? null,
            'region_code' => $input['region_code'] ?? null,
            'gstin' => $this->isGSTEnabled && isset($input['gstin']) ? $input['gstin'] : null,
            'original_inv_no' => $input['original_inv_no'] ?? null,
            'original_inv_date' => $input['original_inv_date'] ?? null,
            'broker_id' => $brokerDetails['broker_id'] ?? null,
            'brokerage_for_sale' => $brokerDetails['brokerage_for_sale'] ?? null,
            'brokerage_on_value_type' => $brokerDetails['brokerage_on_value_type'] ?? null,
            'credit_period' => $creditPeriod,
            'credit_period_type' => $creditPeriodType,
            'credit_period_due_date' => $creditPeriodDueDate,
            'transport_id' => $transportDetails['transport_id'] ?? null,
            'transporter_document_number' => $transportDetails['transporter_document_number'] ?? null,
            'transporter_document_date' => isset($transportDetails['transporter_document_date']) ? Carbon::parse($transportDetails['transporter_document_date']) : null,
            'transporter_vehicle_number' => $transportDetails['transporter_vehicle_number'] ?? null,
            'shipping_name' => $shippingName,
            'shipping_gstin' => $shippingGSTIN,
            'address_name' => $addressName,
            'party_name_same_as_address_name' => $partyNameSameAsAddressName,
            'po_no' => $otherDetails['po_no'] ?? null,
            'po_date' => isset($otherDetails['po_date']) ? Carbon::parse($otherDetails['po_date']) : null,
            'dn_item_type' => $input['dn_item_type'],
            'tcs_tax_id' => $tcsDetails['tcs_tax_id'] ?? null,
            'tcs_rate' => $tcsDetails['tcs_rate'] ?? null,
            'tcs_amount' => $tcsDetails['tcs_amount'] ?? null,
            'tds_tax_id' => $tdsDetails['tds_tax_id'] ?? null,
            'tds_rate' => $tdsDetails['tds_rate'] ?? null,
            'tds_amount' => $tdsDetails['tds_amount'] ?? null,
            'cgst' => $this->isGSTEnabled ? $input['cgst'] : 0,
            'sgst' => $this->isGSTEnabled ? $input['sgst'] : 0,
            'igst' => $this->isGSTEnabled ? $input['igst'] : 0,
            'rounding_amount' => $input['rounding_amount'],
            'total' => $input['grand_total'],
            'grand_total' => $input['grand_total'],
            'taxable_value' => $input['taxable_value'],
            'gross_value' => $input['gross_value'],
            'narration' => $input['narration'] ?? null,
            'term_and_condition' => $input['term_and_condition'] ?? null,
            'dispatch_address_id' => $input['dispatch_address_id'] ?? null,
            'shipping_address_id' => $input['shipping_address_id'] ?? null,
            'same_as_billing' => $input['same_as_billing'] ?? false,
            'is_gst_enabled' => $this->isGSTEnabled,
            'cess' => $this->isGSTEnabled ? $input['cess'] : 0,
            'is_cgst_sgst_igst_calculated' => $input['is_cgst_sgst_igst_calculated'],
            'is_gst_na' => $input['is_gst_na'],
            'created_by' => getLoginUser()->id,
            'via_api' => $input['via_api'] ?? false,
            'is_import' => $input['is_import'] ?? false,
            'is_round_off_not_changed' => $input['is_round_off_not_changed'] ?? true,
            'created_at' => $input['created_at'] ?? Carbon::now(), // this is for import excel
            'updated_at' => $input['updated_at'] ?? Carbon::now(), // this is for import excel
            'round_off_method' => $input['round_off_method'],
            'bank_id' => $input['bank_id'] ?? null,
        ];
    }

    private function storeAddresses($incomeDebitNoteTransaction, $input)
    {
        try {
            $dispatchAddress = $input['dispatch_address'] ?? [];
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            $addresses = [];

            if ($dispatchAddress) {
                $addresses[] = $this->prepareAddressData($incomeDebitNoteTransaction, $dispatchAddress, IncomeDebitNoteTransaction::DISPATCH_ADDRESS);
            }

            if ($billingAddress) {
                $addresses[] = $this->prepareAddressData($incomeDebitNoteTransaction, $billingAddress, IncomeDebitNoteTransaction::BILLING_ADDRESS);
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $addresses[] = $this->prepareAddressData($incomeDebitNoteTransaction, $shippingAddress, IncomeDebitNoteTransaction::SHIPPING_ADDRESS);
                }
            }

            if (! empty($addresses)) {
                $incomeDebitNoteTransaction->addresses()->createMany($addresses);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddressData($incomeDebitNoteTransaction, $address, $addressType)
    {
        return [
            'address_1' => $address['address_1'] ?? null,
            'address_2' => $address['address_2'] ?? null,
            'country_id' => $address['country_id'] ?? null,
            'state_id' => $address['state_id'] ?? null,
            'city_id' => $address['city_id'] ?? null,
            'pin_code' => $address['pin_code'] ?? null,
            'model_id' => $incomeDebitNoteTransaction->id,
            'model_type' => IncomeDebitNoteTransaction::class,
            'address_type' => $addressType,
        ];
    }

    private function storeAdditionalCharges($additionChargesData, $incomeDebitNoteTransactionId)
    {
        try {
            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $incomeDebitNoteTransactionId);

                /* Store additional charge */
                AdditionalChargesForIncomeDebitNoteTransaction::create($additionChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAdditionalChargesData($additionCharge, $incomeDebitNoteTransactionId)
    {
        if ($this->isGSTEnabled) {
            $gst = null;
            if (isset($additionCharge['ac_gst_rate_id'])) {
                $gst = GstTax::whereId($additionCharge['ac_gst_rate_id'])->first();
            }
            $gstTaxPercentage = ! empty($gst) ? $gst->tax_rate : null;
        }

        return [
            'income_debit_note_id' => $incomeDebitNoteTransactionId,
            'ledger_id' => $additionCharge['ac_ledger_id'],
            'charge_type' => $additionCharge['ac_type'],
            'value' => $additionCharge['ac_value'],
            'gst_rate_id' => $this->isGSTEnabled && ! empty($gst) ? $gst->id : null,
            'gst_percentage' => $this->isGSTEnabled ? $gstTaxPercentage : null,
            'total_without_tax' => $additionCharge['ac_total_without_tax'],
            'total' => $additionCharge['ac_total'],
        ];
    }

    private function storeAddLess($addLessData, $incomeDebitNoteTransactionId)
    {
        try {

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $incomeDebitNoteTransactionId);

                /* Store add less */
                AddLessForIncomeDebitNoteTransaction::create($addLessChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddLessData($addLessCharge, $incomeDebitNoteTransactionId)
    {
        return [
            'income_debit_note_id' => $incomeDebitNoteTransactionId,
            'ledger_id' => $addLessCharge['al_ledger_id'],
            'is_show_in_print' => $addLessCharge['al_is_show_in_print'] ?? false,
            'type' => $addLessCharge['al_type'],
            'value' => $addLessCharge['al_value'],
            'total' => $addLessCharge['al_total'],
        ];
    }

    private function storePaymentDetails($paymentDetails, $incomeDebitNoteTransaction)
    {
        try {
            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment detail data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $incomeDebitNoteTransaction);
                /* Store payment detail */
                $paymentDetailRecord = PaymentDetailsForIncomeDebitNoteTransaction::create($paymentDetailData);

                $receiptTransaction = ReceiptTransaction::create([
                    'company_id' => $incomeDebitNoteTransaction->company_id,
                    'date' => $paymentDetailData['date'],
                    'receipt_number' => 'income-debit-note/'.$incomeDebitNoteTransaction->full_invoice_number,
                    'is_default_created_by_transaction' => true,
                    'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                    'ledger_id' => $incomeDebitNoteTransaction->customer_ledger_id,
                    'total_received_amount' => $paymentDetailData['amount'],
                    'narration' => $incomeDebitNoteTransaction->narration ?? null,
                    'payment_mode' => $paymentDetailData['mode'],
                    'reference_number' => $paymentDetailData['reference_no'],
                    'created_by' => $incomeDebitNoteTransaction->created_by,
                ]);

                ReceiptTransactionItem::create([
                    'rc_transaction_id' => $receiptTransaction->id,
                    'income_debit_id' => $incomeDebitNoteTransaction->id,
                    'invoice_number' => $incomeDebitNoteTransaction->full_invoice_number,
                    'bill_type' => ReceiptTransaction::INCOME_DEBIT_NOTE_BILL_TYPE,
                    'received_amount' => $paymentDetailData['amount'],
                    'discount' => 0,
                    'round_off' => 0,
                ]);

                $paymentDetailRecord->update(['receipt_id' => $receiptTransaction->id]);

            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function preparePaymentDetailData($paymentDetail, $incomeDebitNoteTransaction)
    {
        return [
            'income_debit_note_id' => $incomeDebitNoteTransaction->id,
            'ledger_id' => $paymentDetail['pd_ledger_id'],
            'date' => Carbon::parse($paymentDetail['pd_date']),
            'amount' => $paymentDetail['pd_amount'],
            'mode' => $paymentDetail['pd_mode'] ?? null,
            'reference_no' => $paymentDetail['pd_reference_number'] ?? null,
        ];
    }

    private function storeItems($items, $incomeDebitNoteTransaction)
    {
        try {
            foreach ($items as $key => $item) {
                /* First check if item inventory has custom fields and user not selected any inventory for this item then throw error */
                $iteminventoryCustomFields = ItemCustomFieldSetting::where('item_id', $item['item_id'])->count();

                if ($iteminventoryCustomFields > 0) {
                    if (! (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0)) {
                        response()->json(['message' => 'Please select inventory for this item or otherwise inward inventory.'], 422)->send();
                        exit();
                    }
                }

                /* Prepare item data */
                $incomeDebitNoteTransactionItemData = $this->prepareItemData($item, $incomeDebitNoteTransaction);

                /* Store item */
                $incomeDebitNoteTransactionItem = IncomeDebitNoteItemTransaction::create($incomeDebitNoteTransactionItemData);

                /* Store Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    StoreCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::INCOME_DEBIT_NOTE,
                        $incomeDebitNoteTransactionItem->id,
                        IncomeDebitNoteItemTransaction::class
                    );
                }

                /* Store Custom Fields for Item Inventory */
                if (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0) {
                    $sortedInventory = collect($item['custom_field_inventory'])->map(function ($group) {
                        return collect($group)->sortBy('custom_field_id')->values();
                    })->toArray();

                    StoreItemCFInventoryAction::run(
                        $sortedInventory,
                        $item['item_id'],
                        $incomeDebitNoteTransactionItem->id,
                        IncomeDebitNoteItemTransaction::class
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareItemData($item, $incomeDebitNoteTransaction)
    {
        $cessRate = 0;
        $cessAmount = 0;
        if ($this->isGSTEnabled) {
            $itemMaster = ItemMaster::with(['model.gstGstCessRate'])->whereId($item['item_id'])->first();
            $cessRate = $itemMaster->model->gstGstCessRate->rate ?? 0;
            $taxableValue = ($item['quantity'] * $item['rpu_without_gst']) - $item['total_discount_amount'];
            $cessAmount = ($taxableValue * $cessRate) / 100;
        }

        return [
            'income_dn_id' => $incomeDebitNoteTransaction->id,
            'item_id' => $item['item_id'],
            'additional_description' => $item['additional_description'] ?? null,
            'ledger_id' => $item['ledger_id'],
            'unit_id' => $item['unit_id'],
            'hsn_code' => $item['hsn_code'] ?? null,
            'quantity' => $item['quantity'],
            'mrp' => $item['mrp'] ?? null,
            'with_tax' => $item['with_tax'] ?? false,
            'rpu_with_gst' => $item['rpu_with_gst'],
            'rpu_without_gst' => $item['rpu_without_gst'],
            'discount_type' => $item['discount_type'] ?? null,
            'discount_value' => $item['discount_value'] ?? null,
            'discount_type_2' => $item['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $item['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $item['total_discount_amount'] ?? 0,
            'gst_id' => $item['gst_tax'],
            'gst_tax_percentage' => $item['gst_tax_percentage'],
            'total' => $item['total'],
            'classification_nature_type' => $this->classificationNatureType,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_igst_tax' => $this->isGSTEnabled ? $item['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $item['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $item['classification_sgst_tax'] : null,
            'classification_cess_tax' => $this->isGSTEnabled && isset($item['cess']) ? $item['cess'] : null,
            'consolidating_items_to_invoice' => $item['consolidating_items_to_invoice'] ?? false,
            'cess_rate' => $cessRate,
            'cess_amount' => $cessAmount,
            'decimal_places_for_quantity' => $item['decimal_places'] ?? 2,
            'decimal_places_for_rate' => $item['decimal_places_for_rate'] ?? 2,
        ];
    }

    private function storeLedgers($ledgers, $incomeDebitNoteTransaction)
    {
        try {
            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $incomeDebitNoteTransactionLedgerData = $this->prepareLedgerData($ledger, $incomeDebitNoteTransaction);

                /* Store ledger */
                $incomeDebitNoteTransactionLedger = IncomeDebitNoteLedgerTransaction::create($incomeDebitNoteTransactionLedgerData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareLedgerData($ledger, $incomeDebitNoteTransaction)
    {
        return [
            'income_dn_id' => $incomeDebitNoteTransaction->id,
            'ledger_id' => $ledger['ledger_id'],
            'additional_description' => $ledger['additional_description'] ?? null,
            'with_tax' => $ledger['with_tax'] ?? false,
            'rpu_with_gst' => $ledger['rpu_with_gst'],
            'rpu_without_gst' => $ledger['rpu_without_gst'],
            'discount_type' => $ledger['discount_type'] ?? null,
            'discount_value' => $ledger['discount_value'] ?? null,
            'discount_type_2' => $ledger['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $ledger['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $ledger['total_discount_amount'] ?? 0,
            'gst_id' => $ledger['gst_tax'],
            'gst_tax_percentage' => $ledger['gst_tax_percentage'],
            'total' => $ledger['total'],
            'classification_igst_tax' => $this->isGSTEnabled ? $ledger['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $ledger['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $ledger['classification_sgst_tax'] : null,
            // 'classification_cess_tax' => $this->isGSTEnabled ? $ledger['cess'] : null,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_nature_type' => $this->classificationNatureType,
        ];
    }

    public function update($input, $incomeDebitNoteTransaction)
    {
        try {
            DB::beginTransaction();

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* Old Income Debit Note transaction */
            $this->oldIncomeDebitNoteTransaction = clone $incomeDebitNoteTransaction;

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['customer_ledger_id'], Ledger::CUSTOMER);
                $input['customer_ledger_id'] = $partyId;
            }

            /* Ledger mobile number */
            if (isset($input['party_phone_number'])) {
                $ledgerData = [
                    'ledger_id' => $input['customer_ledger_id'],
                    'phone_1' => $input['party_phone_number'],
                    'region_iso_1' => $input['region_iso'],
                    'region_code_1' => $input['region_code'],
                ];
                CheckPartyLedgerMobileNumberAction::run($ledgerData);
            }

            /* Prepare Income Debit Note transaction */
            $input['created_at'] = $this->oldIncomeDebitNoteTransaction->created_at;
            $incomeDebitNoteTransactionData = $this->prepareIncomeDebitNoteData($input);
            unset($incomeDebitNoteTransactionData['debit_note_number']);

            /* Update Income Debit Note transaction */
            /** @var IncomeDebitNoteTransaction $incomeDebitNoteTransaction */
            $incomeDebitNoteTransaction->update($incomeDebitNoteTransactionData);

            /* Update Income Debit Note Document */
            if (isset($input['income_debit_note_document']) && ! empty($input['income_debit_note_document'])) {
                foreach ($input['income_debit_note_document'] as $image) {
                    $incomeDebitNoteTransaction->addMedia($image)->toMediaCollection(IncomeDebitNoteTransaction::INCOME_DEBIT_NOTE_DOCUMENT, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Update Income Debit Note items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['dn_item_type'] == IncomeDebitNoteTransaction::ITEM_INVOICE && $this->oldIncomeDebitNoteTransaction->dn_item_type == $input['dn_item_type']) {
                $this->updateItems($input['items'], $incomeDebitNoteTransaction);
            } elseif ($input['dn_item_type'] == IncomeDebitNoteTransaction::ACCOUNTING_INVOICE && $this->oldIncomeDebitNoteTransaction->dn_item_type == $input['dn_item_type']) {
                $this->updateLedgers($input['ledgers'], $incomeDebitNoteTransaction);
            } elseif ($input['dn_item_type'] == IncomeDebitNoteTransaction::ITEM_INVOICE && $this->oldIncomeDebitNoteTransaction->dn_item_type != $input['dn_item_type']) {
                $incomeDebitNoteTransaction->incomeDebitNoteLedgers()->delete();
                $this->storeItems($input['items'], $incomeDebitNoteTransaction);
            } elseif ($input['dn_item_type'] == IncomeDebitNoteTransaction::ACCOUNTING_INVOICE && $this->oldIncomeDebitNoteTransaction->dn_item_type != $input['dn_item_type']) {
                $incomeDebitNoteTransaction->incomeDebitNoteItems()->delete();
                $this->storeLedgers($input['ledgers'], $incomeDebitNoteTransaction);
            }

            /* Update Addresses */
            $this->updateAddresses($incomeDebitNoteTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($incomeDebitNoteTransaction->shipping_address_id) && ! empty($incomeDebitNoteTransaction->shippingAddress)) {
                $incomeDebitNoteTransaction->update([
                    'shipping_address_id' => $incomeDebitNoteTransaction->shippingAddress->id,
                ]);
            }

            /* Update or Create Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->updateAdditionalCharges($input['additional_charges'], $incomeDebitNoteTransaction->id);
            } else {
                $incomeDebitNoteTransaction->additionalCharges()->delete();
            }

            /* Update or Create Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->updateAddLess($input['add_less'], $incomeDebitNoteTransaction->id);
            } else {
                $incomeDebitNoteTransaction->addLess()->delete();
            }

            /* Update or Create Payment Details And Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->updatePaymentDetails($input['payment_details'], $incomeDebitNoteTransaction);
            } else {
                $ledgerIdsForRemoveReceipts = $incomeDebitNoteTransaction->paymentDetails->pluck('ledger_id')->toArray();

                ReceiptTransaction::where('receipt_number', 'income-debit-note/'.$this->oldIncomeDebitNoteTransaction->full_invoice_number)
                    ->where('ledger_id', $this->oldIncomeDebitNoteTransaction->customer_ledger_id)
                    ->whereIn('bank_cash_ledger_id', $ledgerIdsForRemoveReceipts)
                    ->financialYearDate()?->forceDelete();

                $incomeDebitNoteTransaction->paymentDetails()->delete();
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                (new AdvancePaymentAction)->updateAdvancePayment($input['advance_payment'], $incomeDebitNoteTransaction);
            } else {
                SettleAdvancePayment::whereModelId($incomeDebitNoteTransaction->id)->whereModelType(get_class($incomeDebitNoteTransaction))->delete();
            }

            /* Update Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                UpdateTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::INCOME_DEBIT_NOTE,
                    $incomeDebitNoteTransaction->id,
                    IncomeDebitNoteTransaction::class
                );
            } else {
                $incomeDebitNoteTransaction->customFieldValues()->delete();
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($incomeDebitNoteTransaction);

            DB::commit();

            return $incomeDebitNoteTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    private function updateItems($items, $incomeDebitNoteTransaction)
    {
        try {
            /* Get item ids to be removed */
            $itemIds = IncomeDebitNoteItemTransaction::whereIncomeDnId($incomeDebitNoteTransaction->id)->pluck('id')->toArray();
            $editIncomeDebitNoteItemIds = Arr::pluck($items, 'id') ?? [];
            $removeItemIds = array_diff($itemIds, $editIncomeDebitNoteItemIds);

            /* Delete removed items */
            if (count($removeItemIds) > 0) {
                IncomeDebitNoteItemTransaction::whereIn('id', array_values($removeItemIds))?->delete();

                // if remove item from transaction then update remaining quantity in combination
                $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', array_values($removeItemIds))
                    ->where('model_type', IncomeDebitNoteItemTransaction::class)
                    ->get();

                foreach ($inventoryItems as $inventoryItem) {
                    $combination = $inventoryItem->itemCustomFieldCombination;
                    $inventoryItem->delete();
                    CalculateAvailableQTY::run($combination);
                }
            }

            foreach ($items as $key => $item) {
                /* First check if item inventory has custom fields and user not selected any inventory for this item then throw error */
                $iteminventoryCustomFields = ItemCustomFieldSetting::where('item_id', $item['item_id'])->count();

                if ($iteminventoryCustomFields > 0) {
                    if (! (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0)) {
                        response()->json(['message' => 'Please select inventory for this item or otherwise inward inventory.'], 422)->send();
                        exit();
                    }
                }

                /* Prepare item data */
                $incomeDebitNoteTransactionItemData = $this->prepareItemData($item, $incomeDebitNoteTransaction);

                /* Update or create item */
                if (! isset($item['id']) || $item['id'] == null) {
                    $incomeDebitNoteTransactionItem = IncomeDebitNoteItemTransaction::create($incomeDebitNoteTransactionItemData);
                } else {
                    $incomeDebitNoteTransactionItem = IncomeDebitNoteItemTransaction::whereId($item['id'])->first();
                    if (! empty($incomeDebitNoteTransactionItem)) {
                        $incomeDebitNoteTransactionItem->update($incomeDebitNoteTransactionItemData);
                    }
                }

                /* Update Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    UpdateCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::INCOME_DEBIT_NOTE,
                        $incomeDebitNoteTransactionItem->id,
                        IncomeDebitNoteItemTransaction::class
                    );
                } else {
                    $incomeDebitNoteTransactionItem->customFieldTransactionItemsValues()->delete();
                }

                /* Update Custom Fields for Item Inventory */
                if (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0) {
                    $sortedInventory = collect($item['custom_field_inventory'])->map(function ($group) {
                        return collect($group)->sortBy('custom_field_id')->values();
                    })->toArray();

                    UpdateItemCFInventoryAction::run(
                        $sortedInventory,
                        $item['item_id'],
                        $incomeDebitNoteTransactionItem->id,
                        IncomeDebitNoteItemTransaction::class
                    );
                } else {
                    // if custom field inventory is removed then update remaining quantity in combination row batch as well
                    $inventoryItems = $incomeDebitNoteTransactionItem->customFieldTransactionItemsInventoryValues;

                    foreach ($inventoryItems as $inventoryItem) {
                        $combination = $inventoryItem->itemCustomFieldCombination;
                        $inventoryItem->delete();
                        CalculateAvailableQTY::run($combination);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateLedgers($ledgers, $incomeDebitNoteTransaction)
    {
        try {

            $ledgerIds = IncomeDebitNoteLedgerTransaction::whereIncomeDnId($incomeDebitNoteTransaction->id)->pluck('id')->toArray();
            $editIncomeDebitNoteLedgerIds = Arr::pluck($ledgers, 'id') ?? [];
            $removeLedgerIds = array_diff($ledgerIds, $editIncomeDebitNoteLedgerIds);

            /* Delete removed ledgers */
            IncomeDebitNoteLedgerTransaction::whereIn('id', array_values($removeLedgerIds))?->delete();

            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $incomeDebitNoteTransactionLedgerData = $this->prepareLedgerData($ledger, $incomeDebitNoteTransaction);

                /* Update or create ledger */
                if (! isset($ledger['id']) || $ledger['id'] == null) {
                    $incomeDebitNoteTransactionLedger = IncomeDebitNoteLedgerTransaction::create($incomeDebitNoteTransactionLedgerData);
                } else {
                    $incomeDebitNoteTransactionLedger = IncomeDebitNoteLedgerTransaction::whereId($ledger['id'])->first();
                    if (! empty($incomeDebitNoteTransactionLedger)) {
                        $incomeDebitNoteTransactionLedger->update($incomeDebitNoteTransactionLedgerData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddresses($incomeDebitNoteTransaction, $input)
    {
        try {
            $dispatchAddress = $input['dispatch_address'] ?? [];
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            if ($dispatchAddress) {
                $incomeDebitNoteTransaction->addresses()->updateOrCreate(
                    ['address_type' => IncomeDebitNoteTransaction::DISPATCH_ADDRESS, 'model_id' => $incomeDebitNoteTransaction->id],
                    $this->prepareAddressData($incomeDebitNoteTransaction, $dispatchAddress, IncomeDebitNoteTransaction::DISPATCH_ADDRESS)
                );
            }

            if ($billingAddress) {
                $incomeDebitNoteTransaction->addresses()->updateOrCreate(
                    ['address_type' => IncomeDebitNoteTransaction::BILLING_ADDRESS, 'model_id' => $incomeDebitNoteTransaction->id],
                    $this->prepareAddressData($incomeDebitNoteTransaction, $billingAddress, IncomeDebitNoteTransaction::BILLING_ADDRESS)
                );
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $incomeDebitNoteTransaction->addresses()->updateOrCreate(
                        ['address_type' => IncomeDebitNoteTransaction::SHIPPING_ADDRESS, 'model_id' => $incomeDebitNoteTransaction->id],
                        $this->prepareAddressData($incomeDebitNoteTransaction, $shippingAddress, IncomeDebitNoteTransaction::SHIPPING_ADDRESS)
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAdditionalCharges($additionChargesData, $incomeDebitNoteTransactionId)
    {
        try {
            $additionalChargesIds = AdditionalChargesForIncomeDebitNoteTransaction::where('income_debit_note_id', $incomeDebitNoteTransactionId)->pluck('id')->toArray();
            $editedAdditionalChargesIds = Arr::pluck($additionChargesData, 'id');
            $removeAdditionalChargesIds = array_diff($additionalChargesIds, $editedAdditionalChargesIds);

            /* Delete additional charges */
            AdditionalChargesForIncomeDebitNoteTransaction::whereIn('id', array_values($removeAdditionalChargesIds))?->delete();

            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $incomeDebitNoteTransactionId);

                /* Update additional charge */
                if (! isset($additionCharge['id']) || $additionCharge['id'] == null) {
                    AdditionalChargesForIncomeDebitNoteTransaction::create($additionChargeData);
                } else {
                    $additionalChargeRecord = AdditionalChargesForIncomeDebitNoteTransaction::where('id', $additionCharge['id'])->first();
                    if (! empty($additionalChargeRecord)) {
                        $additionalChargeRecord->update($additionChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddLess($addLessData, $incomeDebitNoteTransactionId)
    {
        try {
            $addLessIds = AddLessForIncomeDebitNoteTransaction::where('income_debit_note_id', $incomeDebitNoteTransactionId)->pluck('id')->toArray();
            $editedAddLessIds = Arr::pluck($addLessData, 'id');
            $removeAddLessIds = array_diff($addLessIds, $editedAddLessIds);

            /* Delete add less */
            AddLessForIncomeDebitNoteTransaction::whereIn('id', array_values($removeAddLessIds))?->delete();

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $incomeDebitNoteTransactionId);

                /* Update add less */
                if (! isset($addLessCharge['id']) || $addLessCharge['id'] == null) {
                    AddLessForIncomeDebitNoteTransaction::create($addLessChargeData);
                } else {
                    $addLessRecord = AddLessForIncomeDebitNoteTransaction::where('id', $addLessCharge['id'])->first();
                    if (! empty($addLessRecord)) {
                        $addLessRecord->update($addLessChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updatePaymentDetails($paymentDetails, $incomeDebitNoteTransaction)
    {
        try {
            $paymentDetailsIds = PaymentDetailsForIncomeDebitNoteTransaction::where('income_debit_note_id', $incomeDebitNoteTransaction->id)->pluck('id')->toArray();
            $editedPaymentDetailsIds = Arr::pluck($paymentDetails, 'pd_id');
            $removePaymentDetailsIds = array_diff($paymentDetailsIds, $editedPaymentDetailsIds);

            /* Delete payment details */
            foreach ($removePaymentDetailsIds as $removePaymentDetailsId) {
                $removePaymentDetails = PaymentDetailsForIncomeDebitNoteTransaction::where('id', $removePaymentDetailsId)->first();

                $receiptTransaction = ReceiptTransaction::where('receipt_number', 'income-debit-note/'.$this->oldIncomeDebitNoteTransaction->full_invoice_number)
                    ->where('ledger_id', $this->oldIncomeDebitNoteTransaction->customer_ledger_id)
                    ->where('bank_cash_ledger_id', $removePaymentDetails->ledger_id)
                    ->whereId($removePaymentDetails->receipt_id)
                    ->financialYearDate()?->forceDelete();

                $removePaymentDetails->delete();
            }

            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment details data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $incomeDebitNoteTransaction);

                /* Update payment details */
                if (! isset($paymentDetail['pd_id']) || $paymentDetail['pd_id'] == null) {
                    $paymentDetailRecord = PaymentDetailsForIncomeDebitNoteTransaction::create($paymentDetailData);

                    $receiptTransaction = ReceiptTransaction::create([
                        'company_id' => $incomeDebitNoteTransaction->company_id,
                        'date' => $paymentDetailData['date'],
                        'receipt_number' => 'income-debit-note/'.$incomeDebitNoteTransaction->full_invoice_number,
                        'is_default_created_by_transaction' => true,
                        'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                        'ledger_id' => $incomeDebitNoteTransaction->customer_ledger_id,
                        'total_received_amount' => $paymentDetailData['amount'],
                        'narration' => $incomeDebitNoteTransaction->narration ?? null,
                        'payment_mode' => $paymentDetailData['mode'],
                        'reference_number' => $paymentDetailData['reference_no'],
                        'created_by' => $incomeDebitNoteTransaction->created_by,
                    ]);

                    ReceiptTransactionItem::create([
                        'rc_transaction_id' => $receiptTransaction->id,
                        'income_debit_id' => $incomeDebitNoteTransaction->id,
                        'invoice_number' => $incomeDebitNoteTransaction->full_invoice_number,
                        'bill_type' => ReceiptTransaction::INCOME_DEBIT_NOTE_BILL_TYPE,
                        'received_amount' => $paymentDetailData['amount'],
                        'discount' => 0,
                        'round_off' => 0,
                    ]);
                } else {
                    $paymentDetailRecord = PaymentDetailsForIncomeDebitNoteTransaction::where('id', $paymentDetail['pd_id'])->first();
                    if (! empty($paymentDetailRecord)) {
                        $paymentDetailRecord->update($paymentDetailData);
                    }

                    $receiptTransaction = ReceiptTransaction::where('receipt_number', 'income-debit-note/'.$this->oldIncomeDebitNoteTransaction->full_invoice_number)
                        ->where('ledger_id', $this->oldIncomeDebitNoteTransaction->customer_ledger_id)
                        ->whereId($paymentDetailRecord->receipt_id)
                        ->first();

                    if (! empty($receiptTransaction)) {
                        $receiptTransaction->update([
                            'receipt_number' => 'income-debit-note/'.$incomeDebitNoteTransaction->full_invoice_number,
                            'date' => $paymentDetailData['date'],
                            'ledger_id' => $incomeDebitNoteTransaction->customer_ledger_id,
                            'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                            'total_received_amount' => $paymentDetailData['amount'],
                            'narration' => $incomeDebitNoteTransaction->narration ?? null,
                            'payment_mode' => $paymentDetailData['mode'],
                            'reference_number' => $paymentDetailData['reference_no'],
                        ]);

                        $receiptTransactionItem = ReceiptTransactionItem::where('rc_transaction_id', $receiptTransaction->id)->first();
                        if (! empty($receiptTransactionItem)) {
                            $receiptTransactionItem->update([
                                'invoice_number' => $incomeDebitNoteTransaction->full_invoice_number,
                                'received_amount' => $paymentDetailData['amount'],
                            ]);
                        }
                    }
                }

                $paymentDetailRecord->update(['receipt_id' => $receiptTransaction->id]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}
