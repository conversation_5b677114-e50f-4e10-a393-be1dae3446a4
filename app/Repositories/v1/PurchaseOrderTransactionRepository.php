<?php

namespace App\Repositories\v1;

use App\Actions\CommonAction\DirectCustomerSupplierCreate;
use App\Actions\CustomFields\StoreTransactionCustomFieldsAction;
use App\Actions\CustomFields\UpdateTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Actions\CustomFieldsItemMaster\Inventory\StoreItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Inventory\UpdateItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\StoreCFTransactionWiseAction;
use App\Actions\CustomFieldsItemMaster\Transaction\UpdateCFTransactionWiseAction;
use App\Models\AdditionalChargesForPurchaseOrderTransaction;
use App\Models\AddLessForPurchaseOrderTransaction;
use App\Models\GstTax;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldCombination;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldSetting;
use App\Models\Ledger;
use App\Models\Master\ItemMaster;
use App\Models\PurchaseOrderAccountingInvoice;
use App\Models\PurchaseOrderItemInvoice;
use App\Models\PurchaseOrderTransaction;
use App\Models\TaxClassificationDetails;
use App\Models\TransactionCustomField;
use App\Repositories\API\v1\BaseAPIRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Interface PurchaseOrderTransactionRepository.
 */
class PurchaseOrderTransactionRepository extends BaseAPIRepository
{
    public $isGSTEnabled = null;

    public $classificationNatureType = null;

    public $isRCMApplicable = false;

    public function model()
    {
        return PurchaseOrderTransaction::class;
    }

    public function store($input)
    {
        try {
            DB::beginTransaction();
            unset($input['_token']);

            $this->isGSTEnabled = $input['is_gst_enabled'];

            $input['valid_till_date'] = null;
            if (! empty($input['valid_for'])) {
                if ($input['valid_for_type'] == PurchaseOrderTransaction::CREDIT_PERIOD_TYPE_DAY) {
                    $input['valid_till_date'] = Carbon::parse($input['order_date'])->addDays($input['valid_for']);
                } else {
                    $input['valid_till_date'] = Carbon::parse($input['order_date'])->addMonths($input['valid_for']);
                }
            }

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['party_ledger_id'], Ledger::SUPPLIER);
                $input['party_ledger_id'] = $partyId;
            }

            /* Prepare Purchase Order transaction */
            $purchaseOrderTransactionData = $this->preparePurchaseOrderData($input);

            /* Create Purchase Order */
            $purchaseOrderTransaction = PurchaseOrderTransaction::create($purchaseOrderTransactionData);

            /* Store Purchase Order Document */
            if (isset($input['purchase_order_document']) && ! empty($input['purchase_order_document'])) {
                foreach ($input['purchase_order_document'] as $image) {
                    $purchaseOrderTransaction->addMedia($image)->toMediaCollection(PurchaseOrderTransaction::PURCHASE_ORDER_INVOICE, config('app.media_disc'));
                }
            }

            /* Store Purchase Order items or ledgers based on invoice type */
            if ($this->isGSTEnabled) {
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['order_type'] == PurchaseOrderTransaction::ITEM_INVOICE) {
                $this->storeItems($input['items'], $purchaseOrderTransaction);
            }

            if ($input['order_type'] == PurchaseOrderTransaction::ACCOUNTING_INVOICE) {
                $this->storeLedgers($input['ledgers'], $purchaseOrderTransaction);
            }

            /* Store Addresses */
            $this->storeAddresses($purchaseOrderTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($purchaseOrderTransaction->shipping_address_id) && ! empty($purchaseOrderTransaction->shippingAddress)) {
                $purchaseOrderTransaction->update([
                    'shipping_address_id' => $purchaseOrderTransaction->shippingAddress->id,
                ]);
            }

            /* Store Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->storeAdditionalCharges($input['additional_charges'], $purchaseOrderTransaction->id);
            }

            /* Store Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->storeAddLess($input['add_less'], $purchaseOrderTransaction->id);
            }

            /* Store Transaction Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                StoreTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::PURCHASE_ORDER,
                    $purchaseOrderTransaction->id,
                    PurchaseOrderTransaction::class
                );
            }

            /* Update Transaction Status Column Value */
            updatePurchaseOrderStatus($purchaseOrderTransaction->id);

            DB::commit();

            return $purchaseOrderTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    public function update($input, $purchaseOrderTransaction)
    {
        try {
            DB::beginTransaction();

            $this->isGSTEnabled = $input['is_gst_enabled'];

            $oldPurchaseOrderTransaction = clone $purchaseOrderTransaction;

            $input['valid_till_date'] = null;
            if (! empty($input['valid_for'])) {
                if ($input['valid_for_type'] == PurchaseOrderTransaction::CREDIT_PERIOD_TYPE_DAY) {
                    $input['valid_till_date'] = Carbon::parse($input['order_date'])->addDays($input['valid_for']);
                } else {
                    $input['valid_till_date'] = Carbon::parse($input['order_date'])->addMonths($input['valid_for']);
                }
            }

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['party_ledger_id'], Ledger::SUPPLIER);
                $input['party_ledger_id'] = $partyId;
            }

            /* Prepare Purchase Order transaction */
            $input['created_at'] = $oldPurchaseOrderTransaction->created_at;
            $purchaseOrderTransactionData = $this->preparePurchaseOrderData($input);
            unset($purchaseOrderTransactionData['order_number']);

            /* Update Purchase Order */
            /** @var PurchaseOrderTransaction $purchaseOrderTransaction */
            $purchaseOrderTransaction->update($purchaseOrderTransactionData);

            /* Update Purchase Order Document */
            if (isset($input['purchase_order_document']) && ! empty($input['purchase_order_document'])) {
                foreach ($input['purchase_order_document'] as $image) {
                    $purchaseOrderTransaction->addMedia($image)->toMediaCollection(PurchaseOrderTransaction::PURCHASE_ORDER_INVOICE, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Update Purchase Order items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['order_type'] == PurchaseOrderTransaction::ITEM_INVOICE && $oldPurchaseOrderTransaction->order_type == $input['order_type']) {
                $this->updateItems($input['items'], $purchaseOrderTransaction);
            } elseif ($input['order_type'] == PurchaseOrderTransaction::ACCOUNTING_INVOICE && $oldPurchaseOrderTransaction->order_type == $input['order_type']) {
                $this->updateLedgers($input['ledgers'], $purchaseOrderTransaction);
            } elseif ($input['order_type'] == PurchaseOrderTransaction::ITEM_INVOICE && $oldPurchaseOrderTransaction->order_type != $input['order_type']) {
                $purchaseOrderTransaction->transactionLedgers()->delete();
                $this->storeItems($input['items'], $purchaseOrderTransaction);
            } elseif ($input['order_type'] == PurchaseOrderTransaction::ACCOUNTING_INVOICE && $oldPurchaseOrderTransaction->order_type != $input['order_type']) {
                $purchaseOrderTransaction->transactionItems()->delete();
                $this->storeLedgers($input['ledgers'], $purchaseOrderTransaction);
            }

            /* Update Addresses */
            $this->updateAddresses($purchaseOrderTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($purchaseOrderTransaction->shipping_address_id) && ! empty($purchaseOrderTransaction->shippingAddress)) {
                $purchaseOrderTransaction->update([
                    'shipping_address_id' => $purchaseOrderTransaction->shippingAddress->id,
                ]);
            }

            /* Update or Create Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->updateAdditionalCharges($input['additional_charges'], $purchaseOrderTransaction->id);
            } else {
                $purchaseOrderTransaction->additionalCharges()->delete();
            }

            /* Update or Create Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->updateAddLess($input['add_less'], $purchaseOrderTransaction->id);
            } else {
                $purchaseOrderTransaction->addLess()->delete();
            }

            /* Update Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                UpdateTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::PURCHASE_ORDER,
                    $purchaseOrderTransaction->id,
                    PurchaseOrderTransaction::class
                );
            } else {
                $purchaseOrderTransaction->customFieldValues()->delete();
            }

            /* Update Transaction Status Column Value */
            updatePurchaseOrderStatus($purchaseOrderTransaction->id);

            DB::commit();

            return $purchaseOrderTransaction;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    private function preparePurchaseOrderData($input): array
    {
        /* Destructure input arrays for clarity */
        $brokerDetails = $input['broker_details'] ?? [];
        $transportDetails = $input['transport_details'] ?? [];

        /* Calculate Valid Till Date */
        $validTillDate = null;
        if (! empty($input['valid_for'])) {
            if ($input['valid_for_type'] == PurchaseOrderTransaction::VALID_FOR_TYPE_DAY) {
                $validTillDate = Carbon::parse($input['order_date'])->addDays($input['valid_for']);
            } else {
                $validTillDate = Carbon::parse($input['order_date'])->addMonths($input['valid_for']);
            }
        }

        $shippingName = $input['shipping_address']['shipping_name'] ?? $input['shipping_name'] ?? null;
        $shippingGSTIN = $input['shipping_address']['shipping_gstin'] ?? $input['shipping_gstin'] ?? null;
        $addressName = $input['shipping_address']['address_name'] ?? $input['address_name'] ?? null;
        $partyNameSameAsAddressName = $input['shipping_address']['party_name_same_as_address_name'] ?? $input['party_name_same_as_address_name'] ?? false;
        if ($partyNameSameAsAddressName) {
            $addressName = $shippingName;
        }

        return [
            'company_id' => $input['company']->id,
            'title' => $input['title'],
            'order_number' => $input['order_number'],
            'full_order_number' => $input['full_order_number'],
            'order_date' => Carbon::parse($input['order_date']),
            'valid_for' => $input['valid_for'] ?? null,
            'valid_for_type' => $input['valid_for_type'] ?? 2,
            'valid_till_date' => $validTillDate,
            'party_ledger_id' => $input['party_ledger_id'],
            'gstin' => $this->isGSTEnabled && isset($input['gstin']) ? $input['gstin'] : null,
            'broker_id' => $brokerDetails['broker_id'] ?? null,
            'brokerage' => $brokerDetails['brokerage_for_sale'] ?? null,
            'brokerage_on_value_type' => $brokerDetails['brokerage_on_value_type'] ?? null,
            'shipping_name' => $shippingName,
            'shipping_gstin' => $shippingGSTIN,
            'address_name' => $addressName,
            'party_name_same_as_address_name' => $partyNameSameAsAddressName,
            'transport_id' => $transportDetails['transport_id'] ?? null,
            'transporter_document_number' => $transportDetails['transporter_document_number'] ?? null,
            'transporter_document_date' => isset($transportDetails['transporter_document_date']) ? Carbon::parse($transportDetails['transporter_document_date']) : null,
            'transporter_vehicle_number' => $transportDetails['transporter_vehicle_number'] ?? null,
            'order_type' => $input['order_type'],
            'cgst' => $this->isGSTEnabled ? $input['cgst'] : 0,
            'sgst' => $this->isGSTEnabled ? $input['sgst'] : 0,
            'igst' => $this->isGSTEnabled ? $input['igst'] : 0,
            'cess' => $this->isGSTEnabled ? $input['cess'] : 0,
            'round_off_amount' => $input['rounding_amount'] ?? null,
            'total' => $input['grand_total'],
            'grand_total' => $input['grand_total'],
            'taxable_value' => $input['taxable_value'],
            'gross_value' => $input['gross_value'],
            'narration' => $input['narration'] ?? null,
            'shipping_address_id' => $input['shipping_address_id'] ?? null,
            'same_as_billing' => $input['same_as_billing'] ?? false,
            'is_gst_enabled' => $input['is_gst_enabled'],
            'status' => PurchaseOrderTransaction::OPEN,
            'is_cgst_sgst_igst_calculated' => $input['is_cgst_sgst_igst_calculated'],
            'is_gst_na' => $input['is_gst_na'],
            'created_by' => getLoginUser()->id,
            'via_api' => $input['via_api'] ?? false,
            'is_import' => $input['is_import'] ?? false,
            'is_round_off_not_changed' => $input['is_round_off_not_changed'] ?? true,
            'created_at' => $input['created_at'] ?? Carbon::now(), // this is for import excel
            'updated_at' => $input['updated_at'] ?? Carbon::now(), // this is for import excel
            'round_off_method' => $input['round_off_method'],
        ];
    }

    private function storeItems($items, $purchaseOrderTransaction)
    {
        try {
            foreach ($items as $key => $item) {
                /* First check if item has inventory custom fields and user not added any inventory for this item then throw error */
                $iteminventoryCustomFields = ItemCustomFieldSetting::where('item_id', $item['item_id'])->count();

                if ($iteminventoryCustomFields > 0) {
                    if (! (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0)) {
                        response()->json(['message' => 'Please Add inventory for this item.'], 422)->send();
                        exit();
                    }
                }

                /* Prepare item data */
                $purchaseOrderTransactionItemData = $this->prepareItemData($item, $purchaseOrderTransaction);

                /* Store item */
                $purchaseOrderTransactionItem = PurchaseOrderItemInvoice::create($purchaseOrderTransactionItemData);

                /* Store Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    StoreCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::PURCHASE_ORDER,
                        $purchaseOrderTransactionItem->id,
                        PurchaseOrderItemInvoice::class
                    );
                }

                /* Store Custom Fields for Item Inventory */
                if (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0) {
                    $inventoryData = $item['custom_field_inventory'];
                    $purchaseRate = $item['rpu_without_gst'] ?? null;
                    $purchaseDate = $purchaseOrderTransaction['order_date'] ? Carbon::parse($purchaseOrderTransaction['order_date'])->format('d-m-Y') : null;

                    foreach ($inventoryData as &$inventoryGroup) {
                        foreach ($inventoryGroup as &$inventoryItem) {
                            $inventoryItem['purchase_rate'] = $purchaseRate;
                            $inventoryItem['purchase_date'] = $purchaseDate;
                        }
                    }
                    unset($inventoryGroup, $inventoryItem);

                    $sortedInventory = collect($inventoryData)->map(function ($group) {
                        return collect($group)->sortBy('custom_field_id')->values();
                    })->toArray();

                    StoreItemCFInventoryAction::run(
                        $sortedInventory,
                        $item['item_id'],
                        $purchaseOrderTransactionItem->id,
                        PurchaseOrderItemInvoice::class
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateItems($items, $purchaseOrderTransaction)
    {
        try {
            /* Get item ids to be removed */
            $itemIds = PurchaseOrderItemInvoice::whereTransactionsId($purchaseOrderTransaction->id)->pluck('id')->toArray();

            $editPurchaseOrderItemIds = Arr::pluck($items, 'id') ?? [];
            $removeItemIds = array_diff($itemIds, $editPurchaseOrderItemIds);

            /* Delete removed items */
            if (count($removeItemIds) > 0) {
                PurchaseOrderItemInvoice::whereIn('id', array_values($removeItemIds))?->delete();

                $removeCombinationIds = [];
                $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', array_values($removeItemIds))
                    ->where('model_type', PurchaseOrderItemInvoice::class)
                    ->get();

                foreach ($inventoryItems as $inventoryItem) {
                    $combination = $inventoryItem->itemCustomFieldCombination;
                    $removeCombinationIds[] = $combination->id;
                    $inventoryItem->delete();
                    CalculateAvailableQTY::run($combination);
                }

                // Manage if combination has no any inventory then delete
                if (count($removeCombinationIds) > 0) {
                    foreach ($removeCombinationIds as $combinationId) {
                        $check = ItemCustomFieldCombinationInventory::where('item_custom_field_combination_id', $combinationId)->count();
                        if ($check == 0) {
                            ItemCustomFieldCombination::whereId($combinationId)->where('available_quantity', 0)->delete();
                        }
                    }
                }
            }

            foreach ($items as $key => $item) {
                /* First check if item has inventory custom fields and user not added any inventory for this item then throw error */
                $iteminventoryCustomFields = ItemCustomFieldSetting::where('item_id', $item['item_id'])->count();

                if ($iteminventoryCustomFields > 0) {
                    if (! (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0)) {
                        response()->json(['message' => 'Please Add inventory for this item.'], 422)->send();
                        exit();
                    }
                }

                /* Prepare item data */
                $purchaseOrderTransactionItemData = $this->prepareItemData($item, $purchaseOrderTransaction);

                /* Update or create item */
                if (! isset($item['id']) || $item['id'] == null) {
                    $purchaseOrderTransactionItem = PurchaseOrderItemInvoice::create($purchaseOrderTransactionItemData);
                } else {
                    $purchaseOrderTransactionItem = PurchaseOrderItemInvoice::whereId($item['id'])->first();
                    if (! empty($purchaseOrderTransactionItem)) {
                        $purchaseOrderTransactionItem->update($purchaseOrderTransactionItemData);
                    }
                }

                /* Update Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    UpdateCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::PURCHASE_ORDER,
                        $purchaseOrderTransactionItem->id,
                        PurchaseOrderItemInvoice::class
                    );
                } else {
                    $purchaseOrderTransactionItem->customFieldTransactionItemsValues()->delete();
                }

                /* Update Custom Fields for Item Inventory */
                if (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0) {
                    $inventoryData = $item['custom_field_inventory'];
                    $purchaseRate = $item['rpu_without_gst'] ?? null;
                    $purchaseDate = $purchaseOrderTransaction['order_date'] ? Carbon::parse($purchaseOrderTransaction['order_date'])->format('d-m-Y') : null;

                    foreach ($inventoryData as &$inventoryGroup) {
                        foreach ($inventoryGroup as &$inventoryItem) {
                            $inventoryItem['purchase_rate'] = $purchaseRate;
                            $inventoryItem['purchase_date'] = $purchaseDate;
                        }
                    }
                    unset($inventoryGroup, $inventoryItem);

                    $sortedInventory = collect($inventoryData)->map(function ($group) {
                        return collect($group)->sortBy('custom_field_id')->values();
                    })->toArray();

                    UpdateItemCFInventoryAction::run(
                        $sortedInventory,
                        $item['item_id'],
                        $purchaseOrderTransactionItem->id,
                        PurchaseOrderItemInvoice::class
                    );
                } else {
                    // If Inventory not sended then delete existing inventory & restore qty & delete combination if not used
                    $removeCombinationIds = [];
                    $inventoryItems = $purchaseOrderTransactionItem->customFieldTransactionItemsInventoryValues;

                    foreach ($inventoryItems as $inventoryItem) {
                        $combination = $inventoryItem->itemCustomFieldCombination;
                        $removeCombinationIds[] = $combination->id;

                        $inventoryItem->delete();
                        CalculateAvailableQTY::run($combination);
                    }

                    if (count($removeCombinationIds) > 0) {
                        foreach ($removeCombinationIds as $combinationId) {
                            $check = ItemCustomFieldCombinationInventory::where('item_custom_field_combination_id', $combinationId)->count();
                            if ($check == 0) {
                                ItemCustomFieldCombination::whereId($combinationId)->where('available_quantity', 0)->delete();
                            }
                        }
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareItemData($item, $purchaseOrderTransaction)
    {
        $cessRate = 0;
        $cessAmount = 0;
        if ($this->isGSTEnabled) {
            $itemMaster = ItemMaster::with(['model.gstGstCessRate'])->whereId($item['item_id'])->first();
            $cessRate = $itemMaster->model->gstGstCessRate->rate ?? 0;
            $taxableValue = ($item['quantity'] * $item['rpu_without_gst']) - $item['total_discount_amount'];
            $cessAmount = ($taxableValue * $cessRate) / 100;
        }

        return [
            'transactions_id' => $purchaseOrderTransaction->id,
            'item_id' => $item['item_id'],
            'ledger_id' => $item['ledger_id'],
            'additional_description' => $item['additional_description'] ?? null,
            'unit_id' => $item['unit_id'],
            'hsn_code' => $item['hsn_code'] ?? null,
            'quantity' => $item['quantity'],
            'mrp' => $item['mrp'] ?? null,
            'with_tax' => $item['with_tax'] ?? false,
            'rpu_with_gst' => $item['rpu_with_gst'],
            'rpu_without_gst' => $item['rpu_without_gst'],
            'discount_type' => $item['discount_type'] ?? null,
            'discount_value' => $item['discount_value'] ?? null,
            'discount_type_2' => $item['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $item['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $item['total_discount_amount'] ?? 0,
            'gst_id' => $item['gst_tax'],
            'gst_tax_percentage' => $item['gst_tax_percentage'],
            'total' => $item['total'],
            'classification_nature_type' => $this->classificationNatureType,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_igst_tax' => $this->isGSTEnabled ? $item['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $item['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $item['classification_sgst_tax'] : null,
            'classification_cess_tax' => $this->isGSTEnabled && isset($item['cess']) ? $item['cess'] : null,
            'consolidating_items_to_invoice' => $item['consolidating_items_to_invoice'] ?? false,
            'cess_rate' => $cessRate,
            'cess_amount' => $cessAmount,
            'decimal_places_for_quantity' => $item['decimal_places'] ?? 2,
            'decimal_places_for_rate' => $item['decimal_places_for_rate'] ?? 2,
        ];
    }

    private function storeLedgers($ledgers, $purchaseOrderTransaction)
    {
        try {
            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $purchaseOrderTransactionLedgerData = $this->prepareLedgerData($ledger, $purchaseOrderTransaction);

                /* Store ledger */
                $purchaseOrderTransactionLedger = PurchaseOrderAccountingInvoice::create($purchaseOrderTransactionLedgerData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateLedgers($ledgers, $purchaseOrderTransaction)
    {
        try {
            $ledgerIds = PurchaseOrderAccountingInvoice::whereTransactionsId($purchaseOrderTransaction->id)->pluck('id')->toArray();

            $editLedgerIds = Arr::pluck($ledgers, 'id') ?? [];
            $removeLedgerIds = array_diff($ledgerIds, $editLedgerIds);

            /* Delete removed ledgers */
            PurchaseOrderAccountingInvoice::whereIn('id', array_values($removeLedgerIds))?->delete();

            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $purchaseOrderTransactionLedgerData = $this->prepareLedgerData($ledger, $purchaseOrderTransaction);

                /* Update or create ledger */
                if (! isset($ledger['id']) || $ledger['id'] == null) {
                    $purchaseOrderTransactionLedger = PurchaseOrderAccountingInvoice::create($purchaseOrderTransactionLedgerData);
                } else {
                    $purchaseOrderTransactionLedger = PurchaseOrderAccountingInvoice::whereId($ledger['id'])->first();
                    if (! empty($purchaseOrderTransactionLedger)) {
                        $purchaseOrderTransactionLedger->update($purchaseOrderTransactionLedgerData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareLedgerData($ledger, $purchaseOrderTransaction)
    {
        return [
            'transactions_id' => $purchaseOrderTransaction->id,
            'ledger_id' => $ledger['ledger_id'],
            'additional_description' => $ledger['additional_description'] ?? null,
            'with_tax' => $ledger['with_tax'] ?? false,
            'rpu_with_gst' => $ledger['rpu_with_gst'],
            'rpu_without_gst' => $ledger['rpu_without_gst'],
            'discount_type' => $ledger['discount_type'] ?? null,
            'discount_value' => $ledger['discount_value'] ?? null,
            'discount_type_2' => $ledger['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $ledger['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $ledger['total_discount_amount'] ?? 0,
            'gst_id' => $ledger['gst_tax'],
            'gst_tax_percentage' => $ledger['gst_tax_percentage'],
            'total' => $ledger['total'],
            'classification_igst_tax' => $this->isGSTEnabled ? $ledger['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $ledger['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $ledger['classification_sgst_tax'] : null,
            'classification_cess_tax' => $this->isGSTEnabled && isset($ledger['cess']) ? $ledger['cess'] : null,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_nature_type' => $this->classificationNatureType,
        ];
    }

    private function storeAddresses($purchaseOrderTransaction, $input)
    {
        try {
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            $addresses = [];

            if ($billingAddress) {
                $addresses[] = $this->prepareAddressData($purchaseOrderTransaction, $billingAddress, PurchaseOrderTransaction::BILLING_ADDRESS);
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $addresses[] = $this->prepareAddressData($purchaseOrderTransaction, $shippingAddress, PurchaseOrderTransaction::SHIPPING_ADDRESS);
                }
            }

            if (! empty($addresses)) {
                $purchaseOrderTransaction->addresses()->createMany($addresses);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddresses($purchaseOrderTransaction, $input)
    {
        try {
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            if ($billingAddress) {
                $purchaseOrderTransaction->addresses()->updateOrCreate(
                    ['address_type' => PurchaseOrderTransaction::BILLING_ADDRESS, 'model_id' => $purchaseOrderTransaction->id],
                    $this->prepareAddressData($purchaseOrderTransaction, $billingAddress, PurchaseOrderTransaction::BILLING_ADDRESS)
                );
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $purchaseOrderTransaction->addresses()->updateOrCreate(
                        ['address_type' => PurchaseOrderTransaction::SHIPPING_ADDRESS, 'model_id' => $purchaseOrderTransaction->id],
                        $this->prepareAddressData($purchaseOrderTransaction, $shippingAddress, PurchaseOrderTransaction::SHIPPING_ADDRESS)
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddressData($purchaseOrderTransaction, $address, $addressType)
    {
        return [
            'address_1' => $address['address_1'] ?? null,
            'address_2' => $address['address_2'] ?? null,
            'country_id' => $address['country_id'] ?? null,
            'state_id' => $address['state_id'] ?? null,
            'city_id' => $address['city_id'] ?? null,
            'pin_code' => $address['pin_code'] ?? null,
            'model_id' => $purchaseOrderTransaction->id,
            'model_type' => PurchaseOrderTransaction::class,
            'address_type' => $addressType,
        ];
    }

    private function storeAdditionalCharges($additionChargesData, $purchaseOrderTransactionId)
    {
        try {
            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $purchaseOrderTransactionId);

                /* Store additional charge */
                AdditionalChargesForPurchaseOrderTransaction::create($additionChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAdditionalCharges($additionChargesData, $purchaseOrderTransactionId)
    {
        try {
            $additionalChargesIds = AdditionalChargesForPurchaseOrderTransaction::where('purchase_order_id', $purchaseOrderTransactionId)->pluck('id')->toArray();
            $editedAdditionalChargesIds = Arr::pluck($additionChargesData, 'id');
            $removeAdditionalChargesIds = array_diff($additionalChargesIds, $editedAdditionalChargesIds);

            /* Delete additional charges */
            AdditionalChargesForPurchaseOrderTransaction::whereIn('id', array_values($removeAdditionalChargesIds))?->delete();

            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $purchaseOrderTransactionId);

                /* Update additional charge */
                if (! isset($additionCharge['id']) || $additionCharge['id'] == null) {
                    AdditionalChargesForPurchaseOrderTransaction::create($additionChargeData);
                } else {
                    $additionalChargeRecord = AdditionalChargesForPurchaseOrderTransaction::where('id', $additionCharge['id'])->first();
                    if (! empty($additionalChargeRecord)) {
                        $additionalChargeRecord->update($additionChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAdditionalChargesData($additionCharge, $purchaseOrderTransactionId)
    {
        if ($this->isGSTEnabled) {
            $gst = null;
            if (isset($additionCharge['ac_gst_rate_id'])) {
                $gst = GstTax::whereId($additionCharge['ac_gst_rate_id'])->first();
            }
            $gstTaxPercentage = ! empty($gst) ? $gst->tax_rate : null;
        }

        return [
            'purchase_order_id' => $purchaseOrderTransactionId,
            'ledger_id' => $additionCharge['ac_ledger_id'],
            'charge_type' => $additionCharge['ac_type'],
            'value' => $additionCharge['ac_value'],
            'gst_rate_id' => $this->isGSTEnabled && ! empty($gst) ? $gst->id : null,
            'gst_percentage' => $this->isGSTEnabled ? $gstTaxPercentage : null,
            'total_without_tax' => $additionCharge['ac_total_without_tax'],
            'total' => $additionCharge['ac_total'],
        ];
    }

    private function storeAddLess($addLessData, $purchaseOrderTransactionId)
    {
        try {

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $purchaseOrderTransactionId);

                /* Store add less */
                AddLessForPurchaseOrderTransaction::create($addLessChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddLess($addLessData, $purchaseOrderTransactionId)
    {
        try {
            $addLessIds = AddLessForPurchaseOrderTransaction::where('purchase_order_id', $purchaseOrderTransactionId)->pluck('id')->toArray();
            $editedAddLessIds = Arr::pluck($addLessData, 'id');
            $removeAddLessIds = array_diff($addLessIds, $editedAddLessIds);

            /* Delete add less */
            AddLessForPurchaseOrderTransaction::whereIn('id', array_values($removeAddLessIds))?->delete();

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $purchaseOrderTransactionId);

                /* Update add less */
                if (! isset($addLessCharge['id']) || $addLessCharge['id'] == null) {
                    AddLessForPurchaseOrderTransaction::create($addLessChargeData);
                } else {
                    $addLessRecord = AddLessForPurchaseOrderTransaction::where('id', $addLessCharge['id'])->first();
                    if (! empty($addLessRecord)) {
                        $addLessRecord->update($addLessChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddLessData($addLessCharge, $purchaseOrderTransactionId)
    {
        return [
            'purchase_order_id' => $purchaseOrderTransactionId,
            'ledger_id' => $addLessCharge['al_ledger_id'],
            'is_show_in_print' => $addLessCharge['al_is_show_in_print'] ?? false,
            'type' => $addLessCharge['al_type'],
            'value' => $addLessCharge['al_value'],
            'total' => $addLessCharge['al_total'],
        ];
    }
}
