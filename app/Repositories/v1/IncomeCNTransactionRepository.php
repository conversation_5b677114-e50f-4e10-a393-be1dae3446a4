<?php

namespace App\Repositories\v1;

use App\Actions\CommonAction\AdvancePaymentAction;
use App\Actions\CommonAction\CheckPartyLedgerMobileNumberAction;
use App\Actions\CommonAction\DirectCustomerSupplierCreate;
use App\Actions\CustomFields\StoreTransactionCustomFieldsAction;
use App\Actions\CustomFields\UpdateTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Actions\CustomFieldsItemMaster\Inventory\StoreItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Inventory\UpdateItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\StoreCFTransactionWiseAction;
use App\Actions\CustomFieldsItemMaster\Transaction\UpdateCFTransactionWiseAction;
use App\Models\AdditionalChargesForIncomeCreditNoteTransaction;
use App\Models\AddLessForIncomeCreditNoteTransaction;
use App\Models\GstTax;
use App\Models\IncomeCreditNoteItemTransaction;
use App\Models\IncomeCreditNoteLedgerTransaction;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldCombination;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldSetting;
use App\Models\Ledger;
use App\Models\Master\ItemMaster;
use App\Models\PaymentDetailsForIncomeCreditNoteTransaction;
use App\Models\PaymentTransaction;
use App\Models\PaymentTransactionItem;
use App\Models\SettleAdvancePayment;
use App\Models\TaxClassificationDetails;
use App\Models\TransactionCustomField;
use App\Repositories\API\v1\BaseAPIRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Interface IncomeCNTransactionRepository.
 */
class IncomeCNTransactionRepository extends BaseAPIRepository
{
    public $classificationNatureType = null;

    public $isRCMApplicable = false;

    public $isGSTEnabled = null;

    public $oldIncomeCNTransaction = null;

    public function model()
    {
        return IncomeCreditNoteTransaction::class;
    }

    public function store($input)
    {
        try {
            DB::beginTransaction();

            unset($input['_token']);
            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['customer_ledger_id'], Ledger::CUSTOMER);
                $input['customer_ledger_id'] = $partyId;
            }

            /* Ledger mobile number */
            if (isset($input['party_phone_number'])) {
                $ledgerData = [
                    'ledger_id' => $input['customer_ledger_id'],
                    'phone_1' => $input['party_phone_number'],
                    'region_iso_1' => $input['region_iso'],
                    'region_code_1' => $input['region_code'],
                ];
                CheckPartyLedgerMobileNumberAction::run($ledgerData);
            }

            /* Prepare Income Credit Note transaction */
            $incomeCNTransactionData = $this->prepareIncomeCreditNoteData($input);

            /* Create Income Credit Note */
            $incomeCNTransaction = IncomeCreditNoteTransaction::create($incomeCNTransactionData);

            /* Store Income Credit Note Document */
            if (isset($input['income_credit_note_document']) && ! empty($input['income_credit_note_document'])) {
                foreach ($input['income_credit_note_document'] as $image) {
                    $incomeCNTransaction->addMedia($image)->toMediaCollection(IncomeCreditNoteTransaction::INCOME_CREDIT_NOTE_DOCUMENT, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Store Estimate Quote items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['cn_item_type'] == IncomeCreditNoteTransaction::ITEM_INVOICE) {
                $this->storeItems($input['items'], $incomeCNTransaction);
            }
            if ($input['cn_item_type'] == IncomeCreditNoteTransaction::ACCOUNTING_INVOICE) {
                $this->storeLedgers($input['ledgers'], $incomeCNTransaction);
            }

            /* Store Addresses */
            $this->storeAddresses($incomeCNTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($incomeCNTransaction->shipping_address_id) && ! empty($incomeCNTransaction->shippingAddress)) {
                $incomeCNTransaction->update([
                    'shipping_address_id' => $incomeCNTransaction->shippingAddress->id,
                ]);
            }

            /* Store Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->storeAdditionalCharges($input['additional_charges'], $incomeCNTransaction->id);
            }

            /* Store Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->storeAddLess($input['add_less'], $incomeCNTransaction->id);
            }

            /* Store Payment Details And Create Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->storePaymentDetails($input['payment_details'], $incomeCNTransaction);
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                AdvancePaymentAction::run($input['advance_payment'], $incomeCNTransaction);
            }

            /* Store Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                StoreTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::INCOME_CREDIT_NOTE,
                    $incomeCNTransaction->id,
                    IncomeCreditNoteTransaction::class
                );
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($incomeCNTransaction);

            DB::commit();

            return $incomeCNTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    public function update($input, $incomeCNTransaction)
    {
        try {
            DB::beginTransaction();

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* Old Income Credit Note transaction */
            $this->oldIncomeCNTransaction = clone $incomeCNTransaction;

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['customer_ledger_id'], Ledger::CUSTOMER);
                $input['customer_ledger_id'] = $partyId;
            }

            /* Ledger mobile number */
            if (isset($input['party_phone_number'])) {
                $ledgerData = [
                    'ledger_id' => $input['customer_ledger_id'],
                    'phone_1' => $input['party_phone_number'],
                    'region_iso_1' => $input['region_iso'],
                    'region_code_1' => $input['region_code'],
                ];
                CheckPartyLedgerMobileNumberAction::run($ledgerData);
            }

            /* Prepare Income Credit Note transaction */
            $input['created_at'] = $this->oldIncomeCNTransaction->created_at;
            $incomeCNTransactionData = $this->prepareIncomeCreditNoteData($input);
            unset($incomeCNTransactionData['credit_note_number']);

            /* Update Income Credit Note transaction */
            /** @var IncomeCreditNoteTransaction $incomeCNTransaction */
            $incomeCNTransaction->update($incomeCNTransactionData);

            /* Update Income Credit Note Document */
            if (isset($input['income_credit_note_document']) && ! empty($input['income_credit_note_document'])) {
                foreach ($input['income_credit_note_document'] as $image) {
                    $incomeCNTransaction->addMedia($image)->toMediaCollection(IncomeCreditNoteTransaction::INCOME_CREDIT_NOTE_DOCUMENT, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Update Income Credit Note items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['cn_item_type'] == IncomeCreditNoteTransaction::ITEM_INVOICE && $this->oldIncomeCNTransaction->cn_item_type == $input['cn_item_type']) {
                $this->updateItems($input['items'], $incomeCNTransaction);
            } elseif ($input['cn_item_type'] == IncomeCreditNoteTransaction::ACCOUNTING_INVOICE && $this->oldIncomeCNTransaction->cn_item_type == $input['cn_item_type']) {
                $this->updateLedgers($input['ledgers'], $incomeCNTransaction);
            } elseif ($input['cn_item_type'] == IncomeCreditNoteTransaction::ITEM_INVOICE && $this->oldIncomeCNTransaction->cn_item_type != $input['cn_item_type']) {
                $incomeCNTransaction->incomeCreditNoteLedgers()->delete();
                $this->storeItems($input['items'], $incomeCNTransaction);
            } elseif ($input['cn_item_type'] == IncomeCreditNoteTransaction::ACCOUNTING_INVOICE && $this->oldIncomeCNTransaction->cn_item_type != $input['cn_item_type']) {
                $incomeCNTransaction->incomeCreditNoteItems()->delete();
                $this->storeLedgers($input['ledgers'], $incomeCNTransaction);
            }

            /* Update Addresses */
            $this->updateAddresses($incomeCNTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($incomeCNTransaction->shipping_address_id) && ! empty($incomeCNTransaction->shippingAddress)) {
                $incomeCNTransaction->update([
                    'shipping_address_id' => $incomeCNTransaction->shippingAddress->id,
                ]);
            }

            /* Update or Create Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->updateAdditionalCharges($input['additional_charges'], $incomeCNTransaction->id);
            } else {
                $incomeCNTransaction->additionalCharges()->delete();
            }

            /* Update or Create Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->updateAddLess($input['add_less'], $incomeCNTransaction->id);
            } else {
                $incomeCNTransaction->addLess()->delete();
            }

            /* Update or Create Payment Details And Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->updatePaymentDetails($input['payment_details'], $incomeCNTransaction);
            } else {
                $ledgerIdsForRemovePayments = $incomeCNTransaction->paymentDetails->pluck('ledger_id')->toArray();

                PaymentTransaction::where('payment_voucher_number', 'income-credit-note/'.$this->oldIncomeCNTransaction->full_invoice_number)
                    ->where('ledger_id', $this->oldIncomeCNTransaction->customer_ledger_id)
                    ->whereIn('bank_cash_ledger_id', $ledgerIdsForRemovePayments)
                    ->financialYearDate()?->forceDelete();

                $incomeCNTransaction->paymentDetails()->delete();
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                (new AdvancePaymentAction)->updateAdvancePayment($input['advance_payment'], $incomeCNTransaction);
            } else {
                SettleAdvancePayment::whereModelId($incomeCNTransaction->id)->whereModelType(get_class($incomeCNTransaction))->delete();
            }

            /* Update Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                UpdateTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::INCOME_CREDIT_NOTE,
                    $incomeCNTransaction->id,
                    IncomeCreditNoteTransaction::class
                );
            } else {
                $incomeCNTransaction->customFieldValues()->delete();
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($incomeCNTransaction);

            DB::commit();

            return $incomeCNTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    private function prepareIncomeCreditNoteData($input): array
    {
        /* Destructure input arrays for clarity */
        $otherDetails = $input['other_details'] ?? [];
        $brokerDetails = $input['broker_details'] ?? [];
        $transportDetails = $input['transport_details'] ?? [];
        $ewayBillDetails = $input['eway_bill_details'] ?? [];
        $tcsDetails = $input['tcs_details'] ?? [];
        $tdsDetails = $input['tds_details'] ?? [];

        /* calculate credit period date */
        $creditPeriod = $otherDetails['credit_period'] ?? null;
        $creditPeriodType = $otherDetails['credit_period_type'] ?? null;
        $invoiceDate = Carbon::parse($input['date']);
        $creditPeriodDueDate = $creditPeriod != null && $creditPeriodType != null ? calculateCreditPeriodDate($creditPeriod, $creditPeriodType, $invoiceDate) : null;

        $shippingName = $input['shipping_address']['shipping_name'] ?? $input['shipping_name'] ?? null;
        $shippingGSTIN = $input['shipping_address']['shipping_gstin'] ?? $input['shipping_gstin'] ?? null;
        $addressName = $input['shipping_address']['address_name'] ?? $input['address_name'] ?? null;
        $partyNameSameAsAddressName = $input['shipping_address']['party_name_same_as_address_name'] ?? $input['party_name_same_as_address_name'] ?? false;
        if ($partyNameSameAsAddressName) {
            $addressName = $shippingName;
        }

        return [
            'company_id' => $input['company']->id,
            'payment_mode' => null, // Removed field
            'payment_type_ledger_id' => null, // Removed field
            'full_invoice_number' => $input['full_invoice_number'],
            'credit_note_number' => $input['invoice_number'],
            'date' => Carbon::parse($input['date']),
            'customer_ledger_id' => $input['customer_ledger_id'],
            'party_phone_number' => $input['party_phone_number'] ?? null,
            'region_iso' => $input['region_iso'] ?? null,
            'region_code' => $input['region_code'] ?? null,
            'gstin' => $this->isGSTEnabled && isset($input['gstin']) ? $input['gstin'] : null,
            'original_inv_no' => $input['original_inv_no'] ?? null,
            'original_inv_date' => $input['original_inv_date'] ?? null,
            'broker_id' => $brokerDetails['broker_id'] ?? null,
            'brokerage_for_sale' => $brokerDetails['brokerage_for_sale'] ?? null,
            'brokerage_on_value_type' => $brokerDetails['brokerage_on_value_type'] ?? null,
            'credit_period' => $creditPeriod,
            'credit_period_type' => $creditPeriodType,
            'credit_period_due_date' => $creditPeriodDueDate,
            'transport_id' => $transportDetails['transport_id'] ?? null,
            'transporter_document_number' => $transportDetails['transporter_document_number'] ?? null,
            'transporter_document_date' => isset($transportDetails['transporter_document_date']) ? Carbon::parse($transportDetails['transporter_document_date']) : null,
            'transporter_vehicle_number' => $transportDetails['transporter_vehicle_number'] ?? null,
            'shipping_name' => $shippingName,
            'shipping_gstin' => $shippingGSTIN,
            'address_name' => $addressName,
            'party_name_same_as_address_name' => $partyNameSameAsAddressName,
            'po_no' => $otherDetails['po_no'] ?? null,
            'po_date' => isset($otherDetails['po_date']) ? Carbon::parse($otherDetails['po_date']) : null,
            'cn_item_type' => $input['cn_item_type'],
            'tcs_tax_id' => $tcsDetails['tcs_tax_id'] ?? null,
            'tcs_rate' => $tcsDetails['tcs_rate'] ?? null,
            'tcs_amount' => $tcsDetails['tcs_amount'] ?? null,
            'tds_tax_id' => $tdsDetails['tds_tax_id'] ?? null,
            'tds_rate' => $tdsDetails['tds_rate'] ?? null,
            'tds_amount' => $tdsDetails['tds_amount'] ?? null,
            'cgst' => $this->isGSTEnabled ? $input['cgst'] : 0,
            'sgst' => $this->isGSTEnabled ? $input['sgst'] : 0,
            'igst' => $this->isGSTEnabled ? $input['igst'] : 0,
            'rounding_amount' => $input['rounding_amount'] ?? null,
            'total' => $input['grand_total'],
            'grand_total' => $input['grand_total'],
            'taxable_value' => $input['taxable_value'],
            'gross_value' => $input['gross_value'],
            'narration' => $input['narration'] ?? null,
            'term_and_condition' => $input['term_and_condition'] ?? null,
            'dispatch_address_id' => $input['dispatch_address_id'] ?? null,
            'shipping_address_id' => $input['shipping_address_id'] ?? null,
            'same_as_billing' => $input['same_as_billing'] ?? false,
            'is_gst_enabled' => $this->isGSTEnabled,
            'cess' => $this->isGSTEnabled ? $input['cess'] : 0,
            'billing_state_id' => $input['billing_address']['state_id'] ?? null,
            'is_cgst_sgst_igst_calculated' => $input['is_cgst_sgst_igst_calculated'],
            'is_gst_na' => $input['is_gst_na'],
            'created_by' => getLoginUser()->id,
            'via_api' => $input['via_api'] ?? false,
            'is_import' => $input['is_import'] ?? false,
            'is_round_off_not_changed' => $input['is_round_off_not_changed'] ?? true,
            'created_at' => $input['created_at'] ?? Carbon::now(), // this is for import excel
            'updated_at' => $input['updated_at'] ?? Carbon::now(), // this is for import excel
            'round_off_method' => $input['round_off_method'],
            'bank_id' => $input['bank_id'] ?? null,
        ];
    }

    private function storeItems($items, $incomeCNTransaction)
    {
        try {
            foreach ($items as $key => $item) {
                /* First check if item has inventory custom fields and user not added any inventory for this item then throw error */
                $iteminventoryCustomFields = ItemCustomFieldSetting::where('item_id', $item['item_id'])->count();

                if ($iteminventoryCustomFields > 0) {
                    if (! (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0)) {
                        response()->json(['message' => 'Please Add inventory for this item.'], 422)->send();
                        exit();
                    }
                }

                /* Prepare item data */
                $incomeCNTransactionItemData = $this->prepareItemData($item, $incomeCNTransaction);

                /* Store item */
                $incomeCNTransactionItem = IncomeCreditNoteItemTransaction::create($incomeCNTransactionItemData);

                /* Store Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    StoreCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::INCOME_CREDIT_NOTE,
                        $incomeCNTransactionItem->id,
                        IncomeCreditNoteItemTransaction::class
                    );
                }

                /* Store Custom Fields for Item Inventory */
                if (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0) {
                    $inventoryData = $item['custom_field_inventory'];
                    $purchaseRate = $item['rpu_without_gst'];
                    $purchaseDate = $incomeCNTransaction['date'] ? Carbon::parse($incomeCNTransaction['date'])->format('d-m-Y') : null;

                    foreach ($inventoryData as &$inventoryGroup) {
                        foreach ($inventoryGroup as &$inventoryItem) {
                            $inventoryItem['purchase_rate'] = $purchaseRate;
                            $inventoryItem['purchase_date'] = $purchaseDate;
                        }
                    }
                    unset($inventoryGroup, $inventoryItem);

                    $sortedInventory = collect($inventoryData)->map(function ($group) {
                        return collect($group)->sortBy('custom_field_id')->values();
                    })->toArray();

                    StoreItemCFInventoryAction::run(
                        $sortedInventory,
                        $item['item_id'],
                        $incomeCNTransactionItem->id,
                        IncomeCreditNoteItemTransaction::class
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateItems($items, $incomeCNTransaction)
    {
        try {
            /* Get item ids to be removed */
            $itemIds = IncomeCreditNoteItemTransaction::where('income_cn_id', $incomeCNTransaction->id)->pluck('id')->toArray();
            $editItemIds = Arr::pluck($items, 'id') ?? [];
            $removeItemIds = array_diff($itemIds, $editItemIds);

            /* Delete removed items */
            if (count($removeItemIds) > 0) {
                IncomeCreditNoteItemTransaction::whereIn('id', array_values($removeItemIds))?->delete();

                $removeCombinationIds = [];
                $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', array_values($removeItemIds))
                    ->where('model_type', IncomeCreditNoteItemTransaction::class)
                    ->get();

                foreach ($inventoryItems as $inventoryItem) {
                    $combination = $inventoryItem->itemCustomFieldCombination;
                    $removeCombinationIds[] = $combination->id;
                    $inventoryItem->delete();
                    CalculateAvailableQTY::run($combination);
                }

                // Manage if combination has no any inventory then delete
                if (count($removeCombinationIds) > 0) {
                    foreach ($removeCombinationIds as $combinationId) {
                        $check = ItemCustomFieldCombinationInventory::where('item_custom_field_combination_id', $combinationId)->count();
                        if ($check == 0) {
                            ItemCustomFieldCombination::whereId($combinationId)->where('available_quantity', 0)->delete();
                        }
                    }
                }
            }

            foreach ($items as $key => $item) {
                /* First check if item has inventory custom fields and user not added any inventory for this item then throw error */
                $iteminventoryCustomFields = ItemCustomFieldSetting::where('item_id', $item['item_id'])->count();

                if ($iteminventoryCustomFields > 0) {
                    if (! (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0)) {
                        response()->json(['message' => 'Please Add inventory for this item.'], 422)->send();
                        exit();
                    }
                }

                /* Prepare item data */
                $incomeCNTransactionItemData = $this->prepareItemData($item, $incomeCNTransaction);

                /* Update or create item */
                if (! isset($item['id']) || $item['id'] == null) {
                    $incomeCNTransactionItem = IncomeCreditNoteItemTransaction::create($incomeCNTransactionItemData);
                } else {
                    $incomeCNTransactionItem = IncomeCreditNoteItemTransaction::whereId($item['id'])->first();
                    if (! empty($incomeCNTransactionItem)) {
                        $incomeCNTransactionItem->update($incomeCNTransactionItemData);
                    }
                }

                /* Update Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    UpdateCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::INCOME_CREDIT_NOTE,
                        $incomeCNTransactionItem->id,
                        IncomeCreditNoteItemTransaction::class
                    );
                } else {
                    $incomeCNTransactionItem->customFieldTransactionItemsValues()->delete();
                }

                /* Update Custom Fields for Item Inventory */
                if (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0) {
                    $inventoryData = $item['custom_field_inventory'];
                    $purchaseRate = $item['rpu_without_gst'];
                    $purchaseDate = $incomeCNTransaction['date'] ? Carbon::parse($incomeCNTransaction['date'])->format('d-m-Y') : null;

                    foreach ($inventoryData as &$inventoryGroup) {
                        foreach ($inventoryGroup as &$inventoryItem) {
                            $inventoryItem['purchase_rate'] = $purchaseRate;
                            $inventoryItem['purchase_date'] = $purchaseDate;
                        }
                    }
                    unset($inventoryGroup, $inventoryItem);

                    $sortedInventory = collect($inventoryData)->map(function ($group) {
                        return collect($group)->sortBy('custom_field_id')->values();
                    })->toArray();

                    UpdateItemCFInventoryAction::run(
                        $sortedInventory,
                        $item['item_id'],
                        $incomeCNTransactionItem->id,
                        IncomeCreditNoteItemTransaction::class
                    );
                } else {
                    // If Inventory not sended then delete existing inventory & restore qty & delete combination if not used
                    $removeCombinationIds = [];
                    $inventoryItems = $incomeCNTransactionItem->customFieldTransactionItemsInventoryValues;

                    foreach ($inventoryItems as $inventoryItem) {
                        $combination = $inventoryItem->itemCustomFieldCombination;
                        $removeCombinationIds[] = $combination->id;

                        $inventoryItem->delete();
                        CalculateAvailableQTY::run($combination);
                    }

                    if (count($removeCombinationIds) > 0) {
                        foreach ($removeCombinationIds as $combinationId) {
                            $check = ItemCustomFieldCombinationInventory::where('item_custom_field_combination_id', $combinationId)->count();
                            if ($check == 0) {
                                ItemCustomFieldCombination::whereId($combinationId)->where('available_quantity', 0)->delete();
                            }
                        }
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareItemData($item, $incomeCNTransaction)
    {
        $cessRate = 0;
        $cessAmount = 0;
        if ($this->isGSTEnabled) {
            $itemMaster = ItemMaster::with(['model.gstGstCessRate'])->whereId($item['item_id'])->first();
            $cessRate = $itemMaster->model->gstGstCessRate->rate ?? 0;
            $taxableValue = ($item['quantity'] * $item['rpu_without_gst']) - $item['total_discount_amount'];
            $cessAmount = ($taxableValue * $cessRate) / 100;
        }

        return [
            'income_cn_id' => $incomeCNTransaction->id,
            'item_id' => $item['item_id'],
            'additional_description' => $item['additional_description'] ?? null,
            'ledger_id' => $item['ledger_id'],
            'unit_id' => $item['unit_id'],
            'hsn_code' => $item['hsn_code'] ?? null,
            'quantity' => $item['quantity'],
            'mrp' => $item['mrp'] ?? null,
            'with_tax' => $item['with_tax'] ?? false,
            'rpu_with_gst' => $item['rpu_with_gst'],
            'rpu_without_gst' => $item['rpu_without_gst'],
            'discount_type' => $item['discount_type'] ?? null,
            'discount_value' => $item['discount_value'] ?? null,
            'discount_type_2' => $item['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $item['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $item['total_discount_amount'] ?? 0,
            'gst_id' => $item['gst_tax'],
            'gst_tax_percentage' => $item['gst_tax_percentage'],
            'total' => $item['total'],
            'classification_nature_type' => $this->classificationNatureType,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_igst_tax' => $this->isGSTEnabled ? $item['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $item['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $item['classification_sgst_tax'] : null,
            'classification_cess_tax' => $this->isGSTEnabled && isset($item['cess']) ? $item['cess'] : null,
            'cess_rate' => $cessRate,
            'cess_amount' => $cessAmount,
            'decimal_places_for_quantity' => $item['decimal_places'] ?? 2,
            'decimal_places_for_rate' => $item['decimal_places_for_rate'] ?? 2,
        ];
    }

    private function storeLedgers($ledgers, $incomeCNTransaction)
    {
        try {
            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $incomeCNTransactionLedgerData = $this->prepareLedgerData($ledger, $incomeCNTransaction);

                /* Store ledger */
                $incomeCNTransactionLedger = IncomeCreditNoteLedgerTransaction::create($incomeCNTransactionLedgerData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateLedgers($ledgers, $incomeCNTransaction)
    {
        try {
            $ledgerIds = IncomeCreditNoteLedgerTransaction::where('income_cn_id', $incomeCNTransaction->id)->pluck('id')->toArray();
            $editLedgerIds = Arr::pluck($ledgers, 'id') ?? [];
            $removeLedgerIds = array_diff($ledgerIds, $editLedgerIds);

            /* Delete removed ledgers */
            IncomeCreditNoteLedgerTransaction::whereIn('id', array_values($removeLedgerIds))?->delete();

            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $incomeCNTransactionLedgerData = $this->prepareLedgerData($ledger, $incomeCNTransaction);

                /* Update or create ledger */
                if (! isset($ledger['id']) || $ledger['id'] == null) {
                    $incomeCNTransactionLedger = IncomeCreditNoteLedgerTransaction::create($incomeCNTransactionLedgerData);
                } else {
                    $incomeCNTransactionLedger = IncomeCreditNoteLedgerTransaction::whereId($ledger['id'])->first();
                    if (! empty($incomeCNTransactionLedger)) {
                        $incomeCNTransactionLedger->update($incomeCNTransactionLedgerData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareLedgerData($ledger, $incomeCNTransaction)
    {
        return [
            'income_cn_id' => $incomeCNTransaction->id,
            'ledger_id' => $ledger['ledger_id'],
            'additional_description' => $ledger['additional_description'] ?? null,
            'with_tax' => $ledger['with_tax'] ?? false,
            'rpu_with_gst' => $ledger['rpu_with_gst'],
            'rpu_without_gst' => $ledger['rpu_without_gst'],
            'discount_type' => $ledger['discount_type'] ?? null,
            'discount_value' => $ledger['discount_value'] ?? null,
            'discount_type_2' => $ledger['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $ledger['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $ledger['total_discount_amount'] ?? 0,
            'gst_id' => $ledger['gst_tax'],
            'gst_tax_percentage' => $ledger['gst_tax_percentage'],
            'total' => $ledger['total'],
            'classification_igst_tax' => $this->isGSTEnabled ? $ledger['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $ledger['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $ledger['classification_sgst_tax'] : null,
            // 'classification_cess_tax' => $this->isGSTEnabled ? $ledger['cess'] : null,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_nature_type' => $this->classificationNatureType,
        ];
    }

    private function storeAddresses($incomeCNTransaction, $input)
    {
        try {
            $dispatchAddress = $input['dispatch_address'] ?? [];
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            $addresses = [];

            if ($dispatchAddress) {
                $addresses[] = $this->prepareAddressData($incomeCNTransaction, $dispatchAddress, IncomeCreditNoteTransaction::DISPATCH_ADDRESS);
            }

            if ($billingAddress) {
                $addresses[] = $this->prepareAddressData($incomeCNTransaction, $billingAddress, IncomeCreditNoteTransaction::BILLING_ADDRESS);
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $addresses[] = $this->prepareAddressData($incomeCNTransaction, $shippingAddress, IncomeCreditNoteTransaction::SHIPPING_ADDRESS);
                }
            }

            if (! empty($addresses)) {
                $incomeCNTransaction->addresses()->createMany($addresses);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddresses($incomeCNTransaction, $input)
    {
        try {
            $dispatchAddress = $input['dispatch_address'] ?? [];
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            if ($dispatchAddress) {
                $incomeCNTransaction->addresses()->updateOrCreate(
                    ['address_type' => IncomeCreditNoteTransaction::DISPATCH_ADDRESS, 'model_id' => $incomeCNTransaction->id],
                    $this->prepareAddressData($incomeCNTransaction, $dispatchAddress, IncomeCreditNoteTransaction::DISPATCH_ADDRESS)
                );
            }

            if ($billingAddress) {
                $incomeCNTransaction->addresses()->updateOrCreate(
                    ['address_type' => IncomeCreditNoteTransaction::BILLING_ADDRESS, 'model_id' => $incomeCNTransaction->id],
                    $this->prepareAddressData($incomeCNTransaction, $billingAddress, IncomeCreditNoteTransaction::BILLING_ADDRESS)
                );
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $incomeCNTransaction->addresses()->updateOrCreate(
                        ['address_type' => IncomeCreditNoteTransaction::SHIPPING_ADDRESS, 'model_id' => $incomeCNTransaction->id],
                        $this->prepareAddressData($incomeCNTransaction, $shippingAddress, IncomeCreditNoteTransaction::SHIPPING_ADDRESS)
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddressData($incomeCNTransaction, $address, $addressType)
    {
        return [
            'address_1' => $address['address_1'] ?? null,
            'address_2' => $address['address_2'] ?? null,
            'country_id' => $address['country_id'] ?? null,
            'state_id' => $address['state_id'] ?? null,
            'city_id' => $address['city_id'] ?? null,
            'pin_code' => $address['pin_code'] ?? null,
            'model_id' => $incomeCNTransaction->id,
            'model_type' => IncomeCreditNoteTransaction::class,
            'address_type' => $addressType,
        ];
    }

    private function storeAdditionalCharges($additionChargesData, $incomeCNTransactionId)
    {
        try {
            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $incomeCNTransactionId);

                /* Store additional charge */
                AdditionalChargesForIncomeCreditNoteTransaction::create($additionChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAdditionalCharges($additionChargesData, $incomeCNTransactionId)
    {
        try {
            $additionalChargesIds = AdditionalChargesForIncomeCreditNoteTransaction::where('income_cn_id', $incomeCNTransactionId)->pluck('id')->toArray();
            $editedAdditionalChargesIds = Arr::pluck($additionChargesData, 'id');
            $removeAdditionalChargesIds = array_diff($additionalChargesIds, $editedAdditionalChargesIds);

            /* Delete additional charges */
            AdditionalChargesForIncomeCreditNoteTransaction::whereIn('id', array_values($removeAdditionalChargesIds))?->delete();

            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $incomeCNTransactionId);

                /* Update additional charge */
                if (! isset($additionCharge['id']) || $additionCharge['id'] == null) {
                    AdditionalChargesForIncomeCreditNoteTransaction::create($additionChargeData);
                } else {
                    $additionalChargeRecord = AdditionalChargesForIncomeCreditNoteTransaction::where('id', $additionCharge['id'])->first();
                    if (! empty($additionalChargeRecord)) {
                        $additionalChargeRecord->update($additionChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAdditionalChargesData($additionCharge, $incomeCNTransactionId)
    {
        if ($this->isGSTEnabled) {
            $gst = null;
            if (isset($additionCharge['ac_gst_rate_id'])) {
                $gst = GstTax::whereId($additionCharge['ac_gst_rate_id'])->first();
            }
            $gstTaxPercentage = ! empty($gst) ? $gst->tax_rate : null;
        }

        return [
            'income_cn_id' => $incomeCNTransactionId,
            'ledger_id' => $additionCharge['ac_ledger_id'],
            'charge_type' => $additionCharge['ac_type'],
            'value' => $additionCharge['ac_value'],
            'gst_rate_id' => $this->isGSTEnabled && ! empty($gst) ? $gst->id : null,
            'gst_percentage' => $this->isGSTEnabled ? $gstTaxPercentage : null,
            'total_without_tax' => $additionCharge['ac_total_without_tax'],
            'total' => $additionCharge['ac_total'],
        ];
    }

    private function storeAddLess($addLessData, $incomeCNTransactionId)
    {
        try {

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $incomeCNTransactionId);

                /* Store add less */
                AddLessForIncomeCreditNoteTransaction::create($addLessChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddLess($addLessData, $incomeCNTransactionId)
    {
        try {
            $addLessIds = AddLessForIncomeCreditNoteTransaction::where('income_cn_id', $incomeCNTransactionId)->pluck('id')->toArray();
            $editedAddLessIds = Arr::pluck($addLessData, 'id');
            $removeAddLessIds = array_diff($addLessIds, $editedAddLessIds);

            /* Delete add less */
            AddLessForIncomeCreditNoteTransaction::whereIn('id', array_values($removeAddLessIds))?->delete();

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $incomeCNTransactionId);

                /* Update add less */
                if (! isset($addLessCharge['id']) || $addLessCharge['id'] == null) {
                    AddLessForIncomeCreditNoteTransaction::create($addLessChargeData);
                } else {
                    $addLessRecord = AddLessForIncomeCreditNoteTransaction::where('id', $addLessCharge['id'])->first();
                    if (! empty($addLessRecord)) {
                        $addLessRecord->update($addLessChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddLessData($addLessCharge, $incomeCNTransactionId)
    {
        return [
            'income_cn_id' => $incomeCNTransactionId,
            'ledger_id' => $addLessCharge['al_ledger_id'],
            'is_show_in_print' => $addLessCharge['al_is_show_in_print'] ?? false,
            'type' => $addLessCharge['al_type'],
            'value' => $addLessCharge['al_value'],
            'total' => $addLessCharge['al_total'],
        ];
    }

    private function storePaymentDetails($paymentDetails, $incomeCNTransaction)
    {
        try {
            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment detail data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $incomeCNTransaction);

                /* Store payment detail */
                $paymentDetailRecord = PaymentDetailsForIncomeCreditNoteTransaction::create($paymentDetailData);

                $receiptTransaction = PaymentTransaction::create([
                    'company_id' => $incomeCNTransaction->company_id,
                    'date' => $paymentDetailData['date'],
                    'payment_voucher_number' => 'income-credit-note/'.$incomeCNTransaction->full_invoice_number,
                    'is_default_created_by_transaction' => true,
                    'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                    'ledger_id' => $incomeCNTransaction->customer_ledger_id,
                    'total_paid_amount' => $paymentDetailData['amount'],
                    'narration' => $incomeCNTransaction->narration ?? null,
                    'payment_mode' => $paymentDetailData['mode'],
                    'reference_number' => $paymentDetailData['reference_no'],
                    'created_by' => $incomeCNTransaction->created_by,
                ]);

                PaymentTransactionItem::create([
                    'pc_transaction_id' => $receiptTransaction->id,
                    'income_credit_note_id' => $incomeCNTransaction->id,
                    'invoice_number' => $incomeCNTransaction->full_invoice_number,
                    'bill_type' => PaymentTransaction::INCOME_CREDIT_NOTE_TYPE,
                    'paid_amount' => $paymentDetailData['amount'],
                    'discount' => 0,
                    'round_off' => 0,
                ]);

                $paymentDetailRecord->update([
                    'payment_id' => $receiptTransaction->id,
                ]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updatePaymentDetails($paymentDetails, $incomeCNTransaction)
    {
        try {
            $paymentDetailsIds = PaymentDetailsForIncomeCreditNoteTransaction::where('income_cn_id', $incomeCNTransaction->id)->pluck('id')->toArray();
            $editedPaymentDetailsIds = Arr::pluck($paymentDetails, 'pd_id');
            $removePaymentDetailsIds = array_diff($paymentDetailsIds, $editedPaymentDetailsIds);

            /* Delete payment details */
            foreach ($removePaymentDetailsIds as $removePaymentDetailsId) {
                $removePaymentDetails = PaymentDetailsForIncomeCreditNoteTransaction::where('id', $removePaymentDetailsId)->first();

                $receiptTransaction = PaymentTransaction::where('payment_voucher_number', 'income-credit-note/'.$this->oldIncomeCNTransaction->full_invoice_number)
                    ->where('ledger_id', $this->oldIncomeCNTransaction->customer_ledger_id)
                    ->where('bank_cash_ledger_id', $removePaymentDetails->ledger_id)
                    ->whereId($removePaymentDetails->payment_id)
                    ->financialYearDate()?->forceDelete();

                $removePaymentDetails->delete();
            }

            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment details data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $incomeCNTransaction);

                /* Update payment details */
                if (! isset($paymentDetail['pd_id']) || $paymentDetail['pd_id'] == null) {
                    $paymentDetailRecord = PaymentDetailsForIncomeCreditNoteTransaction::create($paymentDetailData);

                    $paymentTransaction = PaymentTransaction::create([
                        'company_id' => $incomeCNTransaction->company_id,
                        'date' => $paymentDetailData['date'],
                        'payment_voucher_number' => 'income-credit-note/'.$incomeCNTransaction->full_invoice_number,
                        'is_default_created_by_transaction' => true,
                        'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                        'ledger_id' => $incomeCNTransaction->customer_ledger_id,
                        'total_paid_amount' => $paymentDetailData['amount'],
                        'narration' => $incomeCNTransaction->narration ?? null,
                        'payment_mode' => $paymentDetailData['mode'],
                        'reference_number' => $paymentDetailData['reference_no'],
                        'created_by' => $incomeCNTransaction->created_by,
                    ]);

                    PaymentTransactionItem::create([
                        'pc_transaction_id' => $paymentTransaction->id,
                        'income_credit_note_id' => $incomeCNTransaction->id,
                        'invoice_number' => $incomeCNTransaction->full_invoice_number,
                        'bill_type' => PaymentTransaction::INCOME_CREDIT_NOTE_TYPE,
                        'paid_amount' => $paymentDetailData['amount'],
                        'discount' => 0,
                        'round_off' => 0,
                    ]);
                } else {
                    $paymentDetailRecord = PaymentDetailsForIncomeCreditNoteTransaction::where('id', $paymentDetail['pd_id'])->first();

                    if (! empty($paymentDetailRecord)) {
                        $paymentDetailRecord->update($paymentDetailData);
                    }

                    $paymentTransaction = PaymentTransaction::where('payment_voucher_number', 'income-credit-note/'.$this->oldIncomeCNTransaction->full_invoice_number)
                        ->where('ledger_id', $this->oldIncomeCNTransaction->customer_ledger_id)
                        ->whereId($paymentDetailRecord->payment_id)
                        ->first();

                    if (! empty($paymentTransaction)) {
                        $paymentTransaction->update([
                            'payment_voucher_number' => 'income-credit-note/'.$incomeCNTransaction->full_invoice_number,
                            'date' => $paymentDetailData['date'],
                            'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                            'ledger_id' => $incomeCNTransaction->customer_ledger_id,
                            'total_paid_amount' => $paymentDetailData['amount'],
                            'narration' => $incomeCNTransaction->narration ?? null,
                            'payment_mode' => $paymentDetailData['mode'],
                            'reference_number' => $paymentDetailData['reference_no'],
                        ]);

                        $paymentTransactionItem = PaymentTransactionItem::where('pc_transaction_id', $paymentTransaction->id)->first();
                        if (! empty($paymentTransactionItem)) {
                            $paymentTransactionItem->update([
                                'invoice_number' => $incomeCNTransaction->full_invoice_number,
                                'paid_amount' => $paymentDetailData['amount'],
                            ]);
                        }
                    }
                }

                $paymentDetailRecord->update([
                    'payment_id' => $paymentTransaction->id,
                ]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function preparePaymentDetailData($paymentDetail, $incomeCNTransaction)
    {
        return [
            'income_cn_id' => $incomeCNTransaction->id,
            'ledger_id' => $paymentDetail['pd_ledger_id'],
            'date' => Carbon::parse($paymentDetail['pd_date']),
            'amount' => $paymentDetail['pd_amount'],
            'mode' => $paymentDetail['pd_mode'] ?? null,
            'reference_no' => $paymentDetail['pd_reference_number'] ?? null,
        ];
    }
}
