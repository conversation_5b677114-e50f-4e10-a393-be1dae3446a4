<?php

namespace App\Repositories\v1;

use App\Actions\CommonAction\AdvancePaymentAction;
use App\Actions\CommonAction\DirectCustomerSupplierCreate;
use App\Actions\CustomFields\StoreTransactionCustomFieldsAction;
use App\Actions\CustomFields\UpdateTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Actions\CustomFieldsItemMaster\Inventory\StoreItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Inventory\UpdateItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\StoreCFTransactionWiseAction;
use App\Actions\CustomFieldsItemMaster\Transaction\UpdateCFTransactionWiseAction;
use App\Models\AdditionalChargesForExpenseDebitNoteTransaction;
use App\Models\AddLessForExpenseDebitNoteTransaction;
use App\Models\ExpenseDebitNoteItemTransaction;
use App\Models\ExpenseDebitNoteLedgerTransaction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\GstTax;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldSetting;
use App\Models\Ledger;
use App\Models\Master\ItemMaster;
use App\Models\PaymentDetailsForExpenseDebitNoteTransaction;
use App\Models\ReceiptTransaction;
use App\Models\ReceiptTransactionItem;
use App\Models\SettleAdvancePayment;
use App\Models\TaxClassificationDetails;
use App\Models\TransactionCustomField;
use App\Repositories\API\v1\BaseAPIRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Interface ExpenseDebitNoteTransactionRepository.
 */
class ExpenseDebitNoteTransactionRepository extends BaseAPIRepository
{
    public $classificationNatureType = null;

    public $isRCMApplicable = false;

    public $isGSTEnabled = null;

    public $oldExpenseDebitNoteTransaction = null;

    public function model()
    {
        return ExpenseDebitNoteTransaction::class;
    }

    public function store($input)
    {
        try {
            DB::beginTransaction();
            unset($input['_token']);
            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['supplier_id'], Ledger::SUPPLIER);
                $input['supplier_id'] = $partyId;
            }

            /* Prepare Expense Debit Note transaction */
            $expenseDebitNoteTransactionData = $this->prepareExpenseDebitNoteData($input);

            /* Create Expense Debit Note transaction */
            $expenseDebitNoteTransaction = ExpenseDebitNoteTransaction::create($expenseDebitNoteTransactionData);

            /* Store Expense Debit Note Document */
            if (isset($input['expense_debit_note_document']) && ! empty($input['expense_debit_note_document'])) {
                foreach ($input['expense_debit_note_document'] as $image) {
                    $expenseDebitNoteTransaction->addMedia($image)->toMediaCollection(ExpenseDebitNoteTransaction::EXPENSE_DEBIT_NOTE_DOCUMENT, config('app.media_disc'));
                }
            }

            /* GST details for Expense Debit Note */
            if ($this->isGSTEnabled) {
                /* Store Estimate Quote items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            /* Store items or ledgers based on invoice type */
            if ($input['dn_item_type'] == ExpenseDebitNoteTransaction::ITEM_INVOICE) {
                $this->storeItems($input['items'], $expenseDebitNoteTransaction);
            }

            if ($input['dn_item_type'] == ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE) {
                $this->storeLedgers($input['ledgers'], $expenseDebitNoteTransaction);
            }

            /* Store Addresses */
            $this->storeAddresses($expenseDebitNoteTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($expenseDebitNoteTransaction->shipping_address_id) && ! empty($expenseDebitNoteTransaction->dispatchAddress)) {
                $expenseDebitNoteTransaction->update([
                    'shipping_address_id' => $expenseDebitNoteTransaction->dispatchAddress->id, // in expense transaction dispatch address is shipping address
                ]);
            }

            /* Store Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->storeAdditionalCharges($input['additional_charges'], $expenseDebitNoteTransaction->id);
            }

            /* Store Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->storeAddLess($input['add_less'], $expenseDebitNoteTransaction->id);
            }

            /* Store Payment Details And Create Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->storePaymentDetails($input['payment_details'], $expenseDebitNoteTransaction);
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                AdvancePaymentAction::run($input['advance_payment'], $expenseDebitNoteTransaction);
            }

            /* Store Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                StoreTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::EXPENSE_DEBIT_NOTE,
                    $expenseDebitNoteTransaction->id,
                    ExpenseDebitNoteTransaction::class
                );
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($expenseDebitNoteTransaction);

            DB::commit();

            return $expenseDebitNoteTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    public function update($input, $expenseDebitNoteTransaction)
    {
        try {
            DB::beginTransaction();

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* Old expense debit note transaction */
            $this->oldExpenseDebitNoteTransaction = clone $expenseDebitNoteTransaction;

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['supplier_id'], Ledger::SUPPLIER);
                $input['supplier_id'] = $partyId;
            }

            /* Prepare expense debit note transaction */
            $input['created_at'] = $this->oldExpenseDebitNoteTransaction->created_at;
            $expenseDebitNoteTransactionData = $this->prepareExpenseDebitNoteData($input);

            /* Update expense debit note transaction */
            /** @var ExpenseDebitNoteTransaction $expenseDebitNoteTransaction */
            $expenseDebitNoteTransaction->update($expenseDebitNoteTransactionData);

            /* Update Expense Debit Note Document */
            if (isset($input['expense_debit_note_document']) && ! empty($input['expense_debit_note_document'])) {
                foreach ($input['expense_debit_note_document'] as $image) {
                    $expenseDebitNoteTransaction->addMedia($image)->toMediaCollection(ExpenseDebitNoteTransaction::EXPENSE_DEBIT_NOTE_DOCUMENT, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Update Expense Debit Note items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['dn_item_type'] == ExpenseDebitNoteTransaction::ITEM_INVOICE && $this->oldExpenseDebitNoteTransaction->dn_item_type == $input['dn_item_type']) {
                $this->updateItems($input['items'], $expenseDebitNoteTransaction);
            } elseif ($input['dn_item_type'] == ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE && $this->oldExpenseDebitNoteTransaction->dn_item_type == $input['dn_item_type']) {
                $this->updateLedgers($input['ledgers'], $expenseDebitNoteTransaction);
            } elseif ($input['dn_item_type'] == ExpenseDebitNoteTransaction::ITEM_INVOICE && $this->oldExpenseDebitNoteTransaction->dn_item_type != $input['dn_item_type']) {
                $expenseDebitNoteTransaction->debitNoteLedgers()->delete();
                $this->storeItems($input['items'], $expenseDebitNoteTransaction);
            } elseif ($input['dn_item_type'] == ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE && $this->oldExpenseDebitNoteTransaction->dn_item_type != $input['dn_item_type']) {
                $expenseDebitNoteTransaction->debitNoteItems()->delete();
                $this->storeLedgers($input['ledgers'], $expenseDebitNoteTransaction);
            }

            /* Update Addresses */
            $this->updateAddresses($expenseDebitNoteTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($expenseDebitNoteTransaction->shipping_address_id) && ! empty($expenseDebitNoteTransaction->dispatchAddress)) {
                $expenseDebitNoteTransaction->update([
                    'shipping_address_id' => $expenseDebitNoteTransaction->dispatchAddress->id, // in expense transaction dispatch address is shipping address
                ]);
            }

            /* Update or Create Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->updateAdditionalCharges($input['additional_charges'], $expenseDebitNoteTransaction->id);
            } else {
                $expenseDebitNoteTransaction->additionalCharges()->delete();
            }

            /* Update or Create Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->updateAddLess($input['add_less'], $expenseDebitNoteTransaction->id);
            } else {
                $expenseDebitNoteTransaction->addLess()->delete();
            }

            /* Update or Create Payment Details And Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->updatePaymentDetails($input['payment_details'], $expenseDebitNoteTransaction);
            } else {
                $ledgerIdsForRemoveReceipts = $expenseDebitNoteTransaction->paymentDetails->pluck('ledger_id')->toArray();

                ReceiptTransaction::where('receipt_number', 'expense-debit-note/'.$this->oldExpenseDebitNoteTransaction->voucher_number)
                    ->where('ledger_id', $this->oldExpenseDebitNoteTransaction->supplier_id)
                    ->whereIn('bank_cash_ledger_id', $ledgerIdsForRemoveReceipts)
                    ->financialYearDate()?->forceDelete();

                $expenseDebitNoteTransaction->paymentDetails()->delete();
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                (new AdvancePaymentAction)->updateAdvancePayment($input['advance_payment'], $expenseDebitNoteTransaction);
            } else {
                SettleAdvancePayment::whereModelId($expenseDebitNoteTransaction->id)->whereModelType(get_class($expenseDebitNoteTransaction))->delete();
            }

            /* Update Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                UpdateTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::EXPENSE_DEBIT_NOTE,
                    $expenseDebitNoteTransaction->id,
                    ExpenseDebitNoteTransaction::class
                );
            } else {
                $expenseDebitNoteTransaction->customFieldValues()->delete();
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($expenseDebitNoteTransaction);

            DB::commit();

            return $expenseDebitNoteTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    private function prepareExpenseDebitNoteData($input): array
    {
        /* Destructure input arrays for clarity */
        $brokerDetails = $input['broker_details'] ?? [];
        $transportDetails = $input['transport_details'] ?? [];
        $tcsDetails = $input['tcs_details'] ?? [];
        $tdsDetails = $input['tds_details'] ?? [];

        $shippingName = $input['shipping_address']['shipping_name'] ?? $input['shipping_name'] ?? null;
        $shippingGSTIN = $input['shipping_address']['shipping_gstin'] ?? $input['shipping_gstin'] ?? null;
        $addressName = $input['shipping_address']['address_name'] ?? $input['address_name'] ?? null;
        $partyNameSameAsAddressName = $input['shipping_address']['party_name_same_as_address_name'] ?? $input['party_name_same_as_address_name'] ?? false;
        if ($partyNameSameAsAddressName) {
            $addressName = $shippingName;
        }

        return [
            'company_id' => $input['company']->id,
            'payment_mode' => null, // Removed field
            'payment_type_ledger_id' => null, // Removed field
            'voucher_number' => $input['voucher_number'],
            'voucher_date' => Carbon::parse($input['voucher_date']),
            'original_inv_no' => $input['original_inv_no'] ?? null,
            'original_inv_date' => $input['original_inv_date'] ?? null,
            'supplier_purchase_return_number' => $input['supplier_purchase_return_number'] ?? null,
            'supplier_purchase_return_date' => isset($input['supplier_purchase_return_date']) ? Carbon::parse($input['supplier_purchase_return_date']) : null,
            'supplier_id' => $input['supplier_id'],
            'gstin' => $this->isGSTEnabled && isset($input['gstin']) ? $input['gstin'] : null,
            'broker_id' => $brokerDetails['broker_id'] ?? null,
            'brokerage_for_sale' => $brokerDetails['brokerage_for_sale'] ?? null,
            'brokerage_on_value_type' => $brokerDetails['brokerage_on_value_type'] ?? null,
            'transport_id' => $transportDetails['transport_id'] ?? null,
            'transporter_document_number' => $transportDetails['transporter_document_number'] ?? null,
            'transporter_document_date' => isset($transportDetails['transporter_document_date']) ? Carbon::parse($transportDetails['transporter_document_date']) : null,
            'transporter_vehicle_number' => $transportDetails['transporter_vehicle_number'] ?? null,
            'shipping_name' => $shippingName,
            'shipping_gstin' => $shippingGSTIN,
            'address_name' => $addressName,
            'party_name_same_as_address_name' => $partyNameSameAsAddressName,
            'dn_item_type' => $input['dn_item_type'],
            'tcs_tax_id' => $tcsDetails['tcs_tax_id'] ?? null,
            'tcs_rate' => $tcsDetails['tcs_rate'] ?? null,
            'tcs_amount' => $tcsDetails['tcs_amount'] ?? null,
            'ledger_of_tds' => $tdsDetails['tds_tax_id'] ?? null,
            'tds_rate' => $tdsDetails['tds_rate'] ?? null,
            'tds_amount' => $tdsDetails['tds_amount'] ?? null,
            'cgst' => $this->isGSTEnabled ? $input['cgst'] : 0,
            'sgst' => $this->isGSTEnabled ? $input['sgst'] : 0,
            'igst' => $this->isGSTEnabled ? $input['igst'] : 0,
            'rounding_amount' => $input['rounding_amount'],
            'total' => $input['grand_total'],
            'taxable_value' => $input['taxable_value'],
            'gross_value' => $input['gross_value'],
            'grand_total' => $input['grand_total'],
            'narration' => $input['narration'] ?? null,
            'shipping_address_id' => $input['shipping_address_id'] ?? null,
            'same_as_billing' => $input['same_as_billing'] ?? false,
            'is_gst_enabled' => $input['is_gst_enabled'],
            'cess' => $input['cess'] ?? 0,
            'is_cgst_sgst_igst_calculated' => $input['is_cgst_sgst_igst_calculated'],
            'is_gst_na' => $input['is_gst_na'],
            'is_rcm_applicable' => $input['is_rcm_applicable'] ?? false,
            'created_by' => getLoginUser()->id,
            'via_api' => $input['via_api'] ?? false,
            'is_import' => $input['is_import'] ?? false,
            'is_round_off_not_changed' => $input['is_round_off_not_changed'] ?? true,
            'created_at' => $input['created_at'] ?? Carbon::now(), // this is for import excel
            'updated_at' => $input['updated_at'] ?? Carbon::now(), // this is for import excel
            'round_off_method' => $input['round_off_method'],
        ];
    }

    private function storeItems($items, $expenseDebitNoteTransaction)
    {
        try {
            foreach ($items as $key => $item) {
                /* First check if item inventory has custom fields and user not selected any inventory for this item then throw error */
                $iteminventoryCustomFields = ItemCustomFieldSetting::where('item_id', $item['item_id'])->count();

                if ($iteminventoryCustomFields > 0) {
                    if (! (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0)) {
                        response()->json(['message' => 'Please select inventory for this item or otherwise inward inventory.'], 422)->send();
                        exit();
                    }
                }

                /* Prepare item data */
                $expenseDebitNoteTransactionItemData = $this->prepareItemData($item, $expenseDebitNoteTransaction);

                /* Store item */
                $expenseDebitNoteTransactionItem = ExpenseDebitNoteItemTransaction::create($expenseDebitNoteTransactionItemData);

                /* Store Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    StoreCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::EXPENSE_DEBIT_NOTE,
                        $expenseDebitNoteTransactionItem->id,
                        ExpenseDebitNoteItemTransaction::class
                    );
                }

                /* Store Custom Fields for Item Inventory */
                if (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0) {
                    $sortedInventory = collect($item['custom_field_inventory'])->map(function ($group) {
                        return collect($group)->sortBy('custom_field_id')->values();
                    })->toArray();

                    StoreItemCFInventoryAction::run(
                        $sortedInventory,
                        $item['item_id'],
                        $expenseDebitNoteTransactionItem->id,
                        ExpenseDebitNoteItemTransaction::class
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateItems($items, $expenseDebitNoteTransaction)
    {
        try {
            /* Get item ids to be removed */
            $itemIds = ExpenseDebitNoteItemTransaction::whereDebitNoteId($expenseDebitNoteTransaction->id)->pluck('id')->toArray();
            $editDebitNoteIds = Arr::pluck($items, 'id') ?? [];
            $removeItemIds = array_diff($itemIds, $editDebitNoteIds);

            /* Delete removed items */
            if (count($removeItemIds) > 0) {
                ExpenseDebitNoteItemTransaction::whereIn('id', array_values($removeItemIds))?->delete();

                // if remove item from transaction then update remaining quantity in combination
                $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', array_values($removeItemIds))
                    ->where('model_type', ExpenseDebitNoteItemTransaction::class)
                    ->get();

                foreach ($inventoryItems as $inventoryItem) {
                    $combination = $inventoryItem->itemCustomFieldCombination;
                    $inventoryItem->delete();
                    CalculateAvailableQTY::run($combination);
                }
            }

            foreach ($items as $key => $item) {
                /* First check if item inventory has custom fields and user not selected any inventory for this item then throw error */
                $iteminventoryCustomFields = ItemCustomFieldSetting::where('item_id', $item['item_id'])->count();

                if ($iteminventoryCustomFields > 0) {
                    if (! (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0)) {
                        response()->json(['message' => 'Please select inventory for this item or otherwise inward inventory.'], 422)->send();
                        exit();
                    }
                }

                /* Prepare item data */
                $expenseDebitNoteTransactionItemData = $this->prepareItemData($item, $expenseDebitNoteTransaction);

                /* Update or create item */
                if (! isset($item['id']) || $item['id'] == null) {
                    $existsDebitNoteItem = ExpenseDebitNoteItemTransaction::create($expenseDebitNoteTransactionItemData);
                } else {
                    $existsDebitNoteItem = ExpenseDebitNoteItemTransaction::whereId($item['id'])->first();
                    if (! empty($existsDebitNoteItem)) {
                        $existsDebitNoteItem->update($expenseDebitNoteTransactionItemData);
                    }
                }

                /* Update Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    UpdateCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::EXPENSE_DEBIT_NOTE,
                        $existsDebitNoteItem->id,
                        ExpenseDebitNoteItemTransaction::class
                    );
                } else {
                    $existsDebitNoteItem->customFieldTransactionItemsValues()->delete();
                }

                /* Update Custom Fields for Item Inventory */
                if (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0) {
                    $sortedInventory = collect($item['custom_field_inventory'])->map(function ($group) {
                        return collect($group)->sortBy('custom_field_id')->values();
                    })->toArray();

                    UpdateItemCFInventoryAction::run(
                        $sortedInventory,
                        $item['item_id'],
                        $existsDebitNoteItem->id,
                        ExpenseDebitNoteItemTransaction::class
                    );
                } else {
                    // if custom field inventory is removed then update remaining quantity in combination row batch as well
                    $inventoryItems = $existsDebitNoteItem->customFieldTransactionItemsInventoryValues;

                    foreach ($inventoryItems as $inventoryItem) {
                        $combination = $inventoryItem->itemCustomFieldCombination;
                        $inventoryItem->delete();
                        CalculateAvailableQTY::run($combination);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareItemData($item, $expenseDebitNoteTransaction)
    {
        $cessRate = 0;
        $cessAmount = 0;
        if ($this->isGSTEnabled) {
            $itemMaster = ItemMaster::with(['model.gstGstCessRate'])->whereId($item['item_id'])->first();
            $cessRate = $itemMaster->model->gstGstCessRate->rate ?? 0;
            $taxableValue = ($item['quantity'] * $item['rpu_without_gst']) - $item['total_discount_amount'];
            $cessAmount = ($taxableValue * $cessRate) / 100;
        }

        return [
            'debit_note_id' => $expenseDebitNoteTransaction->id,
            'item_id' => $item['item_id'],
            'additional_description' => $item['additional_description'] ?? null,
            'ledger_id' => $item['ledger_id'],
            'unit_id' => $item['unit_id'],
            'hsn_code' => $item['hsn_code'] ?? null,
            'quantity' => $item['quantity'],
            'mrp' => $item['mrp'] ?? null,
            'with_tax' => $item['with_tax'] ?? false,
            'rpu_with_gst' => $item['rpu_with_gst'],
            'rpu_without_gst' => $item['rpu_without_gst'],
            'discount_type' => $item['discount_type'] ?? null,
            'discount_value' => $item['discount_value'] ?? null,
            'discount_type_2' => $item['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $item['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $item['total_discount_amount'] ?? 0,
            'gst_id' => $item['gst_tax'],
            'gst_tax_percentage' => $item['gst_tax_percentage'],
            'total' => $item['total'],
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_nature_type' => $this->classificationNatureType,
            'classification_is_itc_applicable' => $item['classification_is_itc_applicable'] ?? true,
            'classification_igst_tax' => $this->isGSTEnabled ? $item['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $item['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $item['classification_sgst_tax'] : null,
            'classification_cess_tax' => $this->isGSTEnabled && isset($item['cess']) ? $item['cess'] : null,
            'consolidating_items_to_invoice' => $item['consolidating_items_to_invoice'] ?? false,
            'cess_rate' => $cessRate,
            'cess_amount' => $cessAmount,
            'taxable_amount' => $item['taxable_amount'] ?? 0,
            'decimal_places_for_quantity' => $item['decimal_places'] ?? 2,
            'decimal_places_for_rate' => $item['decimal_places_for_rate'] ?? 2,
        ];
    }

    private function storeLedgers($ledgers, $expenseDebitNoteTransaction)
    {
        try {
            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $expenseDebitNoteTransactionLedgerData = $this->prepareLedgerData($ledger, $expenseDebitNoteTransaction);

                /* Store ledger */
                $expenseDebitNoteTransactionLedger = ExpenseDebitNoteLedgerTransaction::create($expenseDebitNoteTransactionLedgerData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateLedgers($ledgers, $expenseDebitNoteTransaction)
    {
        try {
            $ledgerIds = ExpenseDebitNoteLedgerTransaction::whereDebitNoteId($expenseDebitNoteTransaction->id)->pluck('id')->toArray();
            $editDebitNoteLedgerIds = Arr::pluck($ledgers, 'id') ?? [];
            $removeLedgerIds = array_diff($ledgerIds, $editDebitNoteLedgerIds);

            /* Delete removed ledgers */
            ExpenseDebitNoteLedgerTransaction::whereIn('id', array_values($removeLedgerIds))->delete();

            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $expenseDebitNoteTransactionLedgerData = $this->prepareLedgerData($ledger, $expenseDebitNoteTransaction);

                /* Update or create ledger */
                if (! isset($ledger['id']) || $ledger['id'] == null) {
                    $existsDebitNoteLedger = ExpenseDebitNoteLedgerTransaction::create($expenseDebitNoteTransactionLedgerData);
                } else {
                    $existsDebitNoteLedger = ExpenseDebitNoteLedgerTransaction::whereId($ledger['id'])->first();
                    if (! empty($existsDebitNoteLedger)) {
                        $existsDebitNoteLedger->update($expenseDebitNoteTransactionLedgerData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareLedgerData($ledger, $expenseDebitNoteTransaction)
    {
        return [
            'debit_note_id' => $expenseDebitNoteTransaction->id,
            'ledger_id' => $ledger['ledger_id'],
            'additional_description' => $ledger['additional_description'] ?? null,
            'with_tax' => $ledger['with_tax'] ?? false,
            'rpu_with_gst' => $ledger['rpu_with_gst'],
            'rpu_without_gst' => $ledger['rpu_without_gst'],
            'discount_type' => $ledger['discount_type'] ?? null,
            'discount_value' => $ledger['discount_value'] ?? null,
            'discount_type_2' => $ledger['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $ledger['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $ledger['total_discount_amount'] ?? 0,
            'gst_id' => $ledger['gst_tax'],
            'gst_tax_percentage' => $ledger['gst_tax_percentage'],
            'total' => $ledger['total'],
            'classification_is_itc_applicable' => $ledger['classification_is_itc_applicable'] ?? true,
            'classification_igst_tax' => $this->isGSTEnabled ? $ledger['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $ledger['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $ledger['classification_sgst_tax'] : null,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_nature_type' => $this->classificationNatureType,
            'taxable_amount' => $ledger['taxable_amount'] ?? 0,

        ];
    }

    private function storeAddresses($expenseDebitNoteTransaction, $input)
    {
        try {
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];
            $addresses = [];

            if ($billingAddress) {
                $addresses[] = $this->prepareAddressData($expenseDebitNoteTransaction, $billingAddress, ExpenseDebitNoteTransaction::BILLING_ADDRESS);
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $addresses[] = $this->prepareAddressData($expenseDebitNoteTransaction, $shippingAddress, ExpenseDebitNoteTransaction::SHIPPING_ADDRESS);
                }
            }

            if (! empty($addresses)) {
                $expenseDebitNoteTransaction->addresses()->createMany($addresses);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddresses($expenseDebitNoteTransaction, $input)
    {
        try {
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            if ($billingAddress) {
                $expenseDebitNoteTransaction->addresses()->updateOrCreate(
                    ['address_type' => ExpenseDebitNoteTransaction::BILLING_ADDRESS, 'model_id' => $expenseDebitNoteTransaction->id],
                    $this->prepareAddressData($expenseDebitNoteTransaction, $billingAddress, ExpenseDebitNoteTransaction::BILLING_ADDRESS)
                );
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $expenseDebitNoteTransaction->addresses()->updateOrCreate(
                        ['address_type' => ExpenseDebitNoteTransaction::SHIPPING_ADDRESS, 'model_id' => $expenseDebitNoteTransaction->id],
                        $this->prepareAddressData($expenseDebitNoteTransaction, $shippingAddress, ExpenseDebitNoteTransaction::SHIPPING_ADDRESS)
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddressData($expenseDebitNoteTransaction, $address, $addressType)
    {
        return [
            'address_1' => $address['address_1'] ?? null,
            'address_2' => $address['address_2'] ?? null,
            'country_id' => $address['country_id'] ?? null,
            'state_id' => $address['state_id'] ?? null,
            'city_id' => $address['city_id'] ?? null,
            'pin_code' => $address['pin_code'] ?? null,
            'model_id' => $expenseDebitNoteTransaction->id,
            'model_type' => ExpenseDebitNoteTransaction::class,
            'address_type' => $addressType,
        ];
    }

    private function storeAdditionalCharges($additionChargesData, $expenseDebitNoteTransactionId)
    {
        try {
            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $expenseDebitNoteTransactionId);

                /* Store additional charge */
                AdditionalChargesForExpenseDebitNoteTransaction::create($additionChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAdditionalCharges($additionChargesData, $expenseDebitNoteTransactionId)
    {
        try {
            $additionalChargesIds = AdditionalChargesForExpenseDebitNoteTransaction::where('expense_debit_note_id', $expenseDebitNoteTransactionId)->pluck('id')->toArray();
            $editedAdditionalChargesIds = Arr::pluck($additionChargesData, 'id');
            $removeAdditionalChargesIds = array_diff($additionalChargesIds, $editedAdditionalChargesIds);

            /* Delete additional charges */
            AdditionalChargesForExpenseDebitNoteTransaction::whereIn('id', array_values($removeAdditionalChargesIds))?->delete();

            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $expenseDebitNoteTransactionId);

                /* Update additional charge */
                if (! isset($additionCharge['id']) || $additionCharge['id'] == null) {
                    AdditionalChargesForExpenseDebitNoteTransaction::create($additionChargeData);
                } else {
                    $additionalChargeRecord = AdditionalChargesForExpenseDebitNoteTransaction::where('id', $additionCharge['id'])->first();
                    if (! empty($additionalChargeRecord)) {
                        $additionalChargeRecord->update($additionChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAdditionalChargesData($additionCharge, $expenseDebitNoteTransactionId)
    {
        if ($this->isGSTEnabled) {
            $gst = null;
            if (isset($additionCharge['ac_gst_rate_id'])) {
                $gst = GstTax::whereId($additionCharge['ac_gst_rate_id'])->first();
            }
            $gstTaxPercentage = ! empty($gst) ? $gst->tax_rate : null;
        }

        return [
            'expense_debit_note_id' => $expenseDebitNoteTransactionId,
            'ledger_id' => $additionCharge['ac_ledger_id'],
            'charge_type' => $additionCharge['ac_type'],
            'value' => $additionCharge['ac_value'],
            'gst_rate_id' => $this->isGSTEnabled && ! empty($gst) ? $gst->id : null,
            'gst_percentage' => $this->isGSTEnabled ? $gstTaxPercentage : null,
            'total_without_tax' => $additionCharge['ac_total_without_tax'],
            'total' => $additionCharge['ac_total'],
        ];
    }

    private function storeAddLess($addLessData, $expenseDebitNoteTransactionId)
    {
        try {

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $expenseDebitNoteTransactionId);

                /* Store add less */
                AddLessForExpenseDebitNoteTransaction::create($addLessChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddLess($addLessData, $expenseDebitNoteTransactionId)
    {
        try {
            $addLessIds = AddLessForExpenseDebitNoteTransaction::where('expense_debit_note_id', $expenseDebitNoteTransactionId)->pluck('id')->toArray();
            $editedAddLessIds = Arr::pluck($addLessData, 'id');
            $removeAddLessIds = array_diff($addLessIds, $editedAddLessIds);

            /* Delete add less */
            AddLessForExpenseDebitNoteTransaction::whereIn('id', array_values($removeAddLessIds))?->delete();

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $expenseDebitNoteTransactionId);

                /* Update add less */
                if (! isset($addLessCharge['id']) || $addLessCharge['id'] == null) {
                    AddLessForExpenseDebitNoteTransaction::create($addLessChargeData);
                } else {
                    $addLessRecord = AddLessForExpenseDebitNoteTransaction::where('id', $addLessCharge['id'])->first();
                    if (! empty($addLessRecord)) {
                        $addLessRecord->update($addLessChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddLessData($addLessCharge, $expenseDebitNoteTransactionId)
    {
        return [
            'expense_debit_note_id' => $expenseDebitNoteTransactionId,
            'ledger_id' => $addLessCharge['al_ledger_id'],
            'is_show_in_print' => $addLessCharge['al_is_show_in_print'] ?? false,
            'type' => $addLessCharge['al_type'],
            'value' => $addLessCharge['al_value'],
            'total' => $addLessCharge['al_total'],
        ];
    }

    private function storePaymentDetails($paymentDetails, $expenseDebitNoteTransaction)
    {
        try {
            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment detail data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $expenseDebitNoteTransaction);

                /* Store payment detail */
                $paymentDetailRecord = PaymentDetailsForExpenseDebitNoteTransaction::create($paymentDetailData);

                $receiptTransaction = ReceiptTransaction::create([
                    'company_id' => $expenseDebitNoteTransaction->company_id,
                    'date' => $paymentDetailData['date'],
                    'receipt_number' => 'expense-debit-note/'.$expenseDebitNoteTransaction->voucher_number,
                    'is_default_created_by_transaction' => true,
                    'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                    'ledger_id' => $expenseDebitNoteTransaction->supplier_id,
                    'total_received_amount' => $paymentDetailData['amount'],
                    'narration' => $expenseDebitNoteTransaction->narration ?? null,
                    'payment_mode' => $paymentDetailData['mode'],
                    'created_by' => $expenseDebitNoteTransaction->created_by,
                ]);

                ReceiptTransactionItem::create([
                    'rc_transaction_id' => $receiptTransaction->id,
                    'expense_debit_id' => $expenseDebitNoteTransaction->id,
                    'invoice_number' => $expenseDebitNoteTransaction->voucher_number,
                    'bill_type' => ReceiptTransaction::EXPENSE_DEBIT_NOTE_BILL_TYPE,
                    'received_amount' => $receiptTransaction->total_received_amount,
                    'discount' => 0,
                    'round_off' => 0,
                ]);

                $paymentDetailRecord->update([
                    'receipt_id' => $receiptTransaction->id,
                ]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updatePaymentDetails($paymentDetails, $expenseDebitNoteTransaction)
    {
        try {
            $paymentDetailsIds = PaymentDetailsForExpenseDebitNoteTransaction::where('expense_debit_note_id', $expenseDebitNoteTransaction->id)->pluck('id')->toArray();
            $editedPaymentDetailsIds = Arr::pluck($paymentDetails, 'pd_id');
            $removePaymentDetailsIds = array_diff($paymentDetailsIds, $editedPaymentDetailsIds);

            /* Delete payment details */
            foreach ($removePaymentDetailsIds as $removePaymentDetailsId) {
                $removePaymentDetails = PaymentDetailsForExpenseDebitNoteTransaction::where('id', $removePaymentDetailsId)->first();

                $receiptTransaction = ReceiptTransaction::where('receipt_number', 'expense-debit-note/'.$this->oldExpenseDebitNoteTransaction->voucher_number)
                    ->where('ledger_id', $this->oldExpenseDebitNoteTransaction->supplier_id)
                    ->where('bank_cash_ledger_id', $removePaymentDetails->ledger_id)
                    ->whereId($removePaymentDetails->receipt_id)
                    ->financialYearDate()?->forceDelete();

                $removePaymentDetails->delete();
            }

            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment details data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $expenseDebitNoteTransaction);

                /* Update payment details */
                if (! isset($paymentDetail['pd_id']) || $paymentDetail['pd_id'] == null) {
                    $paymentDetailRecord = PaymentDetailsForExpenseDebitNoteTransaction::create($paymentDetailData);

                    $receiptTransaction = ReceiptTransaction::create([
                        'company_id' => $expenseDebitNoteTransaction->company_id,
                        'date' => $paymentDetailData['date'],
                        'receipt_number' => 'expense-debit-note/'.$expenseDebitNoteTransaction->voucher_number,
                        'is_default_created_by_transaction' => true,
                        'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                        'ledger_id' => $expenseDebitNoteTransaction->supplier_id,
                        'total_received_amount' => $paymentDetailData['amount'],
                        'narration' => $expenseDebitNoteTransaction->narration ?? null,
                        'payment_mode' => $paymentDetailData['mode'],
                        'created_by' => $expenseDebitNoteTransaction->created_by,
                    ]);

                    ReceiptTransactionItem::create([
                        'rc_transaction_id' => $receiptTransaction->id,
                        'expense_debit_id' => $expenseDebitNoteTransaction->id,
                        'invoice_number' => $expenseDebitNoteTransaction->voucher_number,
                        'bill_type' => ReceiptTransaction::EXPENSE_DEBIT_NOTE_BILL_TYPE,
                        'received_amount' => $paymentDetailData['amount'],
                        'discount' => 0,
                        'round_off' => 0,
                    ]);
                } else {
                    $paymentDetailRecord = PaymentDetailsForExpenseDebitNoteTransaction::where('id', $paymentDetail['pd_id'])->first();

                    if (! empty($paymentDetailRecord)) {
                        $paymentDetailRecord->update($paymentDetailData);
                    }

                    $receiptTransaction = ReceiptTransaction::where('receipt_number', 'expense-debit-note/'.$this->oldExpenseDebitNoteTransaction->voucher_number)
                        ->where('ledger_id', $this->oldExpenseDebitNoteTransaction->supplier_id)
                        ->whereId($paymentDetailRecord->receipt_id)
                        ->first();

                    if (! empty($receiptTransaction)) {
                        $receiptTransaction->update([
                            'receipt_number' => 'expense-debit-note/'.$expenseDebitNoteTransaction->voucher_number,
                            'date' => $paymentDetailData['date'],
                            'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                            'ledger_id' => $expenseDebitNoteTransaction->supplier_id,
                            'total_received_amount' => $paymentDetailData['amount'],
                            'narration' => $expenseDebitNoteTransaction->narration ?? null,
                            'payment_mode' => $paymentDetailData['mode'],
                            'reference_number' => $paymentDetailData['reference_no'],
                        ]);

                        $receiptTransactionItem = ReceiptTransactionItem::where('rc_transaction_id', $receiptTransaction->id)->first();
                        if (! empty($receiptTransactionItem)) {
                            $receiptTransactionItem->update([
                                'invoice_number' => $expenseDebitNoteTransaction->voucher_number,
                                'received_amount' => $paymentDetailData['amount'],
                            ]);
                        }
                    }
                }

                $paymentDetailRecord->update([
                    'receipt_id' => $receiptTransaction->id,
                ]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function preparePaymentDetailData($paymentDetail, $expenseDebitNoteTransaction)
    {
        return [
            'expense_debit_note_id' => $expenseDebitNoteTransaction->id,
            'ledger_id' => $paymentDetail['pd_ledger_id'],
            'date' => Carbon::parse($paymentDetail['pd_date']),
            'amount' => $paymentDetail['pd_amount'],
            'mode' => $paymentDetail['pd_mode'] ?? null,
            'reference_no' => $paymentDetail['pd_reference_number'] ?? null,
        ];
    }
}
